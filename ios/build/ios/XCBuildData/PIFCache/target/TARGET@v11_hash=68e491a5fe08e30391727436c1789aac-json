{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e5c242f9d43c715891cf00355a1be06b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-prefix.pch", "INFOPLIST_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleMapsUtils", "PRODUCT_NAME": "GoogleMapsUtils", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982adec9daa7bd199b0a014807d3b66424", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eaa72d21ae76be6b7a78aace6e9f2043", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-prefix.pch", "INFOPLIST_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils.modulemap", "PRODUCT_MODULE_NAME": "GoogleMapsUtils", "PRODUCT_NAME": "GoogleMapsUtils", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980c55bee7d8949d2da8233ccc1f9bd470", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eaa72d21ae76be6b7a78aace6e9f2043", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-prefix.pch", "INFOPLIST_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils.modulemap", "PRODUCT_MODULE_NAME": "GoogleMapsUtils", "PRODUCT_NAME": "GoogleMapsUtils", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987b2fe738fb998344e72c28b73646bba9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981e7c1b09ae5e46db14c5de6e3807e679", "guid": "bfdfe7dc352907fc980b868725387e98806d577bad2016bb80bbae4622f5b0f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a8ea35b0b62ccecc6fdfdf7dfeac20f", "guid": "bfdfe7dc352907fc980b868725387e98b52aeddf01615b933ea3f7b1301bd5d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f58211dc18512670e7ef4d70da46d363", "guid": "bfdfe7dc352907fc980b868725387e981f0c4ee31df4effca09f018d090901d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f62ae2ba7f5a31d9ea2be19e5cba9c0", "guid": "bfdfe7dc352907fc980b868725387e984bd29d1c5424203a0a8dbbe89a2d5b70", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c30c8f95a5a9d97fa6da627919f78ce", "guid": "bfdfe7dc352907fc980b868725387e9820a253d24d61cc6afe2ec71ac6f165ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810cf7affa0ce096d2057c6c2385bcd51", "guid": "bfdfe7dc352907fc980b868725387e98c024b990787ab9cb3e90a4dbc9ac4978", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858a261ec20626fbb670fd868bed6781b", "guid": "bfdfe7dc352907fc980b868725387e98066c2c1add0e89de57b36d0e29a1e87d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f90024cf1486f2252126b17feba00589", "guid": "bfdfe7dc352907fc980b868725387e98631feeb5266786d1c3bb0a55d76fda01", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bdf1fff1df4720e39ad25e7f57cb5b2", "guid": "bfdfe7dc352907fc980b868725387e98ada9d39588245fa6085f8cc6535f4d60", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a000d295790c0e9984bcbfc7e55cc747", "guid": "bfdfe7dc352907fc980b868725387e985452e4e134f034492bcb006d6682ac5b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f976cb478712cd5949ac03b74565678", "guid": "bfdfe7dc352907fc980b868725387e984e6964ce4cb18daa9e642463bde705cd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e044a8333dc8682676c2978753ec8de", "guid": "bfdfe7dc352907fc980b868725387e982854599d643bc39e9b12d34d7dae1e7c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0b4b9155d7b571e2b5869cbefdedeff", "guid": "bfdfe7dc352907fc980b868725387e9805adfe8c34e3f169ad5498ce4abbd97b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98769a806dc2b6f7290ff24b93fd82c79b", "guid": "bfdfe7dc352907fc980b868725387e98302a5b19e7005d8592622a5cff8f95a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98535060f6eea6361ceaab2fbd362213f2", "guid": "bfdfe7dc352907fc980b868725387e984e5147a47a180cfcab2f0bf6b4426416", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98422503fabbc2f0006d2059eb314f90e2", "guid": "bfdfe7dc352907fc980b868725387e98188c3d9573569928f5ee9f15726c3908", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98314a73a20aa16648a20abda5c79ac2a1", "guid": "bfdfe7dc352907fc980b868725387e9868db6b56461e051eaa26c19b11a2a643", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852be10b7c2ba76a3e5b6e261f6fca1ba", "guid": "bfdfe7dc352907fc980b868725387e9831eab19b1b7473bb88ed5bc194390bef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b802edb0d75bcafcb50c5ebbaef094e4", "guid": "bfdfe7dc352907fc980b868725387e981128dcb7a549845e4bc592abeecc459d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a52a51a89c0108592d5cb67b50280f6", "guid": "bfdfe7dc352907fc980b868725387e984fc6c0daa1b25e893c4ecec825a42e9b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c055eafb48dcfe15cf300e857908e2cc", "guid": "bfdfe7dc352907fc980b868725387e9858d41b3c378c78b69f61ac72915fcb83", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988112249a38fdba3de00a6b36a9162039", "guid": "bfdfe7dc352907fc980b868725387e9822e8770adc4cf059bdf5b3f4dbfe42ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3a25e2c9f085685370786a3a9e9e493", "guid": "bfdfe7dc352907fc980b868725387e9811cd6b2970e9b422b995a69528369665", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803716933175d7069bd24527931086f5a", "guid": "bfdfe7dc352907fc980b868725387e98cdc65435808a446c0643c07c34f59585", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d50222a38d96e9fdbc946557a61f2f0", "guid": "bfdfe7dc352907fc980b868725387e9894f3a21cca31e1a09fcdafeaea72ab80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a056b1d9aa1ec46f11b3d9bec96121e", "guid": "bfdfe7dc352907fc980b868725387e98082cf411fcbe74fb23a61afd9bedb1a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98780066f0894a56a14d978757bfb2ef04", "guid": "bfdfe7dc352907fc980b868725387e98d54e69fad7910d525524d112c6e7d2dc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d2cafeb4da8f944d6059bd64e5897dc", "guid": "bfdfe7dc352907fc980b868725387e98f57c60869a755422a10504d247af146a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98091c49709bcbf2ffaf8897df21adadbe", "guid": "bfdfe7dc352907fc980b868725387e983a0f7028169cc406eb03bc300345650a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b14905b901ebcd999f70558ff33e7d1a", "guid": "bfdfe7dc352907fc980b868725387e983fe22d2ac352d8046628d96476337160", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de570104154e7e611989ba496e649b37", "guid": "bfdfe7dc352907fc980b868725387e98448bfacde8c2dc67d9c1534e20f44fd0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da2924e605db660fe336942eed047922", "guid": "bfdfe7dc352907fc980b868725387e98ba2f9a3057cc1f7dcd22cb70ddd79daa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98638c396db638ae77b46a2aa5146faabf", "guid": "bfdfe7dc352907fc980b868725387e98cdaaf6ba907e44c7b82a0401bbde2a98", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f37e7e5f6e2a9e5e734f75542c3a6f94", "guid": "bfdfe7dc352907fc980b868725387e987c36f34a5cd3f35d8c57c8f7eb043ba8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98414209216ba2ce85df1c9e37c11fb22c", "guid": "bfdfe7dc352907fc980b868725387e98b4295129df590939e25c137f47ccf1f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d4bdf68471e063846bc5f6090bc8d4d", "guid": "bfdfe7dc352907fc980b868725387e988abed2dc3193f070f0cf1a3fba2fdd92", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989438f1a01552901dc42578a2802e4315", "guid": "bfdfe7dc352907fc980b868725387e98ada10369131e74d1b788a33f7dcd6e12", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ccfe720a23901074a96fde164b4cd67", "guid": "bfdfe7dc352907fc980b868725387e98f9df3cd61852b428dfa680bd35ea754b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd4bc4f51c68ddeac18c41f4203c4305", "guid": "bfdfe7dc352907fc980b868725387e9898b212dc8787ce3d4b7fd6a87a0a4415", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d17aa25d8ad828631a027912df610a9", "guid": "bfdfe7dc352907fc980b868725387e98b6cb90ce781b07e5914863ad71fa8377", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e05d64e09afde40d726fddcbbaafc1c", "guid": "bfdfe7dc352907fc980b868725387e98a89e2b5db28b4c2e870a39ff023e4205", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c28cd0069f24598b5aaa877eb1269dc", "guid": "bfdfe7dc352907fc980b868725387e9853c68b4efc105e2de2e7d3ceb51e9aac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805a0a77a40efe4216006e7cf4047acf1", "guid": "bfdfe7dc352907fc980b868725387e98a8473b3693a6ee75ede363a092b64add", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98021252aafdd7cc1b5849ce792841af8e", "guid": "bfdfe7dc352907fc980b868725387e98f170c1345a02f6fa218d9153e7fc8b08", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98134c4eb660389b9dc9c08d735ccb57c8", "guid": "bfdfe7dc352907fc980b868725387e989d2461733f5d7437ce547fb0f0bd4558", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c5ea82cf2eb447a4cc330f2f71553653", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984a69309512a03959f1d6be51c19ee60a", "guid": "bfdfe7dc352907fc980b868725387e98b33f86cb67045f508cf2ace5a98d6bec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98901cbbf9ba44b7b619dc686e83038618", "guid": "bfdfe7dc352907fc980b868725387e9816b1bb8c09b939ba28fdce5a0b98a268"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6ccc040670b7abf03f784822dc8b908", "guid": "bfdfe7dc352907fc980b868725387e9820dc39a5d544b98fd5e56724280ccf59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dba1a0f8d6d731d50286e418b788a81f", "guid": "bfdfe7dc352907fc980b868725387e987fea851d30d2e76ca37fe8cb655f6cf5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdb7197a185fea083607b0e7d41b44d5", "guid": "bfdfe7dc352907fc980b868725387e983e7d9aed5ea8dd70a745196c3fde312a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba6bb2222470efc28dcc3fbafcf828eb", "guid": "bfdfe7dc352907fc980b868725387e98cc890457a2f6ee0481b12d5821c2f63c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d27840b28102a2049ffffc5d1099f222", "guid": "bfdfe7dc352907fc980b868725387e98a2cfa2a86b5160ed3ab4b0751e230e44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ada760103e17eef3b7065547cdf4263", "guid": "bfdfe7dc352907fc980b868725387e987449a7d3d6711189c31322c4a363ddba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d76f0bf7680fadcec5e452d0507fddea", "guid": "bfdfe7dc352907fc980b868725387e984560ced14dd67a0b0b0aa9351d88659d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828bcaff60b30fc61d7cdaaec4dc306da", "guid": "bfdfe7dc352907fc980b868725387e98205d03e4651a24e7b82c9efed3955094"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9d5a78cdbb777220317c57cb1ff9f4d", "guid": "bfdfe7dc352907fc980b868725387e98d07d7f42027750948251986290dc286c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e97153746f8aa22d42907eeeec134893", "guid": "bfdfe7dc352907fc980b868725387e980aef27fc8b4247a5df43db05e928558b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d08aec06e1ef981e1b703e13fbb5c62", "guid": "bfdfe7dc352907fc980b868725387e98f27d34ee038512bf48712b585743a4c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1b0f642bb88d4e5d5769b772db285c2", "guid": "bfdfe7dc352907fc980b868725387e980eb550bcc03a690ffee730145a3883d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849caa786ce6f34df26d3c8c19fda8fbc", "guid": "bfdfe7dc352907fc980b868725387e98be83f66df92c6bf6fd4bd75dd680b3c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858c4cd1687a954e2bc7448aa7a73b0f2", "guid": "bfdfe7dc352907fc980b868725387e98616d57624f2ddab6779ec3118a0b11aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f081a4025a0f94e5585d26b7a6ad428a", "guid": "bfdfe7dc352907fc980b868725387e98010d359cd88ba87b6ae74f76583700f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830d5966b00840ceb150684bbab7d5ea1", "guid": "bfdfe7dc352907fc980b868725387e981b38d5d55fde4d9fe1fde443b8a34c4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a3ecf75e802edc2e2d97425641c237c", "guid": "bfdfe7dc352907fc980b868725387e9814865e40f3a6969bcf218e4f80cae81f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae195065308c30fcc306c62d083c31f9", "guid": "bfdfe7dc352907fc980b868725387e9897b970cd6c69b01f4c1406906297cc16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebcee585686c230c3f326f3eebd196dc", "guid": "bfdfe7dc352907fc980b868725387e98ca499965967e8cbdafb5206b2bf3caa4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa629d9720a3f4cfaf2cfd0be22542e9", "guid": "bfdfe7dc352907fc980b868725387e9845230914e97a9b04dc0fc28658e2bd67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e81777e329eb7f885c2a9cfcd505858", "guid": "bfdfe7dc352907fc980b868725387e9894e802b6dfc20de05da7c8b6f92afa12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805214e028cbcc2314bb185d1c3c275ad", "guid": "bfdfe7dc352907fc980b868725387e9850112d6b0263163250ac7e0216ee460c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cf1d3d9bd8c7cffafdedc609211666f", "guid": "bfdfe7dc352907fc980b868725387e98ae312ffcb316a9955768bdc4ea82df1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892eb3a0add35f091f0d16601a8a6e7dc", "guid": "bfdfe7dc352907fc980b868725387e983275b241aa919477874218fd448bffb9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cae6bfd8f3a0a53806c058f1db3b906a", "guid": "bfdfe7dc352907fc980b868725387e98b30b6825f205b76836fb475ed9a4fc8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887779274b58780ce256828d705afdbbe", "guid": "bfdfe7dc352907fc980b868725387e98002743e5ff7bb69a9944eea5373b7602"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b54d979ffcc8e2f8c7f71923fa731b5", "guid": "bfdfe7dc352907fc980b868725387e98314c0bde4525ecf85bbcd48043b723f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bac80349c40a78a70eeee319032a0979", "guid": "bfdfe7dc352907fc980b868725387e9834227b514fcdfb509a53694ce72ab88e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b22b8b182befa415d4a059ad0306758", "guid": "bfdfe7dc352907fc980b868725387e980f24297b47a67a8153c561ca8afa8397"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988aa51b4a4c1dff6942d6f8b9cb132592", "guid": "bfdfe7dc352907fc980b868725387e982237441783b6acbcdd3fb1dc435f04d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863060f6e48be22d56b092954ecab1812", "guid": "bfdfe7dc352907fc980b868725387e989d1770b9a2f6b37f93a514cb958e29ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98443ed29d717bc1c2c99da16764eeee39", "guid": "bfdfe7dc352907fc980b868725387e98327d8d630b70bdfd6daa649feec67bc2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ff11ac1aa3c5c03988b5b036ec63406", "guid": "bfdfe7dc352907fc980b868725387e983ee5b469a86f27b8e2c7aca3d4570043"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a36542ef1b10703cbfc1e9313309602", "guid": "bfdfe7dc352907fc980b868725387e9878c1eccc3cc4fbc3910550194fe42f0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbf297421967ed10166a14c092500f59", "guid": "bfdfe7dc352907fc980b868725387e9878a216d321f64171005b4a39d23418fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815beaa5b8fe359b10bba74608804a7a8", "guid": "bfdfe7dc352907fc980b868725387e98221fdd9b392796c927bbb85166767248"}], "guid": "bfdfe7dc352907fc980b868725387e98a4aa9c79cd0ad589e5986c6a793c7569", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e982f53c31d49d3039187aca0202c785aa8"}], "guid": "bfdfe7dc352907fc980b868725387e985a87658fd90a1d7db61dbb5b1b82aefa", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9811a6ce420babe3491436f103a769ee18", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}], "guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988d4f056f23e4e16df108000d3c5e64e7", "name": "GoogleMapsUtils.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}