{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986ef5a0efbd98caacdc16f79219b53876", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98af1bc1b451b01740b95dbcd093fb7566", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d41b7201d2ae44dd02b095f215fdf2d1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9802caa8dbeb46a3809f8bfd000b0db47e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d41b7201d2ae44dd02b095f215fdf2d1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983593fd0a2cc238a2ef8b60d99fca30e6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98de9f1af232ef0679d56ed6122a67c1c0", "guid": "bfdfe7dc352907fc980b868725387e98b515075b9abb4935d57daef63119973a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838a3c744d6f6881e23f594f2f0fb91da", "guid": "bfdfe7dc352907fc980b868725387e98b02edc161d4432a7d3614754ea92e9e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e178cc83efa17ef9163ca06407ae2d5", "guid": "bfdfe7dc352907fc980b868725387e98ec10b47c85a748a2a358029097eb215d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985280181202aa69a5c09053cbdf75bd4c", "guid": "bfdfe7dc352907fc980b868725387e98cf6b096c3273783d458b35b502537113", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98932072efa32e38942e002ef4398f7d25", "guid": "bfdfe7dc352907fc980b868725387e987955e24fb95eae13ce1f0f9867cd931a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc41563a83abc108de52df55aa25444a", "guid": "bfdfe7dc352907fc980b868725387e9826f83fd259b8b4957843034a0f4d4b7a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4db39b24709c538b2404cf91419edb1", "guid": "bfdfe7dc352907fc980b868725387e983418fbc266b1ddc4b8385d394158fba2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988744e59c2a8eafd382fb2d8181361f8e", "guid": "bfdfe7dc352907fc980b868725387e98ef5fea6ab1d791ce57e4a26bddc55e70", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a2c8dbd3d28c31ab947737f69c874c8", "guid": "bfdfe7dc352907fc980b868725387e98499ad9412d024eb4790a8eabf6cac224", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867b771f75eb7deb0f3217f10d99fa3e8", "guid": "bfdfe7dc352907fc980b868725387e98625f8e2dfad1554107b6910be654cbb1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c944f4cc42fbca97fb49026be662acae", "guid": "bfdfe7dc352907fc980b868725387e9819c265e1ffa600f96ce9d31c07cac176", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98f3d41918a998ff3da3ed37ff74ecc0dc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983082830d1ca9bfde13594e9d70b9bab9", "guid": "bfdfe7dc352907fc980b868725387e98e0f34e34645f83dd782b377ce8f8bfe1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2c559b519dbfb347093eb7912625696", "guid": "bfdfe7dc352907fc980b868725387e98ca9a799f0f573f825edc5dce08fe34c5"}], "guid": "bfdfe7dc352907fc980b868725387e987de00d2e6f004c4f2ef85ca2d246a28d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e98f312d9bcd76174ea6a788f297172359f"}], "guid": "bfdfe7dc352907fc980b868725387e98fa064532d948684065ca0e7e23c8c358", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9832c947e92b0fa0d863d36a96615de45e", "targetReference": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881"}], "guid": "bfdfe7dc352907fc980b868725387e981be2b3c24ea7d9658d7dc46949631f33", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881", "name": "FirebaseCoreExtension-FirebaseCoreExtension_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98311e6292af5af43c801705cd189cc184", "name": "FirebaseCoreExtension.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}