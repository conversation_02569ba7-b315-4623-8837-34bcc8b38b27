{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983d1fe2b5182b7c635c72eba29b0db068", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986e4ecde2f78a7c21c226c10341fddc17", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98934680c067fba9ab181474c46bd46404", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98548705a31ca9ceb496fd10288e83b20d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98934680c067fba9ab181474c46bd46404", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985d9452ae3bf8512adde331510ed8f669", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9829777c0082f5fecad2929a264b1c14e4", "guid": "bfdfe7dc352907fc980b868725387e98d6b2098b54ac24c7c8ecb4c93a89dbe6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e987317e8b3425d3841e92bd3642cc46263", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9815c9062f8d16cae9d36d55205f3a4804", "guid": "bfdfe7dc352907fc980b868725387e98de44ad17d67894017f0719c5193a529a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d9ee2d4b50022e3fa895d916d5de2ab", "guid": "bfdfe7dc352907fc980b868725387e98d3b95b1a96bc9c70cebf86e0efd136dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98333657136ea939395f3aba81dd1d3e6a", "guid": "bfdfe7dc352907fc980b868725387e98491fabee78289871eca013087cce6e2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98172aeb33aff787a541cee099d46734e0", "guid": "bfdfe7dc352907fc980b868725387e98662f807a642585ed795d8f1d80a8c3f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985434263b21f900ac077b37207992076e", "guid": "bfdfe7dc352907fc980b868725387e98b6fd52926c19d8314f5074781b4ee04d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829e801757dca8f17701517c3b44a22ea", "guid": "bfdfe7dc352907fc980b868725387e98b9335a773c3ce9d86400b7aeb9f8d987"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d812cd00cdca641dbf816fd0ba5632bc", "guid": "bfdfe7dc352907fc980b868725387e98bb74c9e6aa0964d289815443b81e852d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c5f5953fbcc5f2c5fda09aede9b5e40", "guid": "bfdfe7dc352907fc980b868725387e98d6ec76cd198780f8ff08b7ace2c4e3f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1e602927464519fb038881e898d7dca", "guid": "bfdfe7dc352907fc980b868725387e98ef83bb90bd7cbad855bba2bd5764bcbc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808695a2419f8620ed937eb4cb49586d7", "guid": "bfdfe7dc352907fc980b868725387e98f6b69205258f3b2ea28798d1c01bcde9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e831bee3287edd6f0909a199dea713d", "guid": "bfdfe7dc352907fc980b868725387e984ea7c83650a0776ae28ccf242b495737"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a958744aedb0d452cdad71c61f0ba370", "guid": "bfdfe7dc352907fc980b868725387e9810ea0ca494e2c91ef36bfd96bc4bf76a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c4a9f7480ee592ce358ec5dd6ef0c11", "guid": "bfdfe7dc352907fc980b868725387e98e470e38ea67ce3e3e4f6522e0691aca9"}], "guid": "bfdfe7dc352907fc980b868725387e98958d9f75ee9708b6dbd472f10fb2d521", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e98baef07028e0542820a040b442c2f1cd6"}], "guid": "bfdfe7dc352907fc980b868725387e98f6b92c3eb83c9083ea30c374646498b5", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98eb4d9ca2c1fd0ba27433b43b265151a7", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e981657f398dbbd07ca1c808dd4e8b5c00d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}