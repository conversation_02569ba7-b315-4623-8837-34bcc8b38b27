{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98808990daf40041cf4e6e9da44edd95d9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98708f1c1164576957ffe761d30d5ffdea", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988ebadf9371774b015ab0e1f723aca71f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981c48928655890da43df7b06f76608b00", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988ebadf9371774b015ab0e1f723aca71f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e403d31c69e5bf53bb6a830a5d1e1c68", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9822a90d4ddf8cfacab5a23e93f2d09acb", "guid": "bfdfe7dc352907fc980b868725387e9865ff506f7d79c768b67bdde8411b191f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869174a3e45259bd62dde56a05abb86d8", "guid": "bfdfe7dc352907fc980b868725387e98a8cc2eb0f33233fb4ff333e3ffceadf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b170a11ec8b6a6b47b5707a5e6b0b9a", "guid": "bfdfe7dc352907fc980b868725387e9866e10e227bd1c3f4e80c0288e0d76bc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886cbb737d04ffb27cd0105862a8bbdbd", "guid": "bfdfe7dc352907fc980b868725387e98f1e2c969b9fd1c61e7fcda858f685bd7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839b5508ddbf02685b006b6adc79dc4cc", "guid": "bfdfe7dc352907fc980b868725387e98ba01fb5288f901a69eca65cf6ade9fb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe0fbd016843cfb9e80f37e53d0aca95", "guid": "bfdfe7dc352907fc980b868725387e9806cab781dfb54681712fe397c808e9fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d061846dc6303d4e61069668741b3ed0", "guid": "bfdfe7dc352907fc980b868725387e981a181f1bb96df84fc1d86935a2126e6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2da8ae02c2068b3ddae2574f7215ffc", "guid": "bfdfe7dc352907fc980b868725387e98c8ebd3f67a37f5686b4612b2534e8946", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d28739648a69a69369a4bf557b7eb56d", "guid": "bfdfe7dc352907fc980b868725387e98dcd58b70976790455bde2ee4e8bc609f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a4350567dbdd9db525e6e25860a4e74", "guid": "bfdfe7dc352907fc980b868725387e98ef5b11281fa13ea7eab4e66b459478e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a898ca4623d2fdd36e0cf85bd1d4115", "guid": "bfdfe7dc352907fc980b868725387e98c349389d5cf275f22fa6f2f37e656920"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9293d2561efddacae7225b25b9e3759", "guid": "bfdfe7dc352907fc980b868725387e984c2a760064404743623c233438462200", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98104a18c7b6c5c30e2b2d668f0c95c870", "guid": "bfdfe7dc352907fc980b868725387e9805c3b6992cfdacda8e6c7f9e488bd296"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7043043939daf4c1f21e99fa6e228ac", "guid": "bfdfe7dc352907fc980b868725387e98752701634227bc18540a2b8244813cd8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eca50021e000b12d6b4a7f5abc7a89e7", "guid": "bfdfe7dc352907fc980b868725387e98e6af434d9ca87f1af7802f0cb21e2362"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98830c3e6eb22bb7e06bab33a8fac4c08e", "guid": "bfdfe7dc352907fc980b868725387e98b6f724d203e06b48e8773b23e86573eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dbc1ab3072e3d0e29263cb13d663796", "guid": "bfdfe7dc352907fc980b868725387e982aedc61649e7c4f92f1b8a8e47c4fc23", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d2a1a3b2f6277e11a6d948b1ffed660", "guid": "bfdfe7dc352907fc980b868725387e98ef87a9abfb580a37f64f4b649a45f972"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1e5bfe03556f7bcb6deb71eb25eb703", "guid": "bfdfe7dc352907fc980b868725387e98879f0db4662133714a27a28505360226"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9b6eea529d62334043b2cec0d5f23a2", "guid": "bfdfe7dc352907fc980b868725387e9877dc0a16c562e3c775d9c013e5936cd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b798f073a81dee6be80a805fe0431a94", "guid": "bfdfe7dc352907fc980b868725387e98ff6cd1b63f013e573ff3254568a29117"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bc0b835c3a2e5a89c01e564b365ade7", "guid": "bfdfe7dc352907fc980b868725387e98ba01fb6aa4abd8b645f087f0f9a70ac1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1f1fce57adf5f97c15686a50d92fdfa", "guid": "bfdfe7dc352907fc980b868725387e980d4e56d52b952c09afbef380caad0733"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c864a48056578d8f55034b39ac00f078", "guid": "bfdfe7dc352907fc980b868725387e9883c5e27e65c9ce16fde6ca981d9d6798"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887ad878291e654129218f29b2973c594", "guid": "bfdfe7dc352907fc980b868725387e9802a7b2fe3d6a3cb6dfc5e83d57494c67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a10483a37ad7d5ab869a47e287479751", "guid": "bfdfe7dc352907fc980b868725387e989ac6222768edf1e04e59197176ab57b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825a541d15d38618a22141b266bee2292", "guid": "bfdfe7dc352907fc980b868725387e98c3910e6a09d4fc185b59973dfe6c9d5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d710f8a31f3ea92278e42e86517d987d", "guid": "bfdfe7dc352907fc980b868725387e98b04a46c1c261df5a2cdcd120084fe6df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98733307fbf8a429219e49b78e4b0db5af", "guid": "bfdfe7dc352907fc980b868725387e987c27152c2d8eafb7e050edbc3fd4d116"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989aa238a64ca8437364ce751bde6537ef", "guid": "bfdfe7dc352907fc980b868725387e98a33777e5f9bbf1cb203278f1f9caea59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851a0a0968fe73073cb8ae668eded12a2", "guid": "bfdfe7dc352907fc980b868725387e98650ed578c78bf7ea1729e16b22b9c76a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98112733e93834ac629ee5134f9d4a130d", "guid": "bfdfe7dc352907fc980b868725387e9814cfd3287b2e01ae3d836e97601bae8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812ab39a648a7a748c3c5c2275da4cd0b", "guid": "bfdfe7dc352907fc980b868725387e98f22250adacd8be5e4b07c50609e6248f"}], "guid": "bfdfe7dc352907fc980b868725387e988115ee20b25f61057f7aa6d9c7c0ea3d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988061ee828fb8b8879c8475883edb00f4", "guid": "bfdfe7dc352907fc980b868725387e9876169d7b40b7f9b9ea30e931064c3484"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bf3f4b82b68054ab8ff9fad50ec0eff", "guid": "bfdfe7dc352907fc980b868725387e9816ddfa547fdba859f350b6fb83797882"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c23ffe0d9855d8f5bbb708908445ae0", "guid": "bfdfe7dc352907fc980b868725387e98b1f5b35c61b96c6a46146baf34eb8015"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f584ac5f75ce1830bb51e91316369ee5", "guid": "bfdfe7dc352907fc980b868725387e987a0d67eb91023cb61b28de5db5c9afb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858529b4d45ec61b13322a0fe92e97d57", "guid": "bfdfe7dc352907fc980b868725387e98bac30ae5816334e2d49562f5502c6257"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98532e24a55a261dde864b3e9c65c0ce05", "guid": "bfdfe7dc352907fc980b868725387e9857b10c6ccda4e1a32e2c420311d45f6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bd4f9fba926dd74493a7803625df3ea", "guid": "bfdfe7dc352907fc980b868725387e98eeab001bd163befa12af6944901a23a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98631c3a243bd595ee14846e79176034e2", "guid": "bfdfe7dc352907fc980b868725387e988c2ba2b27ae3d4d2fe0060779f08b855"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb35b915887577ed60aeb73290828875", "guid": "bfdfe7dc352907fc980b868725387e988fbccf650b4841cbfd322b7267b5b8e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a00ef9e1be3a72259ea73ccd7ac49aa9", "guid": "bfdfe7dc352907fc980b868725387e9807d6d07b734aa8ebe19e618b962b4222"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e17c45ea24063aba93a7a1e41cc1b37", "guid": "bfdfe7dc352907fc980b868725387e98b82752067af8573e40686bc8ee490ba4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859d035814ccbc13740c552389d2c6bad", "guid": "bfdfe7dc352907fc980b868725387e9832587cae191785901b4ff6e836be590d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abecb9514668b2e1df7bd87e7c56ed46", "guid": "bfdfe7dc352907fc980b868725387e98329e9b470a8d92dfa2e1286f1f18272d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850095c245fc69563e178c6fe8252d54a", "guid": "bfdfe7dc352907fc980b868725387e98b21a5acb35b0a6e0321aa618e51047e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e8a68f3ce3b2b75679e7ff499361397", "guid": "bfdfe7dc352907fc980b868725387e9883553a0596deb916d38c182982f085fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897ea340b59d43f5f7f2fd547fe393957", "guid": "bfdfe7dc352907fc980b868725387e98a73592e7f5b6b89ecda2ffd79609c5c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fcdcb80699ecb0ffbb3facbdfea4167", "guid": "bfdfe7dc352907fc980b868725387e98fccbdfd5b607af143d68f79f20473877"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b15143fd82e972b375a4154116f82091", "guid": "bfdfe7dc352907fc980b868725387e9851bbd2e0af6f10c80aa72d569c53db05"}], "guid": "bfdfe7dc352907fc980b868725387e98e3d06b0386be18cafb2a5183c76920b3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e98aef59f9d855f71a6a45f852401cc560a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857517583cea0339c53d5dd4c2679b551", "guid": "bfdfe7dc352907fc980b868725387e98369c2c39bf8a60c0bcbda87c2c820b48"}], "guid": "bfdfe7dc352907fc980b868725387e984a934c13106402c774862a9d69594436", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e983d8c02825fc40d53baf49cdd68b0e34a", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98f96e14e243764e85a2583143b9e184c0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}