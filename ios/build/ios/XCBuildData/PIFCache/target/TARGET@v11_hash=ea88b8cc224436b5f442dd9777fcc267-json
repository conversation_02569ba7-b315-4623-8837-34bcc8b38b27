{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9874ac92ef88a6c6d7016e5b72203eae41", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f375e44fcebbb07d1fb5731270a0aeed", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d78a1105f2a2e2225e69e2a2f0f5df23", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c0ab88de60a44a15883f37bb747aa1c3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d78a1105f2a2e2225e69e2a2f0f5df23", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d17611419d8940309ee5ef0328d28e76", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98eaf4217c7e78f5bcf78e79670e4783f1", "guid": "bfdfe7dc352907fc980b868725387e98566fb19dc85ac2fa710484bcc2b66218", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1aa40b20e9e5f9fbcbe8332c5cf03c5", "guid": "bfdfe7dc352907fc980b868725387e98588aac80d4ba09995bb8d1455a047e7b", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e988562e928667b0ee61896988edff20442", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984ff34a00b6f5f99fabc5dfac0c4b7cfb", "guid": "bfdfe7dc352907fc980b868725387e983f03e17115b0c93e847a3031853bc860"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8d71964899b702d4ed55b24bbef8e73", "guid": "bfdfe7dc352907fc980b868725387e983538c642a966c0de3b5a50848b508a25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98221ee1dd00691cc6246fa5d776601b40", "guid": "bfdfe7dc352907fc980b868725387e984091d1d87f7eaace90ea1703bcc60ac0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98442b68b810692664b44831040939624e", "guid": "bfdfe7dc352907fc980b868725387e98491dcfe1181649a30558a678b056ce85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fbdf1edc9e5cb33f292428577bbf522", "guid": "bfdfe7dc352907fc980b868725387e98480d7fb337873ab853c82ecdf67f90a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989434faa3704497caafacd90364891237", "guid": "bfdfe7dc352907fc980b868725387e9821d200e9a539c4361e5d11d2132eaea8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c36934c09baff895d78bfff3077b489", "guid": "bfdfe7dc352907fc980b868725387e98cfa66e58c29d65b7a2f493974234cc39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6f9721db5253505be1d86ad57391789", "guid": "bfdfe7dc352907fc980b868725387e981afac22ebe6df6dda5059eab5d6b6e85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834293d6facc54e47501cc9aba4a7ca01", "guid": "bfdfe7dc352907fc980b868725387e98280d83557f4eb134cfb543ce287ab16a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980de0855c8b4e574db1a98206c9174613", "guid": "bfdfe7dc352907fc980b868725387e987da82499fa0f7d62f99b63baf9d0256c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d55916d1572d17144fbf2becb258d899", "guid": "bfdfe7dc352907fc980b868725387e98cdc207c4ce8f407a911c4590fdeee805"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8a86d8bc59dfd3675a1335e466ebdb8", "guid": "bfdfe7dc352907fc980b868725387e9805106fad929f2ee7d578e92ce3202678"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bca428a2a567d4509878231b6e0ccde5", "guid": "bfdfe7dc352907fc980b868725387e9825ed6b8a13472ede28bd9f90742412a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baf44aa1197a5dc7e7969e9f4be87c2a", "guid": "bfdfe7dc352907fc980b868725387e9823c55d5c367407cf529ad7116d805c66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eada5e3165c52eb226efd2adb146d026", "guid": "bfdfe7dc352907fc980b868725387e983b89478ed01c4240d89deea1d24d2623"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bebcc92d607d90c980752da8f02ef9c", "guid": "bfdfe7dc352907fc980b868725387e98f7e9b2aa1e0bad2a378c68a06c3a4620"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98361a87678a628839e6d49098dc741113", "guid": "bfdfe7dc352907fc980b868725387e984986de31fdcf521088d69f2554297af4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9a357b982e65ca13c54e05e85842984", "guid": "bfdfe7dc352907fc980b868725387e98830620e4a57fd020f1f166a0171e52f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b2f04c03c4ec7dc08ffa00751c32680", "guid": "bfdfe7dc352907fc980b868725387e98318634c27b561561d141d6f2eb714061"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98718a3c4100101bf1627da8764d7914a2", "guid": "bfdfe7dc352907fc980b868725387e98d085c3082bc0011355debd1e93583d03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bf47b2892da4fcd6ba95adadcad8c56", "guid": "bfdfe7dc352907fc980b868725387e98f7b633166f017b8c6695207733feed59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ffaa84ca9f3fe1bad84e0064e9c44ba", "guid": "bfdfe7dc352907fc980b868725387e98c6c488c84b1288952b54b61e493bcf96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863c0184f6f5b0980350dcbfe9076ed50", "guid": "bfdfe7dc352907fc980b868725387e98425d200dfb57081ac510917c32b2d329"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fff95023696cfe4382379521e51c915", "guid": "bfdfe7dc352907fc980b868725387e9825e0092fdf2d52f4571f55ded252b040"}], "guid": "bfdfe7dc352907fc980b868725387e9827a46ea785278316d8d17de9bb5ce9b7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e982b7356d669cddd9af80eda95828c537f"}], "guid": "bfdfe7dc352907fc980b868725387e9802744a72d9c8a56aa7510e79e87e87f8", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c56cf5ce26d83630cedceb2c2b04de34", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e988e935c81efc4686179f554b8fe37864a", "name": "FirebaseAuthInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98f1e09b32067e7d86144abdaf0d62fddc", "name": "FirebaseStorage", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9861b2e033fd71c20add064527e8a82b5a", "name": "FirebaseStorage.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}