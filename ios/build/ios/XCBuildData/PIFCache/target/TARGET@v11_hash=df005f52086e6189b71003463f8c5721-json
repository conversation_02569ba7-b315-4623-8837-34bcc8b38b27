{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e26a3f8bbc56026f374da578d6ce94dc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a3633a0f6635d8a57906fb7568ee981e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987990fb312cf445bc0d76e597b3d24c0f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a34e9d2e40dca777b26ae1ad6c86f6bb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987990fb312cf445bc0d76e597b3d24c0f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984d4a89cfbb3aba31572fdc5fa5345aab", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984d1342d4f9b049e0de6ee153621dcdc8", "guid": "bfdfe7dc352907fc980b868725387e98d2286e76771d92378da373540187701d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccb09cfa9e897e2fcd524dd41e031216", "guid": "bfdfe7dc352907fc980b868725387e98044aab640080d97707bfc74b03215f67", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdf7ff768e6d90c259350a436036f41a", "guid": "bfdfe7dc352907fc980b868725387e980c9be6d4d68368eb685b355c220bb18c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e93a752f45d7c6d190b4b2beb2a22351", "guid": "bfdfe7dc352907fc980b868725387e98774e8adacadd1f5310d547927db42229", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b93f4370550dc35399c4b8085482e54b", "guid": "bfdfe7dc352907fc980b868725387e98a011785ddf54a2f24bb0353e2cae1a42", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fdfc96e0ee2db318b9a89467e5c7c3d", "guid": "bfdfe7dc352907fc980b868725387e9867a180c79dd6fbdf4924a48c180df024", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbadb3695f4ae829e02a65706d78fff8", "guid": "bfdfe7dc352907fc980b868725387e9835862fa4f2dc03dba7a6928c0722b314", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98018ecb496bb219cf56fe6625a4df451c", "guid": "bfdfe7dc352907fc980b868725387e98d4ab9ba051378564ac3bf4cfc34b25f8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832bbbfdb8721bccf730769ac623bca23", "guid": "bfdfe7dc352907fc980b868725387e9841266011df10d16224f1539a8c5e97a6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839e8bdeaf80e7e62d083f7b3714fc516", "guid": "bfdfe7dc352907fc980b868725387e986b6c9761090416533a6194052cbaab6f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980929f8a65036c9cc9aca34ffdec04b82", "guid": "bfdfe7dc352907fc980b868725387e984aaa7f7f1efd7251214a9cc4ef9ae407", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98221d9840dd78d141b4fbaa254acb4034", "guid": "bfdfe7dc352907fc980b868725387e98bc464fd5f18d74682ec6f85e48a6e02c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832dadb0d9228fb445ac2da1efc9c5ae4", "guid": "bfdfe7dc352907fc980b868725387e981a20cc3b5616ce65077629f5ab49fe8a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842d452c21c8042c44206df86270795a9", "guid": "bfdfe7dc352907fc980b868725387e98a7d10a2ffb2a18a962e22e0644d67bd2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a55f08c5db9339de2a6e09c59bbdfc68", "guid": "bfdfe7dc352907fc980b868725387e981c5cf3f45c70bbbe298565d513c0d8f0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838c1c66229feda4d75bf4efdf343b6f2", "guid": "bfdfe7dc352907fc980b868725387e986f5ac7b25e969f486947bf08f109617e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988284d6c72de4b91ceee7c5b2c9549bbc", "guid": "bfdfe7dc352907fc980b868725387e98286a4ec5489e592ea7852e564364170b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98250ba60ca4480306d342fb44523cd742", "guid": "bfdfe7dc352907fc980b868725387e98776dd8c4727e7c0072698f8dc689ad54", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985966ccea1e2ab562cb5161b6c1db8f06", "guid": "bfdfe7dc352907fc980b868725387e98555c0dccec6be34a7921ebcbfbc1753e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868a99b316e130684faaea6522327a53b", "guid": "bfdfe7dc352907fc980b868725387e98be8007ac8e50538c426a954159c20741", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98557baedd654f7c79840f1f7ec231383b", "guid": "bfdfe7dc352907fc980b868725387e9893fcb978e0e66f0a1a7669b73acb9475", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5a0d9adc1383d542d909978f236a71c", "guid": "bfdfe7dc352907fc980b868725387e981e5ed94ec86b2d731b40ff479a8e2e37", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c82dedc0d1c6fabe9a7101afd129d40d", "guid": "bfdfe7dc352907fc980b868725387e9894447826d75b654bc82a8843f88e8023", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986396f7b0aa0c77b9c90f94c802499286", "guid": "bfdfe7dc352907fc980b868725387e98bd2b22387df36cabb32410c3885b98b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5bd4e68d24f1aeb6ad7909a9633d834", "guid": "bfdfe7dc352907fc980b868725387e98a8f22a6ce47d4703464c0decb4131375", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833590cc3140920238705a5af39812aa4", "guid": "bfdfe7dc352907fc980b868725387e982376013ed9ab5ec301dc764213105547", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982998bb6d6addf6b58697001a1e56bdf4", "guid": "bfdfe7dc352907fc980b868725387e986a5a7a5f948c5368610c363a8bf48678", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834f506c417ed4d98e7ad59851e75fa12", "guid": "bfdfe7dc352907fc980b868725387e9813d360fae94e86cc945607ed861a08b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aba5f083eb0a1e432ee38ab58d76c767", "guid": "bfdfe7dc352907fc980b868725387e988d4b3462176c101c97ef2a447bf7c557", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ab923284cd71310ed0659fd325cc4ed", "guid": "bfdfe7dc352907fc980b868725387e98af70914caf9c69797a53073197200fb2", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9815a065744d238a574e62dfee59f2ec8e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989d1b02e28b92fe636f4434dfcb289980", "guid": "bfdfe7dc352907fc980b868725387e9839e6e48f81f794f9716b6f747583f59b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860dc3279d7ac5bfb1de7de9acc05f2ab", "guid": "bfdfe7dc352907fc980b868725387e984f6b7f71e1d0bfd0e679f0893f58c157"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837018f9c8d63ad0d5a46daaa909dd231", "guid": "bfdfe7dc352907fc980b868725387e989fa7aa82ca0fa5a7e17c7afe8b06c14b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989956bd057cf650872c762da5f4c1c319", "guid": "bfdfe7dc352907fc980b868725387e9849f7364e5d3cb97cc28b8c5334b7e937"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f566b0eec22bdda3944624e2019acc8", "guid": "bfdfe7dc352907fc980b868725387e989417b0d7373200315dd22aff681c8185"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98429b05093db883f4f339e627ea83a25a", "guid": "bfdfe7dc352907fc980b868725387e98388a6647da8455caac346f8f654ff5eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed569837df8ca493bf34e5da87360897", "guid": "bfdfe7dc352907fc980b868725387e98ae36f5f8c127fe98249cad3950de9eff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989380d2405721836c6dc52ef004f0c0f7", "guid": "bfdfe7dc352907fc980b868725387e984e3cac73e345644a5c99faa7fccd15d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dffc75a11336e8131a955ed4cdf0e6e", "guid": "bfdfe7dc352907fc980b868725387e9871e50694f6b66870b74485ac1b299f3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d25675e20a73b33b0e012767cae3d120", "guid": "bfdfe7dc352907fc980b868725387e98fb45f2150ef662b3e38d1a09de2a654d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831af67b4e16772774ede3e96d7efbc51", "guid": "bfdfe7dc352907fc980b868725387e989656667313c276634c7428d33e8c4a46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98511b2742a527c0734c252107cd586f12", "guid": "bfdfe7dc352907fc980b868725387e9866d48376566be700241780b89fb6e36a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bba232b697de2f4165aaba48d18d52a", "guid": "bfdfe7dc352907fc980b868725387e98932ac65c667135698eb72de251572b44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbde3646463d25a69c650671893dfce1", "guid": "bfdfe7dc352907fc980b868725387e98a4daae51b82c1a3f34b6daf180810283"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ead8a113db180af16bb85ba4508ae9ec", "guid": "bfdfe7dc352907fc980b868725387e986633a4d9e307e224db87adbee7a8ee12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e80ddcdf409cba74536174158f4b9970", "guid": "bfdfe7dc352907fc980b868725387e98c32767119c5895b0a26b0fa4e89bd06c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c52b79c26fe503d3ffaeaa405f6109e", "guid": "bfdfe7dc352907fc980b868725387e98fe212730bb7c4fa0d215fb5f412a6255"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983089de3774d673a6ae510c7456076551", "guid": "bfdfe7dc352907fc980b868725387e98b11ccc9020cce8563c4e42dbc0126d93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879ddab48ecc00edc753fb840dfad7d0d", "guid": "bfdfe7dc352907fc980b868725387e98d92d5f4c106f349515df05be3da6fefd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a1282b8ce1bc70c6533ac560c734061", "guid": "bfdfe7dc352907fc980b868725387e98f0d1ad2b1cf3b4803adfbd3b24e8d594"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98137ee9659dd848931605c6f9541a51de", "guid": "bfdfe7dc352907fc980b868725387e9842a0a5bfc700efbdb30ca40ad58e7707"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcd308e2b74346b1b1b29a12d8ced1ea", "guid": "bfdfe7dc352907fc980b868725387e9876985247375c04e240ba6f37c84ac7b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886cd4801bebcb0949f4edfe2d237df4f", "guid": "bfdfe7dc352907fc980b868725387e988175cf4d70a030392dc53a17cfd7e779"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98063f15c63bbc82061cab350990e4e59e", "guid": "bfdfe7dc352907fc980b868725387e98c2485b1acd4253bf4e8327a1b7df1623"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1ae4ea97a02c8a32413673164965cbb", "guid": "bfdfe7dc352907fc980b868725387e9812e10c0918a47919ff2b52526a63bebd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98478f55332d9c3e15571e197852eacff5", "guid": "bfdfe7dc352907fc980b868725387e98decb52c13b93443d588a458191b01a48"}], "guid": "bfdfe7dc352907fc980b868725387e98b6257c2d0b4ecb51124831d5853e29df", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e9885bd13349f3cfe2e9722ea33506ea35a"}], "guid": "bfdfe7dc352907fc980b868725387e98c3c62d5f0e3966e5d5a4d6ace3b0adeb", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e980b74784d8dadef5d2745cacf31e357da", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e98a38eb7074962369b199e7341a7c77370", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}