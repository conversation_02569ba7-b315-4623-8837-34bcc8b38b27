{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9818aa237cd587bde8ae4b323676c6bb84", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983acee0263ee80cf24be6e20e3fe1d937", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eaf93c85867c4917360c3e752a9d302f", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d3c1f856451a44dd2238bb8e03f629a8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eaf93c85867c4917360c3e752a9d302f", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981496f826cc976bf124c3e2cdf403a9f3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a0f6d8eec615b638b78377a2c554fa7d", "guid": "bfdfe7dc352907fc980b868725387e98abbfd4efaca09e6055216d299bf625d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854d5c9d9db62ced6b63360b067387e12", "guid": "bfdfe7dc352907fc980b868725387e98e2aec7dd9f3d201465e29fb465fb8e0f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872c0606ecaf07ec929690f53d388ac06", "guid": "bfdfe7dc352907fc980b868725387e98a25c786d229527859e12fd0ced0b3d0f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815db40251d7b059fb0bffb8ac9ce6a1f", "guid": "bfdfe7dc352907fc980b868725387e98267adcfd98f3b990315dad4684fde4c6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddafa5f375fcfd35461660498da6cd83", "guid": "bfdfe7dc352907fc980b868725387e98310417c27857a7e7c4aa184d7cbb73a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ea6a75842422389d0f4d856d98b8540", "guid": "bfdfe7dc352907fc980b868725387e981309f23c0894b3eac5657a010108d924", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b4b501412266fa2f6d6f6851b728004", "guid": "bfdfe7dc352907fc980b868725387e98cf27526f5e54d55b916825685025674b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0852ba21e0f856def59ed0f96e9df3c", "guid": "bfdfe7dc352907fc980b868725387e984974aa4b701cc2b56cef344d43faabe8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eceada4d27353b20dd93930f923b6984", "guid": "bfdfe7dc352907fc980b868725387e9814e70df90b2f7727d0ccf9227930eba2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afe4081297900f19f369a538e19bdcfb", "guid": "bfdfe7dc352907fc980b868725387e98c259e90b7046364e8193b783830c9e1c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825f82b7893847cad5f863096c4ea9e17", "guid": "bfdfe7dc352907fc980b868725387e983b804e026b6fcd7e1689736644dd44a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e1dc2ce33a059bcf60ba31474b51975", "guid": "bfdfe7dc352907fc980b868725387e98242531cf8f317a91d61b77cb201ba66c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ece2557f11ac4da6081694de7ed0d6d", "guid": "bfdfe7dc352907fc980b868725387e987d539db53a1e72757694fd04fa4bc080", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c8ab29b05a44a9c388598c9af351656", "guid": "bfdfe7dc352907fc980b868725387e98433798fbe49fdbfa2d27069ff4247834", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5266d7dbb23a569bd9f2beaf3b36984", "guid": "bfdfe7dc352907fc980b868725387e98750c7efa3a1610f75622418ee71a64da", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e432b2250aae8b581ccc0ff1f22dbd5c", "guid": "bfdfe7dc352907fc980b868725387e98585fea7a482957dd8938ef0f2c07fe2a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d1e4499d0cfc3559d08404504b510f8", "guid": "bfdfe7dc352907fc980b868725387e989a3e17096880cdae3815b47694f84697", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98bf564ff1975ff472a8bc2c91f2bfcf9f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a806f1f2b8841c8aec685da0b4a91e15", "guid": "bfdfe7dc352907fc980b868725387e9811b3802d4de6e3b9ab68c536ee2479b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813c9590538940e2ea4ba224abd57e3a6", "guid": "bfdfe7dc352907fc980b868725387e98b4dd9c20c0dcbde910bb3b019582c3d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d812e77bef920340355c8e6251617eb7", "guid": "bfdfe7dc352907fc980b868725387e988eb812e61bcfda73c409373fa4281971"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868f8911215a9a9c23738753e6862f31e", "guid": "bfdfe7dc352907fc980b868725387e98c9f65fa2d52c2926a8026464c7b8fbb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98068b85a6adea4c1db7557c19df162bff", "guid": "bfdfe7dc352907fc980b868725387e98185a926dec138453c5fabdc29d0c9a3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1384f50f15ee63c9fa71ce7e37dbeb6", "guid": "bfdfe7dc352907fc980b868725387e9807dfa6a51e1e6cd2aaf2b33081ce034d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6df5b76f3c55130602984206ccfeac9", "guid": "bfdfe7dc352907fc980b868725387e981716220f2e1d688f1ccd9e02715633c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e13dc0b99a6fdf13931897c1d54fc4d3", "guid": "bfdfe7dc352907fc980b868725387e9844500fef7da4bd9baeccf27e03c9cf3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98295aaaf5ea07aac8ab1a6e8c499daa31", "guid": "bfdfe7dc352907fc980b868725387e98b8aa60603d58f3ba9fb1d254fd9efe70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98581afbf855bee3a27c1197fc271ae7df", "guid": "bfdfe7dc352907fc980b868725387e9850a75619d2509c1becacfa7750f1a485"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f4278c4f07e679b66034b3938e26b79", "guid": "bfdfe7dc352907fc980b868725387e98497248f86e805c0f4495322ec1e2e4a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850c599d60ae23921f7b8d456a808483d", "guid": "bfdfe7dc352907fc980b868725387e98e4ef86031fe1d08038ad5e8d41384f61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee9a7aead3e5a3d04397178c5cd5ef48", "guid": "bfdfe7dc352907fc980b868725387e9866bc8ae498d3aaf8efccaee52c0d0707"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981101a36d0ba66aa8a7292fd2c04a8ca8", "guid": "bfdfe7dc352907fc980b868725387e984a25baa00c768ab36e0447bf83be48df"}], "guid": "bfdfe7dc352907fc980b868725387e981b2b4abaaa263ff11fa795c21ec7f9d3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e9850046155a8d9eab9c34f139cf05eb31e"}], "guid": "bfdfe7dc352907fc980b868725387e9800faf1016cc785048ac6ced913c4cff8", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fc5dfdf9349a221f5258db13a60fd150", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98651b31e0272f628eba1fc39908fbac59", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}