{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986a2fb497203f6b58c80ceabd53e66013", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98daa9664a52c2ae28fb4495fe92f9f6b3", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9878ef1a91039653bc86129e29bc496ea8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98358d6100808d9913b598a3f9bc012a04", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9878ef1a91039653bc86129e29bc496ea8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983bfeafbf46210efe79232aeb4029e4e5", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b9eaa11022e291ed0c70802f7b101614", "guid": "bfdfe7dc352907fc980b868725387e9801f70685717d0d1b2a3f0964ae1fbae5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981daece06128dddea471c7c5bd95697a3", "guid": "bfdfe7dc352907fc980b868725387e986e7f7b17436d3e757cb2fdde3f4f1221", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2e58a82427a8fb9248f5569c49bda04", "guid": "bfdfe7dc352907fc980b868725387e98fbe43b4fdc0326af83620cbc4a4faf2d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898235b31154e8055fe7fc6de542c2058", "guid": "bfdfe7dc352907fc980b868725387e98fb9b86acf2e6b2ddfe5b55e511c08e8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98396a0a8d229fd2dd281ac6d29074290e", "guid": "bfdfe7dc352907fc980b868725387e981708ec8d61430609bc03cc93a2005a84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf3d2ce67e5d456277d9bdabe077e7bb", "guid": "bfdfe7dc352907fc980b868725387e984bd509109ca66eaa6a5f9ae3e9890217", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98faf2736bd8efbf15d0246654dc8db4c1", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987592e3b847a909c943b757dc064dc385", "guid": "bfdfe7dc352907fc980b868725387e9869121c174180531c0ff8b8ee9a7741c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828863be231e4890f364edeb5337d04b2", "guid": "bfdfe7dc352907fc980b868725387e9887228f2be5ad6f619aafd10868d55f8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98969c3d3522003d7e233ffcc0befb9b36", "guid": "bfdfe7dc352907fc980b868725387e98758608ea07852047fbdce9280ea525aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdd1ef3125432054eca5074487237c3b", "guid": "bfdfe7dc352907fc980b868725387e98bfd8f98020ff4e14b0713636e1aef6c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1f8173ac8b59be1b6442e66a723fa86", "guid": "bfdfe7dc352907fc980b868725387e9884db8881fcee7851b6665979d31dfd12"}], "guid": "bfdfe7dc352907fc980b868725387e98c2e274154576d0d9092a3d6f1f81be43", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e9896d49673e0d61ab37124c771c90c7630"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857517583cea0339c53d5dd4c2679b551", "guid": "bfdfe7dc352907fc980b868725387e98ceb15843640eb34483c624ed89367f44"}], "guid": "bfdfe7dc352907fc980b868725387e98ada051400159a2a3f58f8329eb2f8768", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e988a0511d326f2e5201041a98e87d1e899", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}], "guid": "bfdfe7dc352907fc980b868725387e983eb3f4752e59f299f0832ae037302c51", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}