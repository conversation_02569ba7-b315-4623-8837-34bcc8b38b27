{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988f940a857e8a27c8872941902f91d401", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98283eb9555431c0e8d111ec4cb7d50a20", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b1cfb36025bb0f14ad227f124789c66a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984c31992c429eee8157cda1c8b2364e5d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b1cfb36025bb0f14ad227f124789c66a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983d0772d8cf84fea4992ef097b450a70c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98223d8c2b46c1c7c6ca5d2eab2fcc1b4c", "guid": "bfdfe7dc352907fc980b868725387e987dda1a49c5de172aef4c9f5fc73926f0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5f19e279ad482bd192ac1d5f8b03a3f", "guid": "bfdfe7dc352907fc980b868725387e98dd761d4253d3459f09b60806b48cc612", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98087cc7d21cda6b7ce1bb700919978ef1", "guid": "bfdfe7dc352907fc980b868725387e981ff037af1c3e98346e42a1d275993d55", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d01d7633e2bc3a66f5ec244f52adeea", "guid": "bfdfe7dc352907fc980b868725387e98d462bf99b2e49f3fd8f4e554a6b1e8e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e089e78667839374f3b043d278b8d60c", "guid": "bfdfe7dc352907fc980b868725387e98f18adcbbb656c4face3df9de8798587f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0fd65d60851ef49c8e95b086e039969", "guid": "bfdfe7dc352907fc980b868725387e98fe689754fe8d60969ecd7fb5d8f164e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e071b31c5e5b820327a3768581f6b433", "guid": "bfdfe7dc352907fc980b868725387e98d945c9bffcfa9d4d73f24b1b32e3d366", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f580e3f7a7cc63fec29f9e8978d8c16", "guid": "bfdfe7dc352907fc980b868725387e98a42e097dff937234c284ae1993956f18", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809bc196258cd83812680345041289fb4", "guid": "bfdfe7dc352907fc980b868725387e9878c10f0bc9dca550bf6bc3f703d8f906", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c98a4e9a76883ab5631e70d70a923c9", "guid": "bfdfe7dc352907fc980b868725387e98d18db21ca07afd4d82d0004facad79be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847d93353ac6cd7f2013fecc724bda957", "guid": "bfdfe7dc352907fc980b868725387e98e4a0889c5f98969c0c9b7a93d8c6b4f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d89cf2eed0a624c4b0981320ec74f1d", "guid": "bfdfe7dc352907fc980b868725387e982c0ad9a8bda0f804edbca0abb5003395", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e835372aa60a95cb22b4b5a70430fea7", "guid": "bfdfe7dc352907fc980b868725387e982f1819615e5a749b877f73b11de69eeb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b727b50e5ccc0c9d9c0efd93ba77ad72", "guid": "bfdfe7dc352907fc980b868725387e983cedf8a5449413b763735a7b4168eebe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981971dd5ef42ee921f58779e83ff9f1f8", "guid": "bfdfe7dc352907fc980b868725387e98138d152b779ba48bb132675cb61607cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989521b02ae798c3d3c263c2fd19356d28", "guid": "bfdfe7dc352907fc980b868725387e98194dca5d2de861be5623475398863184", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98311ae53efd680ed3932e3c84fabf443b", "guid": "bfdfe7dc352907fc980b868725387e983f03a2acec14b16c1735f4d30c9a5b82", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d7b65d5e606dd6666aaa5280c98c545", "guid": "bfdfe7dc352907fc980b868725387e98ac6d558276337ecb9f5810894fe9c72a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988870745866214aec8d21db8f026ae24e", "guid": "bfdfe7dc352907fc980b868725387e98ded8e61fb694122a5e67cfa634a950b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98119b00e53929fb881f1c52732f69a22e", "guid": "bfdfe7dc352907fc980b868725387e9863b3b234da3eb286df9168de295739d2", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4f20675ad2e8e938e7c82f7932efa1d", "guid": "bfdfe7dc352907fc980b868725387e9836071bb69f1c1a198c1bb1c018904eef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f42fe1b16218cbd4b803b119a1dc1ac6", "guid": "bfdfe7dc352907fc980b868725387e989aa91a57b40df44d4b0a8ef2b273959d", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98ab9315c4d8edd47e25eadc272c85678a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ba83ae89da74d136e104a69eb5c3d771", "guid": "bfdfe7dc352907fc980b868725387e98956a8bb314fc18733418a367f1fe2e28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6826e61eba7c54b2593bc10e1491e2e", "guid": "bfdfe7dc352907fc980b868725387e98fa4b661719dbc145e2a18ecddb60a67a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bbe3d9b8f2942175e411d2cb86c9381", "guid": "bfdfe7dc352907fc980b868725387e987903cca80bc93546906de24402c88ed7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2876383053faecfa5f7da9748637a87", "guid": "bfdfe7dc352907fc980b868725387e98b291bd3273455266093885d227efc9fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e64c0663c7fd5083e4bafd3a4be0475", "guid": "bfdfe7dc352907fc980b868725387e98619bda374fa2a9aba9c1cac18e5c1c01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e226ba45cf38f9398ca7e67e720acac7", "guid": "bfdfe7dc352907fc980b868725387e98eb0eeb737bebaa2dba0207850336860e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980abec90803fe35f4d12cc6db0810e1fa", "guid": "bfdfe7dc352907fc980b868725387e9855f2b47340dbd9f07f7b5a3f606cab21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a450cac7ce6df1dec91b6b872d694ce4", "guid": "bfdfe7dc352907fc980b868725387e985998282aedcfb937672f7740b5d24b12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ff9e396bd7e724cc46041a649b6c1de", "guid": "bfdfe7dc352907fc980b868725387e9826fa0b08370f5b7af067ea58024e0349"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af67ff98c8cd8ef9662b4ca4fe0252c7", "guid": "bfdfe7dc352907fc980b868725387e98cf7e103b60a5bc0b8e4201a2e9858604"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854a3dd00633d99105c1cc6f8c1da2c3b", "guid": "bfdfe7dc352907fc980b868725387e984110261b568402a5e5e31f8989b80893"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6cc35570f5070f408e64904d8f2433a", "guid": "bfdfe7dc352907fc980b868725387e986c7026bc0a8374a5e1d31ce4de14bf00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98479b990979d7fe4473905a1d5bd420ea", "guid": "bfdfe7dc352907fc980b868725387e98d550504aa522742454be8ff668bee711"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba57e81bd240639bccfe81f1bd8c3b95", "guid": "bfdfe7dc352907fc980b868725387e980756da8aa98d4175471f1de03fcd0e10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fecc2131c32a85f06dfb0d8346a29f7", "guid": "bfdfe7dc352907fc980b868725387e98457201b8353f065623486b8f809e6273"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c6553503b8cac2bd1c9ce059c5d3b00", "guid": "bfdfe7dc352907fc980b868725387e98a76b97ff9cc57b6b86c5a5593b45e92c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7045d7918cb6e3eb308c22daed67311", "guid": "bfdfe7dc352907fc980b868725387e98b81cea1b6ecc1bc3b47a7edc12207e6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980590c93c4a57b0bfe23faadf7855906a", "guid": "bfdfe7dc352907fc980b868725387e98958f5d5d6ad50764d251291d1f7c4993"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885b02b2455e2a237db117105da35b687", "guid": "bfdfe7dc352907fc980b868725387e98216dff61672f3377dc319d50649106d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ad86719251e1ff1d7f345962f2f5e1b", "guid": "bfdfe7dc352907fc980b868725387e98b8cfb10ae874b7df32f2cc54b20ca3cd"}], "guid": "bfdfe7dc352907fc980b868725387e98e7cadeea759fe982b1b0fc15d7f262c0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e98f18a8072c3cb13cd86cf12198e691c76"}], "guid": "bfdfe7dc352907fc980b868725387e98e82a2c4479f900eb16c409db970874ec", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e983c3f1b9e7b817dc566bdca93441d8405", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98374da6591be9d254cafc1436e8b347f3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}