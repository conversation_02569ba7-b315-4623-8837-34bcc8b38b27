{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d2b984a7c2f8c95947ebee55da9e0943", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c1cc874a020a479547cea61eca3df2b6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98555c5bbd428a1d7087b098e885f75f21", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d47dd7ba22ca9f0b4fa96fadfb06490e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98555c5bbd428a1d7087b098e885f75f21", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e6188ca4110dd4031a8a69eedacc01eb", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e330b7e54d1f8473cecddb3b67b27a89", "guid": "bfdfe7dc352907fc980b868725387e980fc788749403d02a0a1eeca28a3584cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a556716d3e76675e052fac0bec3db969", "guid": "bfdfe7dc352907fc980b868725387e98f1192caadf98bffa909c4bd5094ac2e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98155ad46ccd073453123df46cd8735136", "guid": "bfdfe7dc352907fc980b868725387e98a7ba597914c4a2439612b4c156938b33", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0c07ba99af2efad8033c899d008255a", "guid": "bfdfe7dc352907fc980b868725387e98c616e891fa14eb53517087b5a2adefdf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8ac1c7c21a9e29d908b54398b570c76", "guid": "bfdfe7dc352907fc980b868725387e9869795bc21d9140a9e36e92cbc6247a3d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc71401a4507efb715095b727e28e36e", "guid": "bfdfe7dc352907fc980b868725387e9871efdb04e083833e502eb623fd36375a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c15522adabc25696df63ef85ae7e349", "guid": "bfdfe7dc352907fc980b868725387e98225246c8dfcd09f7065809f6c512e680", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6be87196d87141dee82d44c9020799c", "guid": "bfdfe7dc352907fc980b868725387e98410a264f0803f5c59e9895e6ae17784b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98414f414014cb43c308ae991849ae1c6d", "guid": "bfdfe7dc352907fc980b868725387e98269abd569b617d017b68f7567b61592c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863d0732e9111473ff25038e826dee34f", "guid": "bfdfe7dc352907fc980b868725387e98dfa67bbf6ffb40c37f3d13d3b583a238", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f703a26b891236bcfc73d691055303f1", "guid": "bfdfe7dc352907fc980b868725387e98ebecc2ac66b3b42728dd994a366268ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988841e8cfd09da21cfe7303f5b739456f", "guid": "bfdfe7dc352907fc980b868725387e98f0b8ad2c84ff2cecf50771b768430004", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845a756f583a072791ec025fea61b6e2c", "guid": "bfdfe7dc352907fc980b868725387e989ec0a2c9b362875ea9e8fdd85ed22d69", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e09ce64903f624005076703f1da391c8", "guid": "bfdfe7dc352907fc980b868725387e98062dc8fd202ac7cd492b9c6868622978", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f1eb5bd68e6a816decd85796a9cdd1e", "guid": "bfdfe7dc352907fc980b868725387e986c2bc73ba1598450b684d5768c897129", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989294d783db2ca19d4f26f53b3592e964", "guid": "bfdfe7dc352907fc980b868725387e98e08ee7a76b47c11e013c557b065f6dab", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982a1d921d86947a34d02991e7a955939a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a65c96209ef6b7408e93863273f9c018", "guid": "bfdfe7dc352907fc980b868725387e987a261c9a77c515f6c78c4b88ca5689a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820e70ea54cd9aa94d0e4f6fb50612926", "guid": "bfdfe7dc352907fc980b868725387e98cf19ab41bd0f697d95063abd3b6a3173"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eebb9889e072bd0503a0c75789f82314", "guid": "bfdfe7dc352907fc980b868725387e983d076e1f40e12c2a0564be8292c9cc4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850bbc8bb1ac830092e30f695e1586d2a", "guid": "bfdfe7dc352907fc980b868725387e986bc5fd829e09798edc192d79de288c40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896d2e2db94ef2bd7db791b24994df9aa", "guid": "bfdfe7dc352907fc980b868725387e980941ecbff8c4c701a030c9a210bc6389"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983af51929b383276fd67440bd3c5e5f99", "guid": "bfdfe7dc352907fc980b868725387e98fafce775ca901497c4dd87958133f44e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e88112cfe265fd1c46809238f7951e6", "guid": "bfdfe7dc352907fc980b868725387e98c59068fa8503cad50b4ec66ba6fb7142"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98799b83f0a83e6b873d0d3a1d117fb41b", "guid": "bfdfe7dc352907fc980b868725387e980202b4a030a6e7fc10f6e9cc18d4c7bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a854bd68bb35c8985e86d269c9f19be", "guid": "bfdfe7dc352907fc980b868725387e98ccbbe80b1e353587dc0ae6fa748880d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3c83c01c1a4860033ba4cea20d0206c", "guid": "bfdfe7dc352907fc980b868725387e98cc743b8106c0d47c63f8f84181b2f40d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d53383a814f6a1d38240b5564422f9c4", "guid": "bfdfe7dc352907fc980b868725387e988d18d715334630543676baaee34400de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98287f321b3f36c246d9bb4677cf6fdf5f", "guid": "bfdfe7dc352907fc980b868725387e98293b8908501e20758f1858696f51f593"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f77e41d7531a2575a0dc7945ca6c6e0e", "guid": "bfdfe7dc352907fc980b868725387e9832c8b1bf7fb6b4ed4d050259ac77b48f"}], "guid": "bfdfe7dc352907fc980b868725387e9888ddff36bfdb8fba1896fffbff290975", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e98e21cb5e95267ac01a60b3f75a1af0e91"}], "guid": "bfdfe7dc352907fc980b868725387e980e1ce268bfb1c82d099ffdb3b17b4474", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e20954141ebd835c2535267f91ccb9ea", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}