{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98808990daf40041cf4e6e9da44edd95d9", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseInstallations", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "FirebaseInstallations", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/ResourceBundle-FirebaseInstallations_Privacy-FirebaseInstallations-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "FirebaseInstallations_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98d5b47c7837644d3ed4f372493f17e69b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988ebadf9371774b015ab0e1f723aca71f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseInstallations", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "FirebaseInstallations", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/ResourceBundle-FirebaseInstallations_Privacy-FirebaseInstallations-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "PRODUCT_NAME": "FirebaseInstallations_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98e58ba39c485fdf728e33218a7b33dc3b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988ebadf9371774b015ab0e1f723aca71f", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseInstallations", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "FirebaseInstallations", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/ResourceBundle-FirebaseInstallations_Privacy-FirebaseInstallations-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "PRODUCT_NAME": "FirebaseInstallations_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98c7fdd6685c34d9ca523f3cbf3f794be3", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98b0a7567bd31c5b2d3080eb6bc6739997", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ec89f7d327e4de5eae8e02e9059dcaa1", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987a0880103b283ae89353523ae1249ab1", "guid": "bfdfe7dc352907fc980b868725387e98b4bdc38f36c89f4f1d7c91338b458c6d"}], "guid": "bfdfe7dc352907fc980b868725387e980bf42432e867a4ae545976005191b0d5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981703d6bed554c9878c28cb40b989a332", "name": "FirebaseInstallations_Privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}