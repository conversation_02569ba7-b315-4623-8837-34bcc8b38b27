{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98460de3573e56889082072f5e54dd4058", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ecf0e7e063e575683d920a44c5f00ff5", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9811ba4d44625e7fb2135e0144ff39fe37", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ea21aec5b3654d2a7a621718e5697042", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9811ba4d44625e7fb2135e0144ff39fe37", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985393e9ee0259ff8768cb075412a14754", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9820f3d1718b77154fe23dbeca9324598f", "guid": "bfdfe7dc352907fc980b868725387e98abc8db93bdc67fd8acd54eea916b704c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879d66c89f79aad0b92277705faf99930", "guid": "bfdfe7dc352907fc980b868725387e98c23af28ad1333cf660d2221cd764c9ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3a474bd0deee2c25125e67e60711aa3", "guid": "bfdfe7dc352907fc980b868725387e98eedbf6eb7e6695f194de53f0836ef4bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a26733af168f508c24540c203248b2d", "guid": "bfdfe7dc352907fc980b868725387e9867bdcf2ea7e23ea2ff2a1b8b21d130ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98877dd6cb1d6958394bd282522a776e04", "guid": "bfdfe7dc352907fc980b868725387e985e3d2b12ec0ee89e48392eac4d7136c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816e20ecbf25cd81bee16a6b7e62e61ef", "guid": "bfdfe7dc352907fc980b868725387e980d6edde214db9f162227120e6c24ce37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987361e3334a07add2b6abb91437175917", "guid": "bfdfe7dc352907fc980b868725387e98121025913c2ae31a2344a2bdad198250"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e2ce3af916587143c300bdb31f52105", "guid": "bfdfe7dc352907fc980b868725387e986b9621ae00271a27d3113d5464242618"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a73c5f605d5256848e3843602f2e5db", "guid": "bfdfe7dc352907fc980b868725387e98195a151b7bd90646a749749445767975", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989edcdcd31f96d7261af2a4af68c01ace", "guid": "bfdfe7dc352907fc980b868725387e98808360613bf0e387f51b3bd0c15868e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f01f289164df019cd0394fbc250da703", "guid": "bfdfe7dc352907fc980b868725387e98678fe2bf12c99c2368636cae7709ce5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e70e539627730acd6ca7ccedb684bfe", "guid": "bfdfe7dc352907fc980b868725387e981b404687f9d7a996e3c3fd3cd1830f3e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839a9ed83798407d730f5c9ebbc78d5f1", "guid": "bfdfe7dc352907fc980b868725387e987cd977005cacdd3d5c292c561e8689a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f3e799327c11faff6c1e5a0044fabd3", "guid": "bfdfe7dc352907fc980b868725387e98d705b9eeb37587511ebe5c3b26b753b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7ffea746e5c2a170c2d9bff475b205c", "guid": "bfdfe7dc352907fc980b868725387e98e075480e0b448ee9c81ba2e79758525e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2546c3a04629aa08167bbd18618e30a", "guid": "bfdfe7dc352907fc980b868725387e98f0d78133584a7a427d8af9f93f9f9477"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98703ce03e236b8f99a43b52f8ed5ae032", "guid": "bfdfe7dc352907fc980b868725387e9856f0782573f6fedbc0ab9a33d23b6ec0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a87ed95b433fbca6354ce51450a717f", "guid": "bfdfe7dc352907fc980b868725387e989aeed633f8886245aad86c9f7f02195a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ff515050813ead69ee433f1dacad7a7", "guid": "bfdfe7dc352907fc980b868725387e984655950b88f8030eb28627ad5879c0b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b485bf57fe7e3eab31ebaec63fa60a5", "guid": "bfdfe7dc352907fc980b868725387e98386103d69c777d6de32d68a4910fc34f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb29052c1719ba0f6352ae3045f88dd2", "guid": "bfdfe7dc352907fc980b868725387e9855a3fc7bc3254815f7611078926708e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b42ea337d92bcd6299b2a9260d35ecd2", "guid": "bfdfe7dc352907fc980b868725387e98886549e958d0bd00c95aa56525b1d66a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b14707ac684676d7faacb876b0206494", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98436f045ba41048a007e9205815fffb64", "guid": "bfdfe7dc352907fc980b868725387e9885107d619d2a374ffaff0ae48a793bbb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98374a51520e42800b8a54f0e4d4e0c475", "guid": "bfdfe7dc352907fc980b868725387e987f33239f85fb3b0d005bba8833008a49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98590587e6477fd44a00a230c9b2f346e3", "guid": "bfdfe7dc352907fc980b868725387e981c9830ad2f49554d78515bb452a1cab5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982051c6910ed6d5c4570bef7d538cec68", "guid": "bfdfe7dc352907fc980b868725387e988f3842541022f97658d807e6a2b7bc41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986154da51953762ebe2d814e125970ac9", "guid": "bfdfe7dc352907fc980b868725387e984cc23ce235677890cb9de4ce3f3b6358"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d16e7c14d9291bcacf2a0249ce02f79", "guid": "bfdfe7dc352907fc980b868725387e988f4ee27968156c9f0d88fef9fd516de0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b75f4dcd9c394f7e3ea12bb38862e4c", "guid": "bfdfe7dc352907fc980b868725387e98b7d0ec32a2d06cb30ea8e32a708b9b56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c2973ff3c6aa101ad7d48255bd56bf4", "guid": "bfdfe7dc352907fc980b868725387e98cc629d4897bf24af2a5d8aa6984de13c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9d12754654c48b6c1fa755cce640caf", "guid": "bfdfe7dc352907fc980b868725387e98a7bcc7768745c01d4ed07c75398220cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eda56032b25f4d64f9302bf651a8efa8", "guid": "bfdfe7dc352907fc980b868725387e981d270ed73a6fdedba7735b51be862011"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887d993517746c7cc458ee564c6ca9e5b", "guid": "bfdfe7dc352907fc980b868725387e9819331734209e5a72a70aa723c8c652c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98433e9ce616c3f3d821860aa04c4fddff", "guid": "bfdfe7dc352907fc980b868725387e983ae95321f76a4248987121bb368bb186"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f523071f18b8d1f6219bb64558dd4c8d", "guid": "bfdfe7dc352907fc980b868725387e98bdc7e9276af3e033fd0e5c85b949f6dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985dac67ec8abcd91bf49be6dff04c74e0", "guid": "bfdfe7dc352907fc980b868725387e982b75a800a6234b1939db586162ce5753"}], "guid": "bfdfe7dc352907fc980b868725387e98c6955d9312888afa0b57b64fd8c1f877", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e98d28a7908532c4520e6fe9667fd99c163"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab997f728dfc73763c7be192e3d19493", "guid": "bfdfe7dc352907fc980b868725387e985fb57b91ee9b660ca8296ed52568c03f"}], "guid": "bfdfe7dc352907fc980b868725387e98f7b2617384cd9c05c5d384969deb1625", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d822a832bdd3e30af5940f35092baa69", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98eacf8141649ac6284bc2e70f691c3e24", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}