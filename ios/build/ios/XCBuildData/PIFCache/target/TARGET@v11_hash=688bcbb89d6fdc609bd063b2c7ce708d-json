{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e38068f7ce5ed81f0b22de8ef705a806", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d15420b18834da786f148242969989e7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e7d1d7838de60ad7199fd8ba3bc1987b", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98acf95ee3ab2a6ee729287c0d42603188", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e7d1d7838de60ad7199fd8ba3bc1987b", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b68d75e79f98bc6640329a44a26f353a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d60ee8f5b15c8451e1fcec0c4766625a", "guid": "bfdfe7dc352907fc980b868725387e9899c86dbbf14f32759ead240721b164d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804f1ac3ccaf36656111022471083b47e", "guid": "bfdfe7dc352907fc980b868725387e98a082d5e38d8868ef390965590fb489b7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2e86b996832794b2091c553f43ecf27", "guid": "bfdfe7dc352907fc980b868725387e98f073933ea59a9f6de4019830bd99a383", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865040d1923239dba4ef919d21a7d18a3", "guid": "bfdfe7dc352907fc980b868725387e986fb7a2788d4b5f924d884219fc278b3e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd8aefb76611ce9abbebcdee38e74a3a", "guid": "bfdfe7dc352907fc980b868725387e98511b78e0e8e1d11cc469b40706f4a682", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f058c03f5635554d7a488124186b25c9", "guid": "bfdfe7dc352907fc980b868725387e983c56a451d6853cbf2b1a83729aa47267", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fa0d51dde2845a839fff10f10619d5f", "guid": "bfdfe7dc352907fc980b868725387e98a4f96ead6e87d510aeb4debf0d9e3444", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eadd60808c182e703e620ebccee38b52", "guid": "bfdfe7dc352907fc980b868725387e9851aaecaab283c2d1e21d20b577b97158", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98289e3343de11a0fd010f648919ac4ed6", "guid": "bfdfe7dc352907fc980b868725387e98c9fe3c9a8505277691415bd4db28e91b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cffae844b56be80a822a412bf96883b", "guid": "bfdfe7dc352907fc980b868725387e983afe4849724c33bae767df14fe319ca5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b0676004237d6ce541d323ccd9c009f", "guid": "bfdfe7dc352907fc980b868725387e98c563a3c13203e00fb6617fa9a6ae8daa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ab5aa0f3661a31f5e53368f0bc7410e", "guid": "bfdfe7dc352907fc980b868725387e98813d5bf31863706edd4fe6d96fc3468b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b367decb8df4a98aceb93c412a58915d", "guid": "bfdfe7dc352907fc980b868725387e989e97ad7faf44c970ff1e6892a1c6ff4c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6dcf09ed0270d3ffba764eb4e27b6ff", "guid": "bfdfe7dc352907fc980b868725387e981845e74c3ab02957839c110d24141c4f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989734c1a75c857f0cc102ffc77e362e7a", "guid": "bfdfe7dc352907fc980b868725387e98b31f694c6fb5bdfe4217010f4be1233b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98883298c3b2dd4d3b0dad62ba681614c3", "guid": "bfdfe7dc352907fc980b868725387e98bb1a96e7597c62d9def40782fefeea02", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839d24373e1a91768667f75f4b403dda6", "guid": "bfdfe7dc352907fc980b868725387e989395a1125accffe23244dfbf9de0efe9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98335285ecaabd53414e5ab7c82e69da84", "guid": "bfdfe7dc352907fc980b868725387e98b3ce9fe6067f6fa80f9063a02121687c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fca2f31b5853eb994d9c9e44ad1fe25f", "guid": "bfdfe7dc352907fc980b868725387e9832098e05495ac7e32f62120828010f81", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981216a3035011e866bb693ed56ac0a7de", "guid": "bfdfe7dc352907fc980b868725387e98955bb61d597938c7db2f81e30ba9141c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a52d09e12cdf9e3d60c7aed09804940", "guid": "bfdfe7dc352907fc980b868725387e98788574342f2a1a108076cc7bf5b10e83", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f85628ecdd68cc9c4925eaf875918c1", "guid": "bfdfe7dc352907fc980b868725387e9844687755944ab126b41af2a030ca1113", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865fb4403ed82cff7273ed39234099bc4", "guid": "bfdfe7dc352907fc980b868725387e9885ea9530b0ae71a48333140350a2e9bd", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98d5c8c539c8b21555bcb4eb3367cc806c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9821d6fcc17a97a0c0c98b75be03a5d1aa", "guid": "bfdfe7dc352907fc980b868725387e98d6138f26491167826ad1e7b67f30377e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cac795bb989fa8ad9ff507b3971bb3cd", "guid": "bfdfe7dc352907fc980b868725387e987ad050cb0ea48f3496432d0ca0073ad0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae7f5eec8870ad1c2f0dda4ae9baf8d2", "guid": "bfdfe7dc352907fc980b868725387e9820596f903eabe89bc1b49ff61d6160e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845fc642ffac9b6730401dc9e03faca72", "guid": "bfdfe7dc352907fc980b868725387e98aec5db6be50e2c3d48bcc2d2d26ce288"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0ca1b3e4aa755bf2b120086fb7edbae", "guid": "bfdfe7dc352907fc980b868725387e98b74324fa816fec932ac8150c8092d95c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5599a6dfb090f3d38e7c4a672cd14e8", "guid": "bfdfe7dc352907fc980b868725387e987b714edbe45b1f2cedd6ab2547ba49fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833379d9f5fd8d83ff39c59f9a021e490", "guid": "bfdfe7dc352907fc980b868725387e98b629b35be16949fa21819ab19b9dd484"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3dbfe1141ae2026bd750af89e697245", "guid": "bfdfe7dc352907fc980b868725387e9859c137f54541a356007982ed8ba35951"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861becae1a9cfc026bcf60816f358cfdf", "guid": "bfdfe7dc352907fc980b868725387e9886d512b0d1e136960ef41dd8fe703f1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813b7469e6761cf895e0f93e0a925b4f7", "guid": "bfdfe7dc352907fc980b868725387e98fc5c3848a64297f6848e709189458bda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd28bb6153636606651e165f80e25791", "guid": "bfdfe7dc352907fc980b868725387e98af88d36d14da38aca408d4bbbaabf2ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a14203bdf95a4fbf81fb1647a0ab368", "guid": "bfdfe7dc352907fc980b868725387e98e1826b74efc5b348495fbd4381b80a7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980108e416e5e0d9e84bd0239adebbb188", "guid": "bfdfe7dc352907fc980b868725387e98c0229d70a6a984dd4b305a2b34d61e96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cc1f871387a9dce4ad1ce8ff965a0f3", "guid": "bfdfe7dc352907fc980b868725387e98e027e6eb73fc95cae70db64fa95fdc37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850a264ae4b4a3f7611b400059e7a6b01", "guid": "bfdfe7dc352907fc980b868725387e9830709f907fa9c9390565b4bb37d776aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a9da5f530391bafffadd95b0b664e10", "guid": "bfdfe7dc352907fc980b868725387e9847f81ff8a94aa776eecccaf9151207dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846581af67350376ac6716f508185ea5d", "guid": "bfdfe7dc352907fc980b868725387e984367df2002e8669383e261597374c080"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812693d2b130a030e46a862847a97b2f8", "guid": "bfdfe7dc352907fc980b868725387e98f0b23a014063b8b9f480e7a771d15aff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f75b76c5ac8df5121b068c6a7249f13", "guid": "bfdfe7dc352907fc980b868725387e98a0e1c2ee68ea625406ecc88237cc9fc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890370ea0c9ac7dfa84c7f1ee807640ab", "guid": "bfdfe7dc352907fc980b868725387e98e39187ef85dfdd2adea7ddaed37f4b9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c690dff2b4af1635491723a8c50b6166", "guid": "bfdfe7dc352907fc980b868725387e984db214660267fcd2e6e4f668f64dd8c1"}], "guid": "bfdfe7dc352907fc980b868725387e98057eb75c841554d25f0f5035104e0263", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e980017a4fef110a270efddb901b92f2e25"}], "guid": "bfdfe7dc352907fc980b868725387e9810423e99f15760aeac349670a9357a85", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9862935e1388b76505ac12d06927e9652c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}