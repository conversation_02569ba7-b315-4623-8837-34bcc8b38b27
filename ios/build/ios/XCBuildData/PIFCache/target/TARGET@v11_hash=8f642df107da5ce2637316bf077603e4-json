{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ca2ccce12121701e26ddaf817a05ba6c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98efbf0289064c70cff9716571b4817e90", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987e0bf589db3b98329d350303d11bbfcd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c35e8fd75597be9a3e2a54e9f04cdb40", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987e0bf589db3b98329d350303d11bbfcd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98117766a9e832e77497c56525b774ba88", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cd7cea5bea9de3bff1b6abe162317dd9", "guid": "bfdfe7dc352907fc980b868725387e98979a868ec6cb25a6822a4624f9dc5c78", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af0b6dccddcbad0951900a9f04feae8a", "guid": "bfdfe7dc352907fc980b868725387e9866e47dc4ddc51beb8038770f02fb3f6b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981762f87aa0c5e0ca65405da67c4bc2fa", "guid": "bfdfe7dc352907fc980b868725387e9889cbcdedd0e34ca8b707d5c3cb17699b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845311cb43f0da68b87b9e889ab3edd39", "guid": "bfdfe7dc352907fc980b868725387e981c637377ca23ce3721c4776f7c5bd272", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988526995edc7ab09d86b2ae133bfec32e", "guid": "bfdfe7dc352907fc980b868725387e987b54ad3384601852b154705d15f71977", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837aa4b46c362426a135f3a45a5bd3aa9", "guid": "bfdfe7dc352907fc980b868725387e98fa3e8fb4d8e4cf82fe021a386a4bda61", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d55db8c2b837c91e45724cd3d794cb58", "guid": "bfdfe7dc352907fc980b868725387e9878c3e0c1cb70e01f073e49c3bda073ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98298660294e65e8a20902067c199cab9a", "guid": "bfdfe7dc352907fc980b868725387e9819a75588769ff25effc127d8675c36bf", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab2c1534b7aa1fc43449c342f4b7ff2f", "guid": "bfdfe7dc352907fc980b868725387e98750c552384f440832b46a2f4a0c749a5", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8272acabc79e248f6a432a29826fa9f", "guid": "bfdfe7dc352907fc980b868725387e98cb2d3c1723bf00b5170ce52f46f86162", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98331e2968cd44e7c51d52a00f84619129", "guid": "bfdfe7dc352907fc980b868725387e98a82f82d966721f9bbbb3f8b5ead05067", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806d11b4c74fb0fa1d8442c73d7c29029", "guid": "bfdfe7dc352907fc980b868725387e9823cc6cccdf93696f9a4cceb52d645ee8", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9763d7dcbe338515faa695a2b1c20d3", "guid": "bfdfe7dc352907fc980b868725387e98b79c6001d23cd03d7ac426978696e8c1", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad4f35e93949583f76348a82ed975fea", "guid": "bfdfe7dc352907fc980b868725387e984ebe318b7ae6974ff8d31fa01e04bc6a", "headerVisibility": "private"}], "guid": "bfdfe7dc352907fc980b868725387e98b88ef27fd18bb6e3876c3f43db5901ba", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986a39a801b96f61986fa20370846b329a", "guid": "bfdfe7dc352907fc980b868725387e986c038aa933ee0897ebdb9dceea5baae6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98788fb9cfb4727acba522a15913defb2e", "guid": "bfdfe7dc352907fc980b868725387e9878f6f0638352231350e7d331321bd8ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f0eb5ca9ecf728bcfc18b73a48cd3a1", "guid": "bfdfe7dc352907fc980b868725387e98f6dbbd1f092a9e983d9738bbcd17a680"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba5f7028bb1a66d7af04fbc51c8d70d7", "guid": "bfdfe7dc352907fc980b868725387e98bf863ab2361af9c512a3212ed2d03684"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823295bd41255329b19b452f76d73ad97", "guid": "bfdfe7dc352907fc980b868725387e988994c8b2823109f8f7eecfa288540008"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec551ecb1ea23773f9e04d5799ab0562", "guid": "bfdfe7dc352907fc980b868725387e988b9a94d9ab8dc849032dbed985540005"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870ce4393a0c725d3801e6e1ea4a1fd41", "guid": "bfdfe7dc352907fc980b868725387e9866668df667e223be6d0fb3ad5266fa69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833a58b1b4b976f1f4732d37712fc422d", "guid": "bfdfe7dc352907fc980b868725387e98b0772c8af52bfacd8bfb19affc68066c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98801c0c53f656a3bbf6be49905bb9052e", "guid": "bfdfe7dc352907fc980b868725387e984ff794962ee5902a5ba08819ebe8e32d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848cf838c1ea03d1d16197741c807c2ee", "guid": "bfdfe7dc352907fc980b868725387e985df3026272e2b593161049f94c645bc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980562e9245c1cfad0d73386324962654a", "guid": "bfdfe7dc352907fc980b868725387e981e6c58f55444d4f159cd9b6e2c4bfff2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878a5419dd5888144e79a64a4a03a241a", "guid": "bfdfe7dc352907fc980b868725387e989f8529883c6c5072b781e3ed9e220ad0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984dd5e551abcc1c78c2e8ce57f8736d75", "guid": "bfdfe7dc352907fc980b868725387e9829837dbd1c48b2e3e9084a81c6d3cfc5"}], "guid": "bfdfe7dc352907fc980b868725387e9835f4c8f61e73511fc560fa84bf693430", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e988aaf844137aefd0f7dadaa75c6732ac7"}], "guid": "bfdfe7dc352907fc980b868725387e985af968884e986bae48f258bcd28bb9de", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e980d2794352e93f0efd04427d324195dea", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}