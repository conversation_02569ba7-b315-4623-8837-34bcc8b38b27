{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980be00037d809edc55ae3758d879a670a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98124d05d95b0a88b50ba23971f882cc63", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981cb525867675ceb8a307658bf3f5e30d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985e60d334a0636be678d24ed155f17e68", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981cb525867675ceb8a307658bf3f5e30d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9850968fc64e545db8702a9275a14bb5b9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e545e27da71cb06c5f44f6a6170492af", "guid": "bfdfe7dc352907fc980b868725387e985e35902722fa51253ddf7f2db938da97", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0ec2ba34e119caeb66b566bca3e5916", "guid": "bfdfe7dc352907fc980b868725387e984b99fd778dcd38005317b7b6aeaafe4a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858d4ca4eec6d81a7ce22d4bf21f62582", "guid": "bfdfe7dc352907fc980b868725387e98fdc7c50aa9a6eb5e24c91448d244360f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983639ce343bf2a3bbaa41e0a8055d2565", "guid": "bfdfe7dc352907fc980b868725387e98a1fe1d223ffb40ed37e0397fba903811", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984238b7a0108fd8c71b156072f4649a70", "guid": "bfdfe7dc352907fc980b868725387e985322ec7d5e22648f3fdce19d040b654f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830b9038e330b5a601035b5109d934049", "guid": "bfdfe7dc352907fc980b868725387e98db7af2d642d3dbd22a718973814d3019", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836f3fc102918a3698318cab7f061c8de", "guid": "bfdfe7dc352907fc980b868725387e985f337a66ecc59822b3756542bacb231b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dfb01268eee28681f44adb3d9b3fb2f", "guid": "bfdfe7dc352907fc980b868725387e987461cae5833ac1c6d65b89240699a012", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e922934b9acc4c57a86e386e4eebb9f", "guid": "bfdfe7dc352907fc980b868725387e983441c68b5a5d5eabb837ebf033bf2cb9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a22cc148e8c50831d050f45bd0fcf7ba", "guid": "bfdfe7dc352907fc980b868725387e98789376a6793e03b17b7652b3f6faeea4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826dc3a633f84b2cb246341c684556d4c", "guid": "bfdfe7dc352907fc980b868725387e98ca3b94760499bc64ed2f476a670cf8ef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5b241cfcadc0cec02c13234a3c39fbe", "guid": "bfdfe7dc352907fc980b868725387e98c4c8d0de2b5c292bac828a8aa973393d", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9851198fae0d8905fb79f391da70d27b9a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98233116cfc77ebcbc04ab0dc9bb8a464e", "guid": "bfdfe7dc352907fc980b868725387e9806ca13c36fada04737fab64394e4a672"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3e3764f2f6b57149240b25c28a3b673", "guid": "bfdfe7dc352907fc980b868725387e989d4063eb73cd5b5582d3f70db2a1754e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821e360c848a5d04330fd60ef0c047f1a", "guid": "bfdfe7dc352907fc980b868725387e98658e2a9e7e0973476afac76affd7e540"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d75fd69cd545157ed2033fa20d16901a", "guid": "bfdfe7dc352907fc980b868725387e98140872e3d81bd9bf1e0eaf0fcc2d2504"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d93726a37ce8a35e98fbca276b311d1", "guid": "bfdfe7dc352907fc980b868725387e98cdbd239c298d394ba34708b2bc0c8007"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1e9f5a92b614fcfb34a230643b9810c", "guid": "bfdfe7dc352907fc980b868725387e9848970c4eb270a185774fc56fa23ced53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5c7baabc5131bd958fb61612b40d858", "guid": "bfdfe7dc352907fc980b868725387e986464cd512eec31492ba6f3508e4ebb79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac68902dea0a4f09b4e7f75dcf655f04", "guid": "bfdfe7dc352907fc980b868725387e9831f42f4d0dbbfdd7c88ed51ca703851d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984dff2fee0f76d9f5b8706b68758502a5", "guid": "bfdfe7dc352907fc980b868725387e988dfe7bb6984f377e6172bc446e17e9c0"}], "guid": "bfdfe7dc352907fc980b868725387e98c53cda76c6714cc99d33f8d438858f07", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e98cdd7430e01d92244c5d5c3d4bd77581d"}], "guid": "bfdfe7dc352907fc980b868725387e98e25737a103f495bf6b667e8f6f32d4bd", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9804c93d896fd7e7654fa5c52570948a90", "targetReference": "bfdfe7dc352907fc980b868725387e9894b6f514aa32ee4cfdd7fc11c1ff5321"}], "guid": "bfdfe7dc352907fc980b868725387e9868af60886bbf96f1bb9951e1a108aa20", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9894b6f514aa32ee4cfdd7fc11c1ff5321", "name": "sqflite-sqflite_darwin_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e983786431ce548989b846bbf1a7384f58e", "name": "sqflite", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9892137925c03f59a4fb600ced1a959f92", "name": "sqflite.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}