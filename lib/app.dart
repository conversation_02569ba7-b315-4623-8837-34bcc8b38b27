import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/app_binding.dart';
import 'package:attp_2024/core/configs/theme/app_theme.dart';
import 'package:attp_2024/core/langs/translation_service.dart';
import 'package:attp_2024/core/routes/pages.dart';
import 'package:attp_2024/core/utils/behavior.dart';

class App extends StatelessWidget {
  const App({super.key});

  @override
  Widget build(BuildContext context) {
    return ResponsiveSizer(builder: (context, orientation, deviceType) {
      return GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: GetMaterialApp(
          debugShowCheckedModeBanner: false,
          initialRoute: Pages.initial,
          scrollBehavior: MyBehavior(),
          getPages: Pages.routes,
          initialBinding: AppBinding(),
          locale: LocalizationService.fallbackLocale,
          supportedLocales: LocalizationService.locales,
          localizationsDelegates: LocalizationService.localizationsDelegates,
          fallbackLocale: LocalizationService.fallbackLocale,
          translations: LocalizationService(),
          theme: AppTheme.light,
          darkTheme: AppTheme.dark,
          themeMode: ThemeMode.light,
        ),
      );
    });
  }
}
