import 'package:get/get.dart';
import 'package:attp_2024/core/data/api/configs/dio_configs.dart';
import 'package:attp_2024/core/data/api/services/auth/auth_service.dart';

import 'core/data/api/services/api/api_service.dart';
import 'core/data/api/services/proc/proc_service.dart';
import 'core/data/database/device_data.dart';

class AppBinding extends Bindings {
  @override
  void dependencies() async {
    await Get.putAsync<DioService>(() async {
      final dioService = DioService();
      return await dioService.init();
    });

    Get.lazyPut<AuthService>(() => AuthService(Get.find<DioService>()));
    Get.lazyPut<ProcService>(() => ProcService(Get.find<DioService>()));
    Get.lazyPut(() => DeviceData(Get.find<DioService>()));
    Get.lazyPut(() => APIService(Get.find<DioService>()));
  }
}
