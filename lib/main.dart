import 'dart:io';

import 'package:attp_2024/core/utils/bypassssl.dart';
import 'package:flutter/material.dart';
import 'package:attp_2024/app.dart';
import 'package:attp_2024/app_config.dart';

void main() async {
  await appConfig();
  HttpOverrides.global = MyHttpOverrides();
  runApp(const App()
      // DevicePreview(
      //   enabled: true,
      //   builder: (context) =>const App(),
      // ),
      );
}
