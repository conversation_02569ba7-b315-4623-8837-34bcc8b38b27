import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/data/api/configs/dio_configs.dart';
import 'package:attp_2024/core/data/prefs/prefs.dart';

class ApiHelper {
  final DioService _dioService = Get.find<DioService>();

  Future<String> _getToken() async {
    return await Prefs.preferences.get('token');
  }

  Future<dynamic> postRequest(String url, Map<String, dynamic> body) async {
    String token = await _getToken();
    var options = Options(
      headers: {
        'Authorization': 'Bearer $token',
      },
    );

    try {
      final response =
          await _dioService.dio.post(url, data: body, options: options);
      print("Full Response: ${response.data}"); // In toàn bộ phản hồi
      return _handleResponse(response);
    } catch (e) {
      throw Exception("Error making request: $e");
    }
  }

  dynamic _handleResponse(dynamic response) {
    if (response.data != null && response.data['success'] == true) {
      return response.data;
    } else if (response.data != null) {
      throw Exception(
          "Request failed: ${response.data['message'] ?? 'Unknown error'}");
    } else {
      throw Exception("Request failed: No response data");
    }
  }
}
