import 'package:attp_2024/core/data/models/user_access_model.dart';
import 'package:attp_2024/core/services/user_access_use_case.dart';

class UrlHelper {
  static Future<String> getImageUrl(String imagePath) async {
    // L<PERSON>y danh sách UserAccessModel từ UserAccessUseCase
    List<UserAccessModel> userAccessList =
        await UserAccessUseCase.getUserAccess();

    // Kiểm tra nếu danh sách trống
    if (userAccessList.isEmpty) {
      throw Exception("No UserAccessModel found");
    }

    // L<PERSON>y phần tử đầu tiên
    UserAccessModel userAccessModel = userAccessList.first;

    // Kiểm tra apiUpFile
    if (userAccessModel.apiUpFile.isEmpty) {
      throw Exception("Base URL is not available");
    }

    return '${userAccessModel.apiUpFile}$imagePath';
  }
}
