import 'package:flutter/material.dart';
import 'package:html/parser.dart';

String getText(String input, {String separator = '-', bool before = true}) {
  if (input.isEmpty || !input.contains(separator)) return input.trim();

  List<String> parts = input.split(separator);
  return before ? parts.first.trim() : parts.last.trim();
}

String truncateText(String text, TextStyle textStyle, double maxWidth) {
  final TextPainter textPainter = TextPainter(
    text: TextSpan(text: text, style: textStyle),
    maxLines: 1,
    textDirection: TextDirection.ltr,
  )..layout(maxWidth: maxWidth);

  if (!textPainter.didExceedMaxLines) {
    return text; // Trả về chuỗi gốc nếu không vượt quá 1 hàng
  }

  String truncatedText = text;
  while (textPainter.didExceedMaxLines) {
    truncatedText = truncatedText.substring(0, truncatedText.length - 1);
    textPainter.text = TextSpan(text: '$truncatedText...', style: textStyle);
    textPainter.layout(maxWidth: maxWidth);
  }

  return '$truncatedText...';
}

String convertHtmlToString(String html, String exception) {
  return parse(html).body?.text ?? exception;
}

String converImage(String path) {
  return path.replaceFirst('~', '');
}

// // Chuyển chuỗi hình ảnh * sang danh sách
// List<String> convertImageStringToList(String baseUrl, String inputString) {
//   List<String> paths = inputString.split('*');
//   List<String> fullPaths = paths.map((path) => '$baseUrl$path').toList();

//   return fullPaths;
// }

// Dung fix
List<String> convertImageStringToList(String baseUrl, String inputString) {
  // Tách chuỗi bằng '*'
  List<String> paths = inputString.split('*');

  // Loại bỏ các phần tử trống (nếu có)
  paths = paths.where((path) => path.isNotEmpty).toList();

  // Ghép baseUrl vào từng đường dẫn
  List<String> fullPaths = paths.map((path) => '$baseUrl$path').toList();

  return fullPaths;
}

String removeFileScheme(String url) {
  if (url.startsWith('file:///')) {
    return url.replaceFirst('file:///', '');
  }
  return url;
}

List<String> convertAttachItemStringToList(String inputString) {
  return inputString
      .split('*') // Tách theo dấu *
      .where((url) => url.isNotEmpty) // Lọc bỏ rỗng
      .toList();
}

// Loại bỏ ~ ở đầu và * ở cuối trong đính kèm => list
List<String> convertAttachItemStringToListWithoutSwungDash(
    String baseUrl, String inputString) {
  List<String> paths = inputString.split('*');
  paths = paths
      .where((path) => path.isNotEmpty)
      .map((path) => path.replaceFirst('~/', ''))
      .toList();
  List<String> fullPaths = paths.map((path) => '$baseUrl/$path').toList();
  return fullPaths;
}
