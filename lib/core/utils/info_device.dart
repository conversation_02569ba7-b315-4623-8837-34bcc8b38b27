import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:attp_2024/core/data/dto/response/device_response.dart';
import 'package:uuid/uuid.dart';

class InfoDevice {
  static Future<DeviceResponse> getDeviceData() async {
    final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    const uuid = Uuid();

    String thietBiID = uuid.v4();
    String thietBiCode = "";
    String tenThietBi = "";
    String hangSX = "";
    String platform = "";
    String version = "";
    String loaiThietBi = "";
    if (Platform.isAndroid) {
      final AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      tenThietBi = androidInfo.model;
      thietBiCode = androidInfo.id;
      hangSX = androidInfo.manufacturer;
      platform = "Android";
      version = androidInfo.version.release;
      loaiThietBi = androidInfo.device;
    } else if (Platform.isIOS) {
      final IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      tenThietBi = iosInfo.utsname.machine;
      hangSX = "Apple";
      platform = "iOS";
      version = iosInfo.systemVersion;
      loaiThietBi = iosInfo.model;
    }

    return DeviceResponse(
        thietBiID: thietBiID,
        thietBiCode: thietBiCode,
        tenThietBi: tenThietBi,
        hangSX: hangSX,
        platform: platform,
        version: version,
        loaiThietBi: loaiThietBi);
  }
}
