import 'dart:ui';

/// Convert a hex color string to a [Color] object.
/// If the input is null, empty, or invalid, it returns the default color [Color(0xFFD63939)].
Color hexToColor(String? hexColor,
    {Color defaultColor = const Color(0xFFD63939)}) {
  if (hexColor == null || hexColor.isEmpty) {
    return defaultColor;
  }

  // Remove the '#' if present
  hexColor = hexColor.replaceAll("#", "");

  // Handle short hex formats like "FFF" -> "FFFFFF"
  if (hexColor.length == 3) {
    hexColor = hexColor.split("").map((char) => char * 2).join();
  }

  // Parse the color and add alpha channel if missing
  try {
    return Color(int.parse("FF$hexColor", radix: 16));
  } catch (e) {
    return defaultColor; // Return default color if parsing fails
  }
}
