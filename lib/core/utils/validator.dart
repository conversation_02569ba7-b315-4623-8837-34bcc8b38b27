class Validators {
  Validators._();

  static bool validateEmail(String value) {
    final RegExp emailRegExp =
        RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    return emailRegExp.hasMatch(value);
  }

  static bool validPassword(String password) {
    String pattern = r'^(?=.*?[a-z])(?=.*?[A-Z])(?=.*?[0-9]).{7,20}$';
    RegExp regExp = RegExp(pattern);
    return regExp.hasMatch(password);
  }

  bool isValidPassword(String password) {
    final RegExp passwordRegex =
        RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{7,20}$');
    return passwordRegex.hasMatch(password);
  }

  static bool validPhone(String phone) {
    String pattern =
        r'^(?:[+0]9)?[0-9]{10,12}$'; // <PERSON><PERSON><PERSON> tra từ 10-12 số và có thể bắt đầu với + hoặc 0
    RegExp regExp = RegExp(pattern);
    return regExp.hasMatch(phone);
  }
}
