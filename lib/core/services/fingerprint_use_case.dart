import 'package:attp_2024/core/configs/contanst/prefs_contansts.dart';
import 'package:attp_2024/core/data/prefs/prefs.dart';

class FingerprintUseCase {
  static final Prefs prefs = Prefs();

  static Future<void> turnOnFingerprintAuth() async {
    await prefs.setBool(PrefsConstants.fingerprintAuth, true);
  }

  static Future<void> turnOffFingerprintAuth() async {
    await prefs.setBool(PrefsConstants.fingerprintAuth, false);
  }

  static Future<bool> getSateFingerprintAuth() async {
    try {
      return await prefs.get(PrefsConstants.fingerprintAuth);
    } catch (e) {
      return false;
    }
  }
}
