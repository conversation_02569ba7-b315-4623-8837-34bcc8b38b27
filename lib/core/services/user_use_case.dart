import 'dart:convert';
import 'package:attp_2024/core/data/models/user_access_model.dart';
import 'package:attp_2024/core/data/prefs/prefs.dart';
import '../configs/contanst/prefs_contansts.dart';

class UserUseCase {
  static final Prefs prefs = Prefs();

  // Store the user login details
  static Future<void> setUser(
      {required UserAccessModel userAccessModel}) async {
    try {
      await prefs.set(PrefsConstants.userMemory, userAccessModel);
      print("User successfully set in preferences.");
    } catch (e) {
      print('Error setting user memory: $e');
    }
  }

  static Future<UserAccessModel?> getUser() async {
    try {
      var userJson = await prefs.get(PrefsConstants.userMemory);
      if (userJson == null) {
        return null;
      }

      var user = jsonDecode(userJson);

      if (user is Map<String, dynamic>) {
        return UserAccessModel.from<PERSON><PERSON>(user);
      } else {
        print("Decoded data is not a Map<String, dynamic>");
        return null;
      }
    } catch (e) {
      print('Error getting user memory: $e');
      return null;
    }
  }

  static Future<void> clearUser() async {
    try {
      await prefs.remove(PrefsConstants.userMemory);
      print("User successfully removed from preferences.");
    } catch (e) {
      print('Error clearing user memory: $e');
    }
  }

  static Future<void> setKeepLogin() async {
    try {
      await prefs.setBool(PrefsConstants.keepLogin, true);
    } catch (e) {
      print(e);
    }
  }

  static Future<bool> getKeepLogin() async {
    try {
      return await prefs.get(PrefsConstants.keepLogin);
    } catch (e) {
      print(e);
      return false;
    }
  }
}
