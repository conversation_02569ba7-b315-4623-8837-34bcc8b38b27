import 'dart:convert';

import 'package:attp_2024/core/configs/contanst/prefs_contansts.dart';
import 'package:attp_2024/core/configs/theme/app_theme_manager.dart';
import 'package:attp_2024/core/data/models/theme_model.dart';
import 'package:attp_2024/core/data/prefs/prefs.dart';

class ThemeUseCase {
  static final Prefs prefs = Prefs();
  static AppThemeManager appThemeManager = AppThemeManager.initialize();

  static Future<void> setThemes({
    required ThemeModel themes,
  }) async {
    try {
      await prefs.set(PrefsConstants.listThemes, themes.toJson());
      print("Themes have been set successfully.");
    } catch (e) {
      print('Error setting themes: $e');
    }
  }

  // Get a list of themes from Prefs
  static Future<ThemeModel> getThemes() async {
    var themesString = await prefs.get(PrefsConstants.listThemes);
    if (themesString == null || themesString.isEmpty) {
      return appThemeManager.defaultTheme;
    }
    return ThemeModel.fromJson(json.decode(themesString));
  }
}
