import 'dart:convert';

import 'package:attp_2024/core/configs/contanst/prefs_contansts.dart';
import 'package:attp_2024/core/data/dto/response/permission_response.dart';
import 'package:attp_2024/core/data/prefs/prefs.dart';

class PermissionUseCase {
  static final Prefs prefs = Prefs();

  // Save a list of permissions to Prefs
  static Future<void> setPermissions({
    required List<PermissionResponseModel> listPermission,
  }) async {
    try {
      await prefs.set(PrefsConstants.listPermission, listPermission);
      print("Permissions have been set successfully.");
    } catch (e) {
      print('Error setting permissions: $e');
    }
  }

  static Future<List<PermissionResponseModel>> getPermissions() async {
    try {
      var listPermissionString = await prefs.get(PrefsConstants.listPermission);
      if (listPermissionString == null || listPermissionString.isEmpty) {
        return [];
      }
      List<dynamic> listDynamic = jsonDecode(listPermissionString);

      return listDynamic
          .map((item) => PermissionResponseModel.fromJson(item))
          .toList();
    } catch (e) {
      return [];
    }
  }

  // Add or update a permission in the list
  static Future<void> addOrUpdatePermission(
      PermissionResponseModel permission) async {
    try {
      List<PermissionResponseModel> listPermission = await getPermissions();
      final existingPermissionIndex =
          listPermission.indexWhere((p) => p.menuCode == permission.menuCode);

      if (existingPermissionIndex != -1) {
        // Update existing permission
        listPermission[existingPermissionIndex] = permission;
        print("Permission with menuCode ${permission.menuCode} updated.");
      } else {
        // Add new permission
        listPermission.add(permission);
        print("New permission added.");
      }

      await setPermissions(listPermission: listPermission);
    } catch (e) {
      print('Error adding or updating permission: $e');
    }
  }

  // Remove a permission by menuCode
  static Future<void> removePermissionByMenuCode(String menuCode) async {
    try {
      List<PermissionResponseModel> listPermission = await getPermissions();
      listPermission.removeWhere((p) => p.menuCode == menuCode);

      await setPermissions(listPermission: listPermission);
      print("Permission with menuCode $menuCode has been removed.");
    } catch (e) {
      print('Error removing permission: $e');
    }
  }
}
