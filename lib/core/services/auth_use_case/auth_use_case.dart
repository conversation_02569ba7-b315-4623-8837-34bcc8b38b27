import 'package:attp_2024/core/configs/contanst/prefs_contansts.dart';
import 'package:attp_2024/core/data/prefs/prefs.dart';

class AuthUseCase {
  static final Prefs prefs = Prefs();

  static Future<void> setToken({required String token}) async {
    try {
      await prefs.set(PrefsConstants.tokenKey, token.toString());
      await prefs.set(PrefsConstants.tokenMemory, token.toString());
    } catch (e) {
      print('Error setting token: $e');
    }
  }

  static Future<String> getToken() async {
    try {
      var data = await prefs.get(PrefsConstants.tokenKey);
      return data.toString();
    } catch (e) {
      print('Error getting token: $e');
      return "";
    }
  }

  static Future<String> getTokenMemory() async {
    try {
      var data = await prefs.get(PrefsConstants.tokenMemory);
      return data.toString();
    } catch (e) {
      print('Error getting token: $e');
      return "";
    }
  }

  static Future<void> logOut() async {
    try {
      await prefs.remove(PrefsConstants.userMemory);
      await prefs.remove(PrefsConstants.tokenKey);
      await prefs.remove(PrefsConstants.keepLogin);
    } catch (e) {
      print('Error logging out: $e');
    }
  }

  static Future<bool> isToken() async {
    try {
      var token = await getToken();
      if (token == '{"error":true}') {
        return false;
      }
      if (token == "") {
        return false;
      }
      return true;
    } catch (e) {
      return false;
    }
  }
}
