import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart' as get_;
import 'package:get/get_navigation/src/snackbar/snackbar.dart';
import 'package:http_parser/http_parser.dart';
import 'package:attp_2024/core/services/user_use_case.dart';
import 'package:uuid/uuid.dart';

// Bypass SSL (Dùng để debug)
class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}
// dán ở đầu hàm

class UploadService {
  final Dio _dio = Dio();

  Future<Response?> uploadImages(List<File> files, String formName,
      [List<String>? fileNames]) async {
    var user = await UserUseCase.getUser();
    var apiUpload = await user?.apiUpFile ?? "";
    print(apiUpload);

    if (apiUpload.isEmpty) {
      print("API upload URL is not configured.");
      return null;
    }

    try {
      String url =
          '$apiUpload?loaiVB=HA&donViCode=84_000_001&formName=$formName';
      String token = await _get_token();
      // String token =
      //     "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJUZW5EYW5nTmhhcCI6InVibmRodXllbl90ZXN0IiwiQ29ubmVjdFVTRVJfRW5jb2RlIjoiWUtRdFQ1ZHZQZFpXLy9mQ2FpZ0o5QlFSVWdjUjY1eTFqTzJER1pKeGVSUmpTUWZ2NFlGTzRNRnRoeVMranZjb2wyQ0VjYTVMZWw3UjBHQXNIdmNQMnFlOUdkNTY3bEpsNmY4QTJVQ1Q3Q3pYN05pL29wUGhFSnRWdnY5N0RZeWpqb1QwclRZTlJzTmFhNVFSd0JObzVJNHJZMk1hZUlheGI0V3pyc2dEVTdFeUJ4RFVHVmRicHc9PSIsIkNvbm5lY3REYXRhX0VuY29kZSI6IllLUXRUNWR2UGRaVy8vZkNhaWdKOUJRUlVnY1I2NXkxak8yREdaSnhlUlJqU1FmdjRZRk80TUZ0aHlTK2p2Y29sMkNFY2E1TGVsN1IwR0FzSHZjUDJxZTlHZDU2N2xKbDZmOEEyVUNUN0N6WDdOaS9vcFBoRUp0VnZ2OTdEWXlqam9UMHJUWU5Sc05hYTVRUndCTm81STRyWTJNYWVJYXhiNFd6cnNnRFU3RXlCeERVR1ZkYnB3PT0iLCJuYmYiOjE3Mjg5ODEwODAsImV4cCI6MTczMTY1OTQ4MCwiaWF0IjoxNzI4OTgxMDgwfQ.fT2SXyYLxFLnNXQYY2gHuuQIMIf7OpgvbNCVVxtFsVU";
      List<MultipartFile> multipartFiles = [];
      for (int i = 0; i < files.length; i++) {
        String fileName = (fileNames != null && fileNames.length > i)
            ? fileNames[i]
            : 'file_$i.${files[i].path.split('.').last}'; // Lấy phần mở rộng từ đường dẫn tệp
        MultipartFile multipartFile = await MultipartFile.fromFile(
          files[i].path,
          filename: fileName,
          contentType:
              MediaType("image", "png"), // Bạn có thể thay đổi loại tệp nếu cần
        );
        multipartFiles.add(multipartFile);
      }

      // Tạo FormData để gửi
      FormData formData = FormData.fromMap({
        "files": multipartFiles,
      });

      // Thực hiện yêu cầu POST với multipart/form-data
      Response response = await _dio.post(
        url,
        data: formData,
        options: Options(
          headers: {
            "Authorization": "Bearer $token",
            "Content-Type": "multipart/form-data",
          },
        ),
      );

      if (response.statusCode == 200) {
        print("Upload successful: ${response.data}");
        return response;
      } else {
        print("Upload failed: ${response.statusCode} - ${response.data}");
        return response;
      }
    } catch (e) {
      print("Error during upload: $e");
    }
    return null;
  }

  Future<Response?> deleteFiles(List<String> filePaths) async {
    if (filePaths.isEmpty) {
      print("No file paths provided.");
      return null;
    }
    var user = await UserUseCase.getUser();
    var apiDelete = '${user?.siteURL}api/DeleteMultiFiles';
    log(apiDelete.toString(), name: "siteURLDelete");
    try {
      String token = await _get_token();
      // String token =
      //     "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJUZW5EYW5nTmhhcCI6InNveXRlIiwiQ29ubmVjdFVTRVJfRW5jb2RlIjoiWUtRdFQ1ZHZQZFpXLy9mQ2FpZ0o5QlFSVWdjUjY1eTFqTzJER1pKeGVSUmpTUWZ2NFlGTzRNRnRoeVMranZjb0JJUVBnN2pnNzkrN2hQeThFZ2JHUHlEMFZRQm9SNkxqaVVnN1VPQUdyaThpL0dKb09obnY0ZTVpcjBScUxrSnFCaDVJQmNjNXhFOFBCYVZRUDZ4VUdpQmRKQ0NJMFI4YmZLYlpwdlU1eEgzUG5Bc0pYTmdOckE9PSIsIkNvbm5lY3REYXRhX0VuY29kZSI6IllLUXRUNWR2UGRaVy8vZkNhaWdKOUJRUlVnY1I2NXkxak8yREdaSnhlUlJqU1FmdjRZRk80TUZ0aHlTK2p2Y29CSVFQZzdqZzc5KzdoUHk4RWdiR1B5RDBWUUJvUjZMamlVZzdVT0FHcmk4aS9HSm9PaG52NGU1aXIwUnFMa0pxQmg1SUJjYzV4RThQQmFWUVA2eFVHaUJkSkNDSTBSOGJmS2JacHZVNXhIM1BuQXNKWE5nTnJBPT0iLCJuYmYiOjE3MzI5MzQwNjUsImV4cCI6MTczNTUyNjA2NSwiaWF0IjoxNzMyOTM0MDY1fQ.p0zQIsj_S9GD08cKjPa1R3Hvk3fUcOrUtSU4YxrPnvE";
      // Tạo body JSON với danh sách tệp cần xóa
      List<String> requestBody = filePaths;

      // Thực hiện yêu cầu DELETE
      Response response = await _dio.post(
        apiDelete!,
        data: requestBody,
        options: Options(
          headers: {
            "Authorization": "Bearer $token",
            "Content-Type": "application/json",
          },
        ),
      );
      if (response.statusCode == 200) {
        print("Delete successful: ${response.data}");
        return response;
      } else {
        print("Delete failed: ${response.statusCode} - ${response.data}");
        return response;
      }
    } catch (e) {
      print("Error during delete: $e");
    }
    return null;
  }

  Future<String> _get_token() async {
    final userId = await UserUseCase.getUser();
    return userId!.token;
  }

  //FORMNAME
  Future<Response?> uploadImage(Uint8List data, String forName,
      [String? fileName]) async {
    var user = await UserUseCase.getUser();
    var apiUpload = user?.siteURL ?? "";
    log(apiUpload, name: "siteURL");
    try {
      String url =
          '${apiUpload}api/UploadMultiFiles?loaiVB=HA&donViCode=84_000_001&formName=$forName';
      log('${apiUpload}api/UploadMultiFiles?loaiVB=HA&donViCode=84_000_001&formName=$forName',
          name: "apiUrl Upload");
      // "http://laptrinhttld.qlns.vn/api/UploadMultiFiles?loaiVB=HA&donViCode=84_000_001&formName=CungLDBanDau";
      String token = await _get_token();
      // String token =
      //     "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJUZW5EYW5nTmhhcCI6InNveXRlIiwiQ29ubmVjdFVTRVJfRW5jb2RlIjoiWUtRdFQ1ZHZQZFpXLy9mQ2FpZ0o5QlFSVWdjUjY1eTFqTzJER1pKeGVSUmpTUWZ2NFlGTzRNRnRoeVMranZjb0JJUVBnN2pnNzkrN2hQeThFZ2JHUHlEMFZRQm9SNkxqaVVnN1VPQUdyaThpL0dKb09obnY0ZTVpcjBScUxrSnFCaDVJQmNjNXhFOFBCYVZRUDZ4VUdpQmRKQ0NJMFI4YmZLYlpwdlU1eEgzUG5Bc0pYTmdOckE9PSIsIkNvbm5lY3REYXRhX0VuY29kZSI6IllLUXRUNWR2UGRaVy8vZkNhaWdKOUJRUlVnY1I2NXkxak8yREdaSnhlUlJqU1FmdjRZRk80TUZ0aHlTK2p2Y29CSVFQZzdqZzc5KzdoUHk4RWdiR1B5RDBWUUJvUjZMamlVZzdVT0FHcmk4aS9HSm9PaG52NGU1aXIwUnFMa0pxQmg1SUJjYzV4RThQQmFWUVA2eFVHaUJkSkNDSTBSOGJmS2JacHZVNXhIM1BuQXNKWE5nTnJBPT0iLCJuYmYiOjE3MzI5MzQwNjUsImV4cCI6MTczNTUyNjA2NSwiaWF0IjoxNzMyOTM0MDY1fQ.p0zQIsj_S9GD08cKjPa1R3Hvk3fUcOrUtSU4YxrPnvE";
      MultipartFile multipartFile = MultipartFile.fromBytes(
        data,
        filename: '${DateTime.timestamp()}$fileName.png',
        contentType: MediaType("image", "png"),
      );
      // Tạo FormData để gửi
      FormData formData = FormData.fromMap({
        "file": multipartFile,
      });
      // Thực hiện yêu cầu POST với multipart/form-data
      Response response = await _dio.post(
        url,
        data: formData,
        options: Options(
          headers: {
            "Authorization": "Bearer $token",
            "Content-Type": "multipart/form-data",
          },
        ),
      );
      if (response.statusCode == 200) {
        print("Tải lên thành công: ${response.data}");
        return response;
      } else {
        print("Tải lên thất bại: ${response.statusCode} - ${response.data}");
        return response;
      }
    } catch (e) {
      print("Lỗi khi tải lên: $e");
    }
    return null;
  }

  

  Future<Response?> uploadMutiMediaFiles(List<String> selectedFiles,
      String loaiVB, String formName, String baseUrl) async {
    HttpOverrides.global = MyHttpOverrides();
    final user = await UserUseCase.getUser();
    final String donViID = user!.donViID;
    final String url =
        "${baseUrl}/api/UploadMultiFiles?loaiVB=$loaiVB&donViCode=$donViID&formName=$formName";
    log("${baseUrl}/api/UploadMultiFiles?loaiVB=$loaiVB&donViCode=$donViID&formName=$formName");
    const uuid = Uuid();
    const maxTotalSizeInMB = 20; // Giới hạn tổng dung lượng 20MB
    const maxTotalSizeInBytes = maxTotalSizeInMB * 1024 * 1024;

    int totalSize = 0;
    FormData formData = FormData();

    for (String filePath in selectedFiles) {
      File file = File(filePath);
      int fileSize = await file.length();
      totalSize += fileSize;

      if (totalSize > maxTotalSizeInBytes) {
        get_.Get.snackbar(
          "Dung lượng quá lớn",
          "Tổng dung lượng tệp tin vượt quá $maxTotalSizeInMB MB, vui lòng chọn ít tệp hoặc tệp có dung lượng nhỏ hơn.",
          snackPosition: SnackPosition.BOTTOM,
        );
        return null;
      }

      String originalFileName = file.path.split('/').last;
      String fileExtension = originalFileName.split('.').last.toLowerCase();

      String newFileName = '${uuid.v4()}.$fileExtension';

      String mimeType;
      if (fileExtension == 'jpg' || fileExtension == 'jpeg') {
        mimeType = 'image/jpeg';
      } else if (fileExtension == 'png') {
        mimeType = 'image/png';
      } else if (fileExtension == 'mp4') {
        mimeType = 'video/mp4';
      } else if (fileExtension == 'mov') {
        mimeType = 'video/quicktime';
      } else {
        mimeType = 'application/octet-stream';
      }

      formData.files.add(
        MapEntry(
          'files',
          await MultipartFile.fromFile(
            file.path,
            filename: newFileName,
            contentType: MediaType.parse(mimeType),
          ),
        ),
      );
    }

    try {
      Response response = await _dio.post(url, data: formData);
      if (response.statusCode == 200) {
        print("Tải lên thành công: ${response.data}");

        // Nếu API trả về danh sách đường dẫn file như ví dụ bạn nói
        // Cắt bỏ domain khỏi đường dẫn mỗi file
        List<String> processedPaths = (response.data['data'] as List)
            .map<String>((e) => cutUrl(e))
            .toList();

        // Gán lại kết quả đã xử lý vào response để sử dụng
        response.data['data'] = processedPaths;

        return response;
      } else {
        print("Tải lên thất bại: ${response.statusCode} - ${response.data}");
        return response;
      }
    } catch (e) {
      print("Lỗi khi tải lên: $e");
      get_.Get.snackbar(
        "Lỗi",
        "Có lỗi xảy ra khi tải lên tệp tin. Vui lòng kiểm tra lại mạng hoặc dung lượng file.",
        snackPosition: SnackPosition.BOTTOM,
      );
    }
    return null;
  }

  String cutUrl(String url) {
    Uri uri = Uri.parse(url);
    String path = uri.path; // Lấy phần /Uploads/Images/e200d681-...
    return path.startsWith('/') ? path.substring(1) : path;
  }


  Future<Response?> uploadPdfFiles(List<String> selectedFiles,
    String loaiVB, String formName, String baseUrl) async {
  HttpOverrides.global = MyHttpOverrides();
  final user = await UserUseCase.getUser();
  final String donViID = user!.donViID;
  final String url =
      "${baseUrl}/api/UploadMultiFiles?loaiVB=$loaiVB&donViCode=$donViID&formName=$formName";
  log("${baseUrl}/api/UploadMultiFiles?loaiVB=$loaiVB&donViCode=$donViID&formName=$formName", name: 'uploadPDFURL');
  const uuid = Uuid();
  const maxTotalSizeInMB = 50; // Giới hạn tổng dung lượng 50MB
  const maxTotalSizeInBytes = maxTotalSizeInMB * 1024 * 1024;

  int totalSize = 0;
  FormData formData = FormData();

  for (String filePath in selectedFiles) {
    File file = File(filePath);
    int fileSize = await file.length();
    totalSize += fileSize;

    if (totalSize > maxTotalSizeInBytes) {
      get_.Get.snackbar(
        "Dung lượng quá lớn",
        "Tổng dung lượng tệp tin vượt quá $maxTotalSizeInMB MB, vui lòng chọn ít tệp hoặc tệp có dung lượng nhỏ hơn.",
        snackPosition: SnackPosition.BOTTOM,
      );
      return null;
    }

    String originalFileName = file.path.split('/').last;
    String fileExtension = originalFileName.split('.').last.toLowerCase();

    if (fileExtension != 'pdf') {
      get_.Get.snackbar(
        "Lỗi định dạng",
        "Chỉ hỗ trợ tải lên tệp PDF.",
        snackPosition: SnackPosition.BOTTOM,
      );
      return null;
    }

    String newFileName = '${uuid.v4()}.$fileExtension';

    formData.files.add(
      MapEntry(
        'files',
        await MultipartFile.fromFile(
          file.path,
          filename: newFileName,
          contentType: MediaType.parse('application/pdf'),
        ),
      ),
    );
  }

  try {
    Response response = await _dio.post(url, data: formData);
    if (response.statusCode == 200) {
      print("Tải lên thành công: ${response.data}");

      List<String> processedPaths = (response.data['data'] as List)
          .map<String>((e) => cutUrl(e))
          .toList();

      response.data['data'] = processedPaths;

      return response;
    } else {
      print("Tải lên thất bại: ${response.statusCode} - ${response.data}");
      return response;
    }
  } catch (e) {
    print("Lỗi khi tải lên: $e");
    get_.Get.snackbar(
      "Lỗi",
      "Có lỗi xảy ra khi tải lên tệp tin. Vui lòng kiểm tra lại mạng hoặc dung lượng file.",
      snackPosition: SnackPosition.BOTTOM,
    );
  }
  return null;
}


}
