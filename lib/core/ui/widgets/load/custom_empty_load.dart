import 'package:flutter/widgets.dart';
import 'package:attp_2024/core/configs/assets/images/no_images.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';

class CustomEmptyLoad extends StatelessWidget {
  const CustomEmptyLoad({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            NoImages.noData,
            width: 60,
          ),
          const TextWidget(text: "<PERSON>hông có dữ liệu"),
        ],
      ),
    );
  }
}
