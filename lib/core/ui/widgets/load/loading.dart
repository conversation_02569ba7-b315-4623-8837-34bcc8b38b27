import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';

class Loading {
  // ignore: non_constant_identifier_names
  static Obx LoadingFullScreen({
    required RxBool isLoading,
    required Widget body,
  }) {
    return Obx(() => Scaffold(
          body: Stack(
            children: [
              body,
              if (isLoading.value)
                Positioned.fill(
                  child: Container(
                    color: AppColors.gray1.withOpacity(0.3),
                    child: Center(
                      child: Image.asset(
                        width: 10.w,
                        AppImageString.iLoading,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ));
  }

  // ignore: non_constant_identifier_names
  static Obx LoadingPartial({required RxBool isLoading, required Widget body}) {
    return Obx(() => isLoading.value
        ? const Center(
            child: TextWidget(
              text: "Loading ...",
              size: AppDimens.textSize12,
              color: AppColors.gray2,
            ),
          )
        : body);
  }
}
