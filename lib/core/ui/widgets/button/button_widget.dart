import 'package:flutter/material.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';

// ignore: must_be_immutable
class ButtonWidget extends StatelessWidget {
  final double textSize;
  final VoidCallback? ontap;
  final String text;
  final double? width;
  late Color? backgroundColor;
  final Color textColor;
  final FontWeight? fontWeight;
  final bool? isBorder;
  late Color? borderColor;
  final Icon? leadingIcon;
  final double? borderRadius;
  final Widget? child;
  final EdgeInsetsGeometry padding;
  final bool enabled; // New attribute

  ButtonWidget({
    super.key,
    this.fontWeight = FontWeight.w600,
    required this.ontap,
    required this.text,
    this.width = double.infinity,
    this.isBorder = false,
    this.borderColor,
    this.textColor = AppColors.white,
    this.backgroundColor,
    this.leadingIcon,
    this.child,
    this.borderRadius = 10,
    this.padding = const EdgeInsets.symmetric(horizontal: 6.0),
    this.textSize = AppDimens.textStandard,
    this.enabled = true, // Default value for enabled
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: enabled ? ontap : null, // Disable onTap when not enabled
      child: Container(
        padding: padding,
        width: width,
        decoration: BoxDecoration(
          border: isBorder == true
              ? Border.all(
                  width: 1,
                  color: borderColor ?? AppColors.grey,
                )
              : null,
          color: enabled
              ? backgroundColor
              : backgroundColor?.withOpacity(0.5) ?? AppColors.grey,
          borderRadius: BorderRadius.circular(borderRadius!),
        ),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (leadingIcon != null) leadingIcon!,
              if (leadingIcon != null) const SizedBox(width: 10.0),
              child ?? 
                  TextWidget(
                    size: textSize,
                    text: text,
                    fontWeight: fontWeight,
                    textAlign: TextAlign.center,
                    color: enabled ? textColor : AppColors.white,
                  ),
            ],
          ),
        ),
      ),
    );
  }
}
