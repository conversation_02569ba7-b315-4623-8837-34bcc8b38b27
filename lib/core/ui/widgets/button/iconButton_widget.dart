import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class CustomIconButton extends StatelessWidget {
  final VoidCallback onPressed;
  final Color colorButton;
  final Icon icon;

  const CustomIconButton({
    super.key,
    required this.onPressed,
    required this.icon,
    required this.colorButton,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 5.h,
      height: 5.h,
      decoration: BoxDecoration(
        color: colorButton,
        shape: BoxShape.rectangle,
        borderRadius: BorderRadius.circular(8),
      ),
      child: IconButton(
        icon: icon,
        onPressed: onPressed,
      ),
    );
  }
}
