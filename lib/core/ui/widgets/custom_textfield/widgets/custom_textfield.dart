import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';

class TextFieldWidget extends StatefulWidget {
  final String title;
  final bool hideTitle;
  final double height;
  final TextStyle? titleStyle;
  final bool isRequired;
  final String placeholder;
  final double cornerRadius;
  final bool isDisabled;
  final String value;
  final String? warningText;
  final bool showClearButton;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final Function(String) onChange;
  final Function()? onDelete;
  final Function()? onTap;
  final FocusNode? focusNode;
  final String? errorWidget;
  final String? setValue;
  final double? maxWidth;
  final Color? clearIcon;
  final Color? focusOutline;
  final bool? isNumber;

  const TextFieldWidget({
    super.key,
    required this.title,
    this.hideTitle = false,
    this.isNumber = false,
    this.height = 45,
    this.titleStyle,
    this.isRequired = false,
    required this.placeholder,
    this.cornerRadius = 5.0,
    this.isDisabled = false,
    required String initialValue,
    this.warningText,
    this.showClearButton = true,
    this.prefixIcon,
    this.suffixIcon,
    required this.onChange,
    this.onTap,
    this.focusNode,
    this.errorWidget,
    this.maxWidth,
    this.clearIcon = AppColors.iconColors,
    this.focusOutline,
    this.setValue,
    this.onDelete,
  }) : value = initialValue;

  @override
  // ignore: library_private_types_in_public_api
  _TextFieldWidgetState createState() => _TextFieldWidgetState();
}

class _TextFieldWidgetState extends State<TextFieldWidget> {
  late TextEditingController _controller;
  final RxBool showError = false.obs;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.value);
  }

  @override
  void didUpdateWidget(TextFieldWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Kiểm tra nếu có sự thay đổi trong `setValue` và cập nhật controller
    if (widget.setValue != null && widget.setValue != _controller.text) {
      _updateValue(widget.setValue!);
    }
  }

  void _updateValue(String newValue) {
    setState(() {
      _controller.text = newValue;
      widget.onChange(newValue); // Gọi hàm onChange với giá trị mới
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!widget.hideTitle)
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: widget.title,
                  style: widget.titleStyle?.copyWith(
                        color: AppColors.black,
                        fontSize: AppDimens.sizeTitle,
                        fontWeight: FontWeight.w500,
                      ) ??
                      const TextStyle(
                        fontWeight: FontWeight.w500,
                        color: AppColors.black,
                        fontSize: AppDimens.sizeTitle,
                      ),
                ),
                if (widget.isRequired)
                  const TextSpan(
                    text: ' *',
                    style: TextStyle(
                      color: Colors.red,
                      fontSize: AppDimens.textSize14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
              ],
            ),
          ),
        const SizedBox(height: 4.0),
        Obx(() {
          final isError = showError.value;
          return SizedBox(
            height: widget.height,
            width: widget.maxWidth,
            child: TextField(
              keyboardType: widget?.isNumber ?? false
                  ? TextInputType.number
                  : TextInputType.text,
              enabled: !widget.isDisabled,
              controller: _controller,
              onChanged: (value) {
                widget.onChange(value);
              },
              decoration: InputDecoration(
                hintText: widget.placeholder,
                contentPadding: const EdgeInsets.symmetric(
                  vertical: 10,
                  horizontal: 20,
                ),
                hintStyle: TextStyle(
                  color: widget.isDisabled
                      ? AppColors.borderInputDisabled
                      : AppColors.borderInputDisabled,
                  fontSize: AppDimens.textSize14,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(widget.cornerRadius),
                  borderSide: BorderSide(
                    color: (widget.errorWidget?.isNotEmpty ?? false)
                        ? Colors.red
                        : AppColors.borderInput1,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(widget.cornerRadius),
                  borderSide: BorderSide(
                    color: (widget.errorWidget?.isNotEmpty ?? false)
                        ? Colors.red
                        : AppColors.focusColor,
                    width: 1,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(widget.cornerRadius),
                  borderSide: BorderSide(
                    color: (widget.errorWidget?.isNotEmpty ?? false)
                        ? Colors.red
                        : AppColors.borderInput1,
                    width: .3,
                  ),
                ),
                disabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(widget.cornerRadius),
                  borderSide: const BorderSide(
                    color: AppColors.borderInput1,
                    width: .3,
                  ),
                ),
                filled: true,
                fillColor: widget.isDisabled
                    ? AppColors.gray1.withOpacity(.5)
                    : Colors.white,
                errorText: (widget.errorWidget?.isNotEmpty ?? false)
                    ? widget.warningText
                    : null,
                errorStyle: const TextStyle(
                  color: Colors.red,
                  fontSize: 12,
                ),
                prefixIcon: widget.prefixIcon,
                suffixIcon:
                    widget.showClearButton && _controller.text.isNotEmpty
                        ? Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              if (widget.suffixIcon != null) widget.suffixIcon!,
                              IconButton(
                                icon: Icon(
                                  CupertinoIcons.clear_circled_solid,
                                  color: widget.clearIcon,
                                  size: 18,
                                ),
                                onPressed: () {
                                  widget.onDelete?.call();
                                  _controller.clear();
                                  widget.onChange('');
                                },
                              ),
                            ],
                          )
                        : widget.suffixIcon,
              ),
              style: TextStyle(
                color:
                    widget.isDisabled ? AppColors.borderInputDisabled : Colors.black,
              ),
              cursorColor:
                  widget.isDisabled ? AppColors.borderInput1 : Colors.black,
            ),
          );
        }),
        Text(
          widget.errorWidget ?? "",
          style: const TextStyle(color: Colors.red),
          overflow: TextOverflow.ellipsis,
          maxLines: null,
          softWrap: true,
        ),
      ],
    );
  }
}
