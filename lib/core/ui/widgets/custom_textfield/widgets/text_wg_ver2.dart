import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

class CustomTextFieldVer2 extends StatefulWidget {
  final String? title;
  final String? placeholder;
  final bool isPassword;
  final bool isRequired;
  final TextInputType? inputType;
  final TextEditingController? controller;
  final Function(String)? onChange;
  final bool showClearButton;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final Function()? onSuffixIconPressed;
  final String? errorText;
  final bool enabled;

  const CustomTextFieldVer2({
    super.key,
    this.title,
    this.placeholder,
    this.isPassword = false,
    this.isRequired = false,
    this.inputType,
    this.controller,
    this.onChange,
    this.showClearButton = true,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconPressed,
    this.errorText,
    this.enabled = true,
  });

  @override
  State<CustomTextFieldVer2> createState() => _CustomTextFieldStateVer2();
}

class _CustomTextFieldStateVer2 extends State<CustomTextFieldVer2> {
  late bool _isObscured;

  @override
  void initState() {
    super.initState();
    _isObscured = widget.isPassword;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.title != null)
          Row(
            children: [
              Text(
                widget.title!,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                ),
              ),
              if (widget.isRequired)
                const Text(
                  ' *',
                  style: TextStyle(color: Colors.red),
                ),
            ],
          ),
        const SizedBox(height: 4),
        TextField(
          controller: widget.controller,
          onChanged: widget.onChange,
          enabled: widget.enabled,
          obscureText: _isObscured,
          keyboardType: widget.inputType ?? TextInputType.text,
          decoration: InputDecoration(
            hintText: widget.placeholder ?? '',
            hintStyle: const TextStyle(color: Colors.grey),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 14,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: widget.errorText != null ? Colors.red : Colors.grey,
                width: 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.grey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: widget.errorText != null ? Colors.red : Colors.blue,
                width: 1.5,
              ),
            ),
            prefixIcon: widget.prefixIcon != null
                ? Icon(widget.prefixIcon, color: Colors.grey)
                : null,
            suffixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (widget.isPassword)
                  IconButton(
                    icon: Icon(
                      _isObscured ? Icons.visibility_off : Icons.visibility,
                      color: Colors.grey,
                    ),
                    onPressed: () {
                      setState(() {
                        _isObscured = !_isObscured;
                      });
                    },
                  ),
                if (widget.suffixIcon != null)
                  IconButton(
                    icon: Icon(widget.suffixIcon, color: Colors.grey),
                    onPressed: widget.onSuffixIconPressed,
                  ),
                if (widget.showClearButton &&
                    widget.controller?.text.isNotEmpty == true)
                  IconButton(
                    icon: const Icon(Icons.clear, color: Colors.grey),
                    onPressed: () {
                      widget.controller?.clear();
                      widget.onChange?.call('');
                    },
                  ),
              ],
            ),
            errorText: widget.errorText,
          ),
        ),
        if (widget.errorText != null)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              widget.errorText!,
              style: const TextStyle(
                color: Colors.red,
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }
}
