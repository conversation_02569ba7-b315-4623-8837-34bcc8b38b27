import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SearchBarWidget extends StatefulWidget {
  final String placeholder;
  final double cornerRadius;
  final bool isDisabled;
  final String? initialValue;
  final bool showClearButton;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final Function(String) onChange;
  final FocusNode? focusNode;
  final String? errorWidget;
  final double? maxWidth;
  final bool autoFocus;

  const SearchBarWidget({
    Key? key,
    required this.placeholder,
    this.cornerRadius = 10.0,
    this.isDisabled = false,
    this.initialValue,
    this.showClearButton = true,
    this.prefixIcon,
    this.suffixIcon,
    required this.onChange,
    this.focusNode,
    this.errorWidget,
    this.maxWidth,
    this.autoFocus = false,
  }) : super(key: key);

  @override
  _SearchBarWidgetState createState() => _SearchBarWidgetState();
}

class _SearchBarWidgetState extends State<SearchBarWidget> {
  late TextEditingController _controller;
  final RxBool showError = false.obs;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final isError = showError.value;
      return SizedBox(
        height: 45,
        width: widget.maxWidth ?? MediaQuery.of(context).size.width,
        child: TextField(
          enabled: !widget.isDisabled,
          controller: _controller,
          autofocus: widget.autoFocus,
          onChanged: (value) {
            showError.value = value.isEmpty;
            widget.onChange(value);
            setState(() {});
          },
          decoration: InputDecoration(
            hintText: widget.placeholder,
            contentPadding:
                const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
            hintStyle: TextStyle(
              color: widget.isDisabled ? Colors.grey.shade400 : Colors.grey,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(widget.cornerRadius),
              borderSide: BorderSide(
                color: isError ? Colors.red : Colors.grey,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(widget.cornerRadius),
              borderSide: BorderSide(
                color: isError ? Colors.red : Colors.blue,
                width: 2,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(widget.cornerRadius),
              borderSide: BorderSide(
                color: isError ? Colors.red : Colors.grey,
                width: 1,
              ),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(widget.cornerRadius),
              borderSide: BorderSide(
                color: Colors.grey.shade400,
                width: 1,
              ),
            ),
            filled: true,
            fillColor: widget.isDisabled ? Colors.grey.shade200 : Colors.white,
            errorText: isError ? widget.errorWidget : null,
            errorStyle: const TextStyle(color: Colors.red, fontSize: 12),
            prefixIcon: widget.prefixIcon ?? const Icon(Icons.search),
            suffixIcon: widget.showClearButton && _controller.text.isNotEmpty
                ? Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (widget.suffixIcon != null) widget.suffixIcon!,
                      IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _controller.clear();
                          widget.onChange('');
                          setState(() {});
                        },
                      ),
                    ],
                  )
                : widget.suffixIcon,
          ),
          style: TextStyle(
            color: widget.isDisabled ? Colors.grey.shade600 : Colors.black,
          ),
          cursorColor: widget.isDisabled ? Colors.grey : Colors.black,
        ),
      );
    });
  }
}
