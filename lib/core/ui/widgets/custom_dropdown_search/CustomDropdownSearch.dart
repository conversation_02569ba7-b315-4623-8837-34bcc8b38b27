// import 'package:dropdown_search/dropdown_search.dart';
// import 'package:flutter/material.dart';
// import 'package:ttld_flutter/features/nav/home/<USER>/item_category_function_model.dart';
//
// class CustomDropdownSearch<T> extends StatelessWidget {
//   final String title;
//   final Future<List<T>> Function(String) items;
//   final void Function(T?) onChanged;
//   final bool Function(T, T) compareFn;
//   final T? selectedItem;
//   final String Function(T) itemLabel;
//   final String title_dropdown;
//   final bool cacheItems;
//   final List<T> selectedItems;
//   final bool required;
//   final double maxHeight;
//   final double minHeight;
//   final bool enabled;
//   final String placeholder;
//
//   const CustomDropdownSearch({
//     super.key,
//     this.selectedItems = const [],
//     this.title = "",
//     required this.items,
//     required this.onChanged,
//     required this.compareFn,
//     this.selectedItem,
//     required this.itemLabel,
//     required this.title_dropdown,
//     this.cacheItems = true,
//     this.required = false,
//     this.maxHeight = double.infinity,
//     this.minHeight = double.infinity,
//     this.enabled = true,
//     this.placeholder = "Chọn một mục",
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         title.isNotEmpty
//             ? RichText(
//                 text: TextSpan(
//                   children: [
//                     TextSpan(
//                       text: title,
//                       style: const TextStyle(
//                         fontWeight: FontWeight.w500,
//                         color: Colors.black,
//                         fontSize: 15,
//                       ),
//                     ),
//                     if (required)
//                       const TextSpan(
//                         text: ' *',
//                         style: TextStyle(
//                           color: Colors.red,
//                           fontSize: 14,
//                           fontWeight: FontWeight.bold,
//                         ),
//                       ),
//                   ],
//                 ),
//               )
//             : SizedBox(
//                 height: 0,
//                 width: 0,
//               ),
//         const SizedBox(height: 4.0),
//         DropdownSearch<T>(
//           items: (filter, s) => items(filter),
//           compareFn: compareFn,
//           selectedItem: selectedItem,
//           enabled: enabled,
//           dropdownBuilder: (context, selectedItem) {
//             return Text(
//               selectedItem != null ? itemLabel(selectedItem) : placeholder,
//             );
//           },
//           popupProps: PopupPropsMultiSelection.modalBottomSheet(
//             fit: FlexFit.tight,
//             constraints: BoxConstraints(maxHeight: maxHeight),
//             title: Text(
//               title_dropdown,
//               textAlign: TextAlign.center,
//               style: const TextStyle(
//                 fontWeight: FontWeight.bold,
//                 fontSize: 16,
//               ),
//             ),
//             modalBottomSheetProps: const ModalBottomSheetProps(
//               showDragHandle: true,
//               backgroundColor: Colors.white,
//             ),
//             showSearchBox: true,
//             cacheItems: cacheItems,
//             showSelectedItems: true,
//             itemBuilder: (context, item, isDisabled, isSelected) {
//               return Container(
//                 color: isSelected ? Colors.blue.withOpacity(0.2) : null,
//                 // Màu nền khi được chọn
//                 child: ListTile(
//                   title: Text(itemLabel(item)),
//                   trailing: isSelected
//                       ? const Icon(
//                           Icons.check, // Icon hiển thị khi được chọn
//                           color: Colors.blue,
//                         )
//                       : null,
//                   selected: isSelected,
//                 ),
//               );
//             },
//             suggestedItemProps: SuggestedItemProps(
//               showSuggestedItems: true,
//               suggestedItems: (us) {
//                 return us.where((e) => itemLabel(e).contains("")).toList();
//               },
//             ),
//           ),
//           onChanged: onChanged,
//           decoratorProps: DropDownDecoratorProps(
//             isHovering: true,
//             decoration: InputDecoration(
//               filled: !enabled ? true : false,
//               fillColor: enabled ? Colors.white : Colors.grey[200],
//               labelStyle: const TextStyle(
//                 color: Colors.grey,
//                 fontSize: 16,
//               ),
//               enabledBorder: OutlineInputBorder(
//                 borderRadius: BorderRadius.circular(5.0),
//                 borderSide: const BorderSide(
//                   color: Colors.grey,
//                   width: 1.0,
//                 ),
//               ),
//               disabledBorder: OutlineInputBorder(
//                 borderRadius: BorderRadius.circular(5.0),
//                 borderSide: const BorderSide(
//                   color: Colors.grey,
//                   width: 1.0,
//                 ),
//               ),
//               focusedBorder: OutlineInputBorder(
//                 borderRadius: BorderRadius.circular(10.0),
//                 borderSide: const BorderSide(
//                   color: Colors.blue,
//                   width: 2.0,
//                 ),
//               ),
//               errorBorder: OutlineInputBorder(
//                 borderRadius: BorderRadius.circular(10.0),
//                 borderSide: const BorderSide(
//                   color: Colors.red,
//                   width: 1.0,
//                 ),
//               ),
//               focusedErrorBorder: OutlineInputBorder(
//                 borderRadius: BorderRadius.circular(10.0),
//                 borderSide: const BorderSide(
//                   color: Colors.redAccent,
//                   width: 2.0,
//                 ),
//               ),
//               contentPadding: const EdgeInsets.symmetric(
//                 vertical: 10,
//                 horizontal: 12,
//               ),
//             ),
//           ),
//         ),
//       ],
//     );
//   }
// }
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';

class CustomDropdownSearch<T> extends StatelessWidget {
  final String title;
  final Future<List<T>> Function(String) items;
  final void Function(T?) onChanged;
  final bool Function(T, T) compareFn;
  final T? selectedItem;
  final String Function(T) itemLabel;
  final String title_dropdown;
  final bool cacheItems;
  final List<T> selectedItems;
  final bool required;
  final double maxHeight;
  final double minHeight;
  final bool enabled;
  final String placeholder;

  const CustomDropdownSearch({
    super.key,
    this.selectedItems = const [],
    this.title = "",
    required this.items,
    required this.onChanged,
    required this.compareFn,
    this.selectedItem,
    required this.itemLabel,
    required this.title_dropdown,
    this.cacheItems = true,
    this.required = false,
    this.maxHeight = double.infinity,
    this.minHeight = double.infinity,
    this.enabled = true,
    this.placeholder = "",
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        title.isNotEmpty
            ? RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: title,
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                        fontSize: 15,
                      ),
                    ),
                    if (required)
                      const TextSpan(
                        text: ' *',
                        style: TextStyle(
                          color: Colors.red,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                  ],
                ),
              )
            : const SizedBox.shrink(),
        const SizedBox(height: 4.0),
        DropdownSearch<T>(
          items: (filter, s) => items(filter),
          compareFn: compareFn,
          selectedItem: selectedItem,
          enabled: enabled,
          dropdownBuilder: (context, selectedItem) {
            return Text(
              selectedItem != null ? itemLabel(selectedItem) : placeholder,
            );
          },
          popupProps: PopupPropsMultiSelection.modalBottomSheet(
            fit: FlexFit.tight,
            constraints: BoxConstraints(maxHeight: maxHeight),
            title: Text(
              title_dropdown,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            modalBottomSheetProps: const ModalBottomSheetProps(
              barrierColor: Colors.transparent,
              useRootNavigator: true,
              showDragHandle: true,
              backgroundColor: Colors.white,
            ),
            showSearchBox: true,
            cacheItems: cacheItems,
            showSelectedItems: true,
            itemBuilder: (context, item, isDisabled, isSelected) {
              return Container(
                color: isSelected ? Colors.blue.withOpacity(0.2) : null,
                child: ListTile(
                  title: Text(itemLabel(item)),
                  trailing: isSelected
                      ? const Icon(
                          Icons.check,
                          color: Colors.blue,
                        )
                      : null,
                  selected: isSelected,
                ),
              );
            },
            suggestedItemProps: SuggestedItemProps(
              showSuggestedItems: true,
              suggestedItems: (us) {
                return us.where((e) => itemLabel(e).contains("")).toList();
              },
            ),
          ),
          onChanged: onChanged,
          decoratorProps: DropDownDecoratorProps(
            isHovering: true,
            decoration: InputDecoration(
              filled: !enabled ? true : false,
              fillColor: enabled ? Colors.white : Colors.grey[200],
              labelStyle: const TextStyle(
                color: Colors.grey,
                fontSize: 16,
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(5.0),
                borderSide: const BorderSide(
                  color: Colors.grey,
                  width: 1.0,
                ),
              ),
              disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(5.0),
                borderSide: const BorderSide(
                  color: Colors.grey,
                  width: 1.0,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10.0),
                borderSide: const BorderSide(
                  color: Colors.blue,
                  width: 2.0,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10.0),
                borderSide: const BorderSide(
                  color: Colors.red,
                  width: 1.0,
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10.0),
                borderSide: const BorderSide(
                  color: Colors.redAccent,
                  width: 2.0,
                ),
              ),
              contentPadding: const EdgeInsets.symmetric(
                vertical: 10,
                horizontal: 12,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
