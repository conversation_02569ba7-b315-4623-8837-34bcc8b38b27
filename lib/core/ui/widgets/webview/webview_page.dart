import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';

class WebViewPage extends StatefulWidget {
  final String initialUrl;
  final String title;
  final bool reload;
  final PreferredSizeWidget? appBar;
  final JavaScriptMode javascriptMode;
  final void Function(WebViewController controller)? onWebViewCreated;
  final void Function(int progress)? onProgress;
  final void Function(String url)? onPageStarted;
  final void Function(String url)? onPageFinished;
  final NavigationDecision Function(NavigationRequest request)?
      onNavigationRequest;

  const WebViewPage({
    super.key,
    required this.initialUrl,
    this.title = 'WebView',
    this.appBar,
    this.javascriptMode = JavaScriptMode.unrestricted,
    this.onWebViewCreated,
    this.onProgress,
    this.onPageStarted,
    this.onPageFinished,
    this.onNavigationRequest,
    this.reload = false,
  });

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> {
  late final WebViewController _controller;

  @override
  void initState() {
    super.initState();

    // ✅ Chuyển đổi URL nếu là PDF trên Android
    String fixedUrl = getPdfUrl(widget.initialUrl);

    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: widget.onProgress,
          onPageStarted: widget.onPageStarted,
          onPageFinished: widget.onPageFinished,
          onNavigationRequest: widget.onNavigationRequest ??
              (request) => NavigationDecision.navigate,
        ),
      )
      ..loadRequest(Uri.parse(fixedUrl));

    widget.onWebViewCreated?.call(_controller);
  }

  /// ✅ Chuyển đổi URL nếu là PDF trên Android
  String getPdfUrl(String url) {
    if (Platform.isAndroid && url.endsWith(".pdf")) {
      return "https://docs.google.com/gview?embedded=true&url=$url";
    }
    return url;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: widget.appBar ??
          AppBarWidget(
            title: widget.title,
            centerTitle: true,
            // actions: [
            //   Padding(
            //     padding: EdgeInsets.only(right: 4.w),
            //     child: IconButton(
            //       color: Colors.white,
            //       onPressed: () {
            //         _controller.reload();
            //       },
            //       icon: const Icon(CupertinoIcons.refresh),
            //     ),
            //   )
            // ],
          ),
      body: WebViewWidget(controller: _controller),
    );
  }
}
