import 'package:flutter/material.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';

// ignore: must_be_immutable
class CstomButtonWidget extends StatelessWidget {
  final double textSize;
  final VoidCallback ontap;
  final String text;
  final double? width;
  final double? height;
  late Color? backgroundColor;
  final Color textColor;
  final FontWeight? fontWeight;
  final bool? isBorder;
  late Color? borderColor;
  final Icon? leadingIcon;
  final double? borderRadius;
  final Widget? child;
  final EdgeInsetsGeometry padding;

  CstomButtonWidget({
    super.key,
    this.fontWeight = FontWeight.w600,
    required this.ontap,
    required this.text,
    this.width = 150,
    this.height,
    this.isBorder = false,
    this.borderColor,
    this.textColor = AppColors.white,
    this.backgroundColor,
    this.leadingIcon,
    this.child,
    this.borderRadius = 10,
    this.padding = const EdgeInsets.symmetric(horizontal: 6.0),
    this.textSize = AppDimens.textStandard,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: ontap,
      child: Container(
        padding: padding,
        width: width,
        height: height,
        decoration: BoxDecoration(
          border: isBorder == true
              ? Border.all(
                  width: 1,
                  color: borderColor!,
                )
              : null,
          color: backgroundColor,
          borderRadius: BorderRadius.circular(borderRadius!),
        ),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (leadingIcon != null) leadingIcon!,
              if (leadingIcon != null) const SizedBox(width: 10.0),
              child ??
                  TextWidget(
                    size: textSize,
                    text: text,
                    fontWeight: fontWeight,
                    textAlign: TextAlign.center,
                    color: textColor,
                  ),
            ],
          ),
        ),
      ),
    );
  }
}
