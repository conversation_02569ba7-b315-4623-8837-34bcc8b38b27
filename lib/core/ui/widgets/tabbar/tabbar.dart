import 'package:flutter/material.dart';

class TabBarWidget extends StatefulWidget {
  final List<String> tabs;
  final List<Widget> children;
  final Color selectedColor;
  final Color unselectedColor;
  final Color indicatorColor;
  final double height;
  final double fontSize;
  final double tabMinWidth;
  final double tabPadding;
  final Function(int)? onChangeTab; // Added callback

  const TabBarWidget({
    Key? key,
    required this.tabs,
    required this.children,
    this.selectedColor = Colors.blue,
    this.unselectedColor = Colors.grey,
    this.indicatorColor = Colors.blue,
    this.height = 45.0,
    this.fontSize = 16.0,
    this.tabMinWidth = 90.0,
    this.tabPadding = 16.0,
    this.onChangeTab, // Added to constructor
  })  : assert(tabs.length == children.length),
        super(key: key);

  @override
  _TabBarWidgetState createState() => _TabBarWidgetState();
}

class _TabBarWidgetState extends State<TabBarWidget> {
  int _selectedIndex = 0;
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollToSelectedTab() {
    final RenderBox tabBox = context.findRenderObject() as RenderBox;
    final double width = tabBox.size.width;
    final double scrollOffset =
        (_selectedIndex * (widget.tabMinWidth + widget.tabPadding * 2)) -
            (width / 2) +
            (widget.tabMinWidth / 2);

    _scrollController.animateTo(
      scrollOffset.clamp(0.0, _scrollController.position.maxScrollExtent),
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _handleTabChange(int index) {
    setState(() => _selectedIndex = index);
    _scrollToSelectedTab();
    widget.onChangeTab?.call(index); // Notify parent about tab change
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          height: widget.height,
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: Colors.grey[300]!,
                width: 1.0,
              ),
            ),
          ),
          child: SingleChildScrollView(
            controller: _scrollController,
            scrollDirection: Axis.horizontal,
            child: Row(
              children: List.generate(
                widget.tabs.length,
                (index) => GestureDetector(
                  onTap: () =>
                      _handleTabChange(index), // Updated to use new handler
                  child: Container(
                    constraints: BoxConstraints(
                      minWidth: widget.tabMinWidth,
                    ),
                    padding:
                        EdgeInsets.symmetric(horizontal: widget.tabPadding),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: _selectedIndex == index
                              ? widget.indicatorColor
                              : Colors.transparent,
                          width: 2.0,
                        ),
                      ),
                    ),
                    child: Center(
                      child: Text(
                        widget.tabs[index],
                        style: TextStyle(
                          color: _selectedIndex == index
                              ? widget.selectedColor
                              : widget.unselectedColor,
                          fontSize: widget.fontSize,
                          fontWeight: _selectedIndex == index
                              ? FontWeight.bold
                              : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        Expanded(
          child: widget.children[_selectedIndex],
        ),
      ],
    );
  }
}


// Hướng dẫn sử dụng:
// Widget _appbar() {
//     return TabBarWidget(
//       tabs: ['Tab 1', 'Tab 2', 'Tab 3'],
//       children: [
//         Container(color: Colors.red, child: Center(child: Text('Content 1'))),
//         Container(color: Colors.green, child: Center(child: Text('Content 2'))),
//         Container(color: Colors.blue, child: Center(child: Text('Content 3'))),
        
//       ],
//       tabMinWidth: 90.0, // Optional: minimum width for each tab
//       tabPadding: 16.0, // Optional: horizontal padding for each tab
//       onChangeTab: (index) {
//         print('Tab changed to index: $index');
//         // Do something when tab changes
//       },
//     );
//   }