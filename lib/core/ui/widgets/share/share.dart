import 'dart:developer';
import 'package:app_links/app_links.dart';
import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';

class ShareWidget extends StatelessWidget {
  final String text;

  const ShareWidget({
    Key? key,
    required this.text,
  }) : super(key: key);

  void shareContent() {
    final appLinks = AppLinks();
    //setup dynamicLinking
    final sub = appLinks.uriLinkStream.listen((uri) {
      log('URI: ${uri.toString()}');
    });
    Share.share(text + '\n Xem thêm tại: attp://nhattamsoft.vn/tintuc');
  }

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: Icon(Icons.share),
      color: Colors.white,
      onPressed: () {
        shareContent();
      },
    );
  }
}
