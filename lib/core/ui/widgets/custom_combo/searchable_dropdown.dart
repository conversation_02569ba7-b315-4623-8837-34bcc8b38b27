import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/ui/widgets/custom_combo/controller/searchable_dropdown_controller.dart';

class SearchableDropdown extends StatelessWidget {
  final List<Map<String, dynamic>> items;
  final Map<String, dynamic>? selectedItem;
  final ValueChanged<Map<String, dynamic>?>? onChanged;
  final String dropdownTitle;
  final bool showCardSelec;
  final bool isDisabled;

  const SearchableDropdown({
    super.key,
    required this.items,
    this.selectedItem,
    this.onChanged,
    this.dropdownTitle = 'Chọn một mục',
    this.showCardSelec = true,
    this.isDisabled = false,
  });

  @override
  Widget build(BuildContext context) {
    final SearchableDropdownController controller =
        Get.put(SearchableDropdownController(items));
    final ScrollController scrollController = ScrollController();

    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.8,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              dropdownTitle,
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              decoration: InputDecoration(
                labelText: 'Tìm kiếm',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
                suffixIcon: const Icon(Icons.search),
                contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16.0, vertical: 12.0),
              ),
              onChanged: (query) {
                controller.updateSearchQuery(query);
              },
            ),
          ),
          // Hiển thị mục đang chọn
          if (selectedItem != null && showCardSelec == true)
            Card(
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.zero, // Bỏ radius
              ),
              margin: const EdgeInsets.symmetric(horizontal: 0, vertical: 8.0),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: SizedBox(
                  width: double.infinity, // Đặt chiều rộng là full
                  child: Text(
                    textAlign: TextAlign.center,
                    selectedItem!['label'],
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16.0,
                      color: Colors.blueAccent,
                    ),
                  ),
                ),
              ),
            ),
          Expanded(
            child: Obx(() {
              return ListView(
                controller: scrollController,
                padding: EdgeInsets.zero,
                children: [
                  ...controller.displayedItems.map((item) {
                    print(controller.displayedItems);
                    final isSelected = selectedItem != null &&
                        item['id'] == selectedItem!['id'];
                    return Container(
                      color: isSelected
                          ? const Color.fromARGB(255, 175, 181, 186)
                          : Colors.transparent,
                      child: ListTile(
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16.0, vertical: 2.0),
                        title: Text(
                          item['label'] ?? '',
                          style: isSelected
                              ? const TextStyle(fontWeight: FontWeight.bold)
                              : null,
                        ),
                        trailing: isSelected
                            ? const Icon(Icons.check, color: Colors.green)
                            : null,
                        onTap: () {
                          onChanged?.call(item);
                          Navigator.of(context).pop();
                        },
                      ),
                    );
                  }),
                  if (controller.displayedItems.isEmpty)
                    const Padding(
                      padding: EdgeInsets.symmetric(vertical: 16.0),
                      child: Center(
                        child: Text('Không tìm thấy mục nào'),
                      ),
                    ),
                ],
              );
            }),
          ),
        ],
      ),
    );
  }
}
