import 'package:get/get.dart';
import 'package:diacritic/diacritic.dart';

class SearchableDropdownController extends GetxController {
  var searchQuery = ''.obs;
  var displayedItems = <Map<String, dynamic>>[].obs;
  final List<Map<String, dynamic>> allItems;

  SearchableDropdownController(this.allItems);

  @override
  void onInit() {
    super.onInit();
    updateSearchQuery('');
  }

  void updateSearchQuery(String query) {
    String normalizedQuery = removeDiacritics(query).toLowerCase();
    searchQuery.value = query;
    if (normalizedQuery.isEmpty) {
      displayedItems.clear();
      displayedItems.addAll(allItems);
    } else {
      displayedItems.clear();
      displayedItems.addAll(allItems.where((item) {
        String label = removeDiacritics(item['label'] ?? '').toLowerCase();
        return label.contains(normalizedQuery);
      }).toList());
    }
  }
}
