// import 'package:get/get.dart';

// class CustomComboController extends GetxController {
//   var selectedItem = Rxn<Map<String, dynamic>>();
//   var warningText = ''.obs;
//   var errorWidget = ''.obs;

//   void setSelectedItem(Map<String, dynamic>? item) {
//     selectedItem.value = item;
//     // // Xóa lỗi khi người dùng đã chọn một mục
//     // errorWidget.value = ''; // Xóa thông báo lỗi
//     update();
//   }

//   void setWarningText(String text) {
//     warningText.value = text;
//   }

//   void setErrorWidget(String text) {
//     errorWidget.value = text;
//   }
// }
import 'package:get/get.dart';

class CustomComboController extends GetxController {
  var selectedItem = Rxn<Map<String, dynamic>>();
  var warningText = ''.obs;
  var errorWidget = ''.obs;

  void setSelectedItem(Map<String, dynamic>? item) {
    selectedItem.value = item;
    // Không cần update() nếu sử dụng obs cho selectedItem
  }

  void setWarningText(String text) {
    warningText.value = text;
  }

  void setErrorWidget(String text) {
    errorWidget.value = text;
  }
}
