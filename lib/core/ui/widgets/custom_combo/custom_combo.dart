// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:ttld_flutter/core/ui/widgets/custom_combo/searchable_dropdown.dart';
// import 'controller/custom_combo_controller.dart';

// class CustomCombo extends StatelessWidget {
//   final CustomComboController? controller;
//   final String title;
//   final bool hideTitle;
//   final TextStyle? titleStyle;
//   final bool isRequired;
//   final String placeholder;
//   final double cornerRadius;
//   final bool isDisabled;
//   final String valiText;
//   final List<Map<String, dynamic>> listItem;
//   final bool showClearButton;
//   final double height;

//   final showIcon = true;

//   // Icon Dropdown
//   final Color iconCbColor;
//   final double iconCbSize;

//   // Icon Clear
//   final Color iconClearColor;
//   final double iconClearSize;

//   // Text combo
//   final Color textCbColor;
//   final double textCbSize;

//   final String dropdownTitle;

//   final Color textTitleCl;

//   const CustomCombo({
//     super.key,
//     required this.title,
//     this.hideTitle = false,
//     this.titleStyle,
//     this.isRequired = false,
//     required this.placeholder,
//     this.cornerRadius = 5.0,
//     this.isDisabled = false,
//     required this.listItem,
//     this.showClearButton = true,
//     this.valiText = 'Vui lòng chọn mục này',
//     this.height = 50,
//     this.iconCbSize = 30,
//     this.textTitleCl = Colors.black,
//     this.textCbColor = Colors.grey,
//     this.textCbSize = 14,
//     this.iconCbColor = Colors.grey,
//     this.iconClearColor = Colors.grey,
//     this.iconClearSize = 30,
//     this.dropdownTitle = 'Chọn một mục',
//     this.controller,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return GetBuilder<CustomComboController>(
//       init: CustomComboController(),
//       global: false,
//       builder: (controller) {
//         return FormField<Map<String, dynamic>>(
//           validator: (value) {
//             if (isRequired && controller.selectedItem.value == null) {
//               return valiText;
//             }
//             return null;
//           },
//           builder: (FormFieldState<Map<String, dynamic>> field) {
//             return Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 if (!hideTitle)
//                   Row(
//                     children: [
//                       Text(
//                         title,
//                         style: titleStyle ??
//                             TextStyle(
//                                 fontSize: textCbSize,
//                                 fontWeight: FontWeight.bold,
//                                 color: textTitleCl),
//                       ),
//                       if (isRequired)
//                         const Text('*',
//                             style: TextStyle(color: Colors.red, fontSize: 18)),
//                     ],
//                   ),
//                 const SizedBox(height: 4),
//                 Opacity(
//                   opacity: isDisabled ? 0.6 : 1.0,
//                   child: AbsorbPointer(
//                     absorbing: isDisabled,
//                     child: Container(
//                       alignment: Alignment.center,
//                       child: ElevatedButton(
//                         style: ElevatedButton.styleFrom(
//                             backgroundColor: isDisabled
//                                 ? Colors.grey.shade300
//                                 : Colors.white,
//                             shape: RoundedRectangleBorder(
//                               borderRadius: BorderRadius.circular(cornerRadius),
//                               side: BorderSide(
//                                 color: field.hasError
//                                     ? Colors.red
//                                     : const Color.fromARGB(255, 138, 135, 135),
//                                 width: 0.8,
//                               ),
//                             ),
//                             minimumSize: Size.fromHeight(height),
//                             elevation: 0),
//                         onPressed: isDisabled
//                             ? null
//                             : () async {
//                                 final selected = await showModalBottomSheet<
//                                     Map<String, dynamic>>(
//                                   context: context,
//                                   isScrollControlled: true,
//                                   backgroundColor: Colors.transparent,
//                                   builder: (context) {
//                                     return GestureDetector(
//                                       onTap: () {
//                                         Navigator.of(context)
//                                             .pop(); // Đóng modal khi bấm khoảng trắng
//                                       },
//                                       child: DraggableScrollableSheet(
//                                         initialChildSize: 0.92,
//                                         minChildSize: 0.3,
//                                         maxChildSize: 0.92,
//                                         builder: (BuildContext context,
//                                             ScrollController scrollController) {
//                                           return Container(
//                                             decoration: const BoxDecoration(
//                                               color: Colors.white,
//                                               borderRadius:
//                                                   BorderRadius.vertical(
//                                                       top: Radius.circular(
//                                                           18.0)),
//                                             ),
//                                             child: GestureDetector(
//                                               onTap: () {
//                                                 // Ngăn không cho đóng khi bấm vào nội dung bên trong
//                                               },
//                                               child: Column(
//                                                 children: [
//                                                   Container(
//                                                     height: 5,
//                                                     width: 100,
//                                                     decoration: BoxDecoration(
//                                                       color: Colors.grey,
//                                                       borderRadius:
//                                                           BorderRadius.circular(
//                                                               5),
//                                                     ),
//                                                     margin: const EdgeInsets
//                                                         .symmetric(
//                                                         vertical: 10),
//                                                   ),
//                                                   Expanded(
//                                                     child:
//                                                         SingleChildScrollView(
//                                                       controller:
//                                                           scrollController,
//                                                       child: SearchableDropdown(
//                                                         dropdownTitle:
//                                                             dropdownTitle,
//                                                         items: listItem,
//                                                         selectedItem: controller
//                                                             .selectedItem.value,
//                                                         onChanged: (value) {
//                                                           if (value != null) {
//                                                             controller
//                                                                 .setSelectedItem(
//                                                                     value);
//                                                             field.didChange(
//                                                                 value);
//                                                           }
//                                                         },
//                                                       ),
//                                                     ),
//                                                   ),
//                                                 ],
//                                               ),
//                                             ),
//                                           );
//                                         },
//                                       ),
//                                     );
//                                   },
//                                 );
//                               },
//                         child: Row(
//                           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                           children: [
//                             Expanded(
//                               child: Text(
//                                 controller.selectedItem.value?['label'] ??
//                                     placeholder,
//                                 style: TextStyle(color: textCbColor),
//                               ),
//                             ),
//                             if (showClearButton &&
//                                 controller.selectedItem.value != null)
//                               IconButton(
//                                 iconSize: iconClearSize,
//                                 color: iconClearColor,
//                                 icon: const Icon(Icons.clear),
//                                 onPressed: () {
//                                   // print(listItem);
//                                   controller.setSelectedItem(null);
//                                   field.didChange(null);
//                                 },
//                               ),
//                             if (!(showClearButton &&
//                                 controller.selectedItem.value != null))
//                               Icon(
//                                 size: iconCbSize,
//                                 Icons.arrow_drop_down,
//                                 color: iconCbColor,
//                               ),
//                           ],
//                         ),
//                       ),
//                     ),
//                   ),
//                 ),
//                 if (field.hasError)
//                   Padding(
//                     padding: const EdgeInsets.only(top: 5),
//                     child: Text(
//                       field.errorText!,
//                       style: const TextStyle(color: Colors.red),
//                     ),
//                   ),
//               ],
//             );
//           },
//         );
//       },
//     );
//   }
// }

// class ItemModel {
//   final String id;
//   final String label;

//   ItemModel({required this.id, required this.label});
// }

import 'package:flutter/material.dart';

class CustomCombo extends StatefulWidget {
  final String title;
  final bool hideTitle;
  final TextStyle? titleStyle;
  final bool isRequired;
  final String placeholder;
  final double cornerRadius;
  final bool isDisabled;
  final String valiText;
  final List<dynamic> listItem;
  final bool showClearButton;
  final double height;
  final Color iconCbColor;
  final double iconCbSize;
  final Color iconClearColor;
  final double iconClearSize;
  final Color textCbColor;
  final double textCbSize;
  final String dropdownTitle;
  final Color textTitleCl;
  final Function? onChangre;

  const CustomCombo({
    super.key,
    this.onChangre,
    required this.title,
    this.hideTitle = false,
    this.titleStyle,
    this.isRequired = false,
    required this.placeholder,
    this.cornerRadius = 5.0,
    this.isDisabled = false,
    required this.listItem,
    this.showClearButton = true,
    this.valiText = 'Vui lòng chọn mục này',
    this.height = 50,
    this.iconCbSize = 30,
    this.textTitleCl = Colors.black,
    this.textCbColor = Colors.grey,
    this.textCbSize = 14,
    this.iconCbColor = Colors.grey,
    this.iconClearColor = Colors.grey,
    this.iconClearSize = 30,
    this.dropdownTitle = 'Chọn một mục',
  });

  @override
  _CustomComboState createState() => _CustomComboState();
}

class _CustomComboState extends State<CustomCombo> {
  Map<String, dynamic>? selectedItem;
  List<dynamic> displayedItems = [];

  @override
  void initState() {
    super.initState();
    // Hiển thị tất cả các mục khi khởi tạo
    displayedItems = widget.listItem;
  }

  // Hàm này sẽ được gọi khi có thay đổi trong TextField tìm kiếm
  void updateSearchQuery(String query) {
    setState(() {
      if (query.isEmpty) {
        // Nếu không có truy vấn tìm kiếm, hiển thị lại toàn bộ danh sách
        displayedItems = widget.listItem;
      } else {
        // Lọc danh sách dựa trên truy vấn tìm kiếm
        displayedItems = widget.listItem
            .where((item) => (item['label'] ?? '')
                .toLowerCase()
                .contains(query.toLowerCase()))
            .toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return FormField<Map<String, dynamic>>(
      validator: (value) {
        if (widget.isRequired && selectedItem == null) {
          return widget.valiText;
        }
        return null;
      },
      builder: (FormFieldState<Map<String, dynamic>> field) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (!widget.hideTitle)
              Row(
                children: [
                  Text(
                    widget.title,
                    style: widget.titleStyle ??
                        TextStyle(
                          fontSize: widget.textCbSize,
                          fontWeight: FontWeight.bold,
                          color: widget.textTitleCl,
                        ),
                  ),
                  if (widget.isRequired)
                    const Text('*',
                        style: TextStyle(color: Colors.red, fontSize: 18)),
                ],
              ),
            const SizedBox(height: 4),
            Opacity(
              opacity: widget.isDisabled ? 0.6 : 1.0,
              child: AbsorbPointer(
                absorbing: widget.isDisabled,
                child: Container(
                  alignment: Alignment.center,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: widget.isDisabled
                          ? Colors.grey.shade300
                          : Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.circular(widget.cornerRadius),
                        side: BorderSide(
                          color: field.hasError
                              ? Colors.red
                              : const Color.fromARGB(255, 138, 135, 135),
                          width: 0.8,
                        ),
                      ),
                      minimumSize: Size.fromHeight(widget.height),
                      elevation: 0,
                    ),
                    onPressed: widget.isDisabled
                        ? null
                        : () async {
                            final selected = await showModalBottomSheet<
                                Map<String, dynamic>>(
                              context: context,
                              isScrollControlled: true,
                              backgroundColor: Colors.transparent,
                              builder: (context) {
                                return GestureDetector(
                                  onTap: () {
                                    Navigator.of(context)
                                        .pop(); // Đóng modal khi bấm khoảng trắng
                                  },
                                  child: DraggableScrollableSheet(
                                    initialChildSize: 0.92,
                                    minChildSize: 0.3,
                                    maxChildSize: 0.92,
                                    builder: (BuildContext context,
                                        ScrollController scrollController) {
                                      return Container(
                                        decoration: const BoxDecoration(
                                          color: Colors.white,
                                          borderRadius: BorderRadius.vertical(
                                              top: Radius.circular(18.0)),
                                        ),
                                        child: Column(
                                          children: [
                                            Container(
                                              height: 5,
                                              width: 100,
                                              decoration: BoxDecoration(
                                                color: Colors.grey,
                                                borderRadius:
                                                    BorderRadius.circular(5),
                                              ),
                                              margin:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 10),
                                            ),
                                            // TextField tìm kiếm
                                            Padding(
                                              padding: const EdgeInsets.all(16),
                                              child: TextField(
                                                decoration: InputDecoration(
                                                  labelText: 'Tìm kiếm',
                                                  border: OutlineInputBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8.0),
                                                  ),
                                                  suffixIcon:
                                                      const Icon(Icons.search),
                                                ),
                                                onChanged: (query) {
                                                  print(query);
                                                  updateSearchQuery(
                                                      query); // Cập nhật tìm kiếm
                                                },
                                              ),
                                            ),
                                            // Danh sách hiển thị sau khi tìm kiếm
                                            Expanded(
                                              child: ListView.builder(
                                                controller: scrollController,
                                                itemCount:
                                                    displayedItems.length,
                                                itemBuilder: (context, index) {
                                                  final item =
                                                      displayedItems[index];
                                                  final isSelected =
                                                      selectedItem != null &&
                                                          item['id'] ==
                                                              selectedItem![
                                                                  'id'];
                                                  return Container(
                                                    color: isSelected
                                                        ? const Color.fromARGB(
                                                            255, 175, 181, 186)
                                                        : Colors.transparent,
                                                    child: ListTile(
                                                      title: Text(
                                                          item['label'] ?? ''),
                                                      trailing: isSelected
                                                          ? const Icon(
                                                              Icons.check,
                                                              color:
                                                                  Colors.green)
                                                          : null,
                                                      onTap: () {
                                                        setState(() {
                                                          selectedItem = item;
                                                        });
                                                        Navigator.of(context)
                                                            .pop();
                                                        field.didChange(item);
                                                      },
                                                    ),
                                                  );
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                                );
                              },
                            );
                          },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            selectedItem?['label'] ?? widget.placeholder,
                            style: TextStyle(color: widget.textCbColor),
                          ),
                        ),
                        if (widget.showClearButton && selectedItem != null)
                          IconButton(
                            iconSize: widget.iconClearSize,
                            color: widget.iconClearColor,
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              setState(() {
                                selectedItem = null;
                              });
                              field.didChange(null);
                            },
                          ),
                        if (!(widget.showClearButton && selectedItem != null))
                          Icon(
                            size: widget.iconCbSize,
                            Icons.arrow_drop_down,
                            color: widget.iconCbColor,
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            if (field.hasError)
              Padding(
                padding: const EdgeInsets.only(top: 5),
                child: Text(
                  field.errorText!,
                  style: const TextStyle(color: Colors.red),
                ),
              ),
          ],
        );
      },
    );
  }
}
