import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';

import '../../../configs/dimens/app_dimens.dart';

class YearPickerWidget extends StatefulWidget {
  final String title;

  const YearPickerWidget({super.key, required this.title});

  @override
  // ignore: library_private_types_in_public_api
  _YearPickerWidgetState createState() => _YearPickerWidgetState();
}

class _YearPickerWidgetState extends State<YearPickerWidget> {
  int selectedYear = 2024;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(widget.title,
            style: const TextStyle(
                fontSize: AppDimens.sizeTitle,
                fontWeight: FontWeight.w500,
                color: AppColors.black)),
        const SizedBox(height: 4),
        GestureDetector(
          onTap: () => _selectYear(context),
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xffC6C6C6)),
              borderRadius: BorderRadius.circular(7),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Icon(
                      CupertinoIcons.calendar_circle_fill,
                      size: 20,
                      color: AppColors.iconColors,
                    ),
                    const SizedBox(
                      width: 13,
                    ),
                    Text('$selectedYear',
                        style: const TextStyle(fontSize: AppDimens.sizeText)),
                  ],
                ),
                const Icon(
                  CupertinoIcons.chevron_down,
                  size: 15,
                  color: const Color(0xff585858),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _selectYear(BuildContext context) async {
    final int? year = await showDialog<int>(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.all(4.w),
                child: const Center(
                  child: Text(
                    'Chọn năm',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: List.generate(6, (index) {
                  final yearValue = 2020 + index;
                  return SimpleDialogOption(
                    onPressed: () {
                      Navigator.pop(context, yearValue);
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: Text(
                        yearValue.toString(),
                        style: const TextStyle(
                          fontSize: 18,
                        ),
                      ),
                    ),
                  );
                }),
              ),
            ],
          ),
        );
      },
    );

    if (year != null && year != selectedYear) {
      setState(() {
        selectedYear = year;
      });
    }
  }
}
