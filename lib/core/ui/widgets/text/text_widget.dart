// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:attp_2024/core/configs/dimens/app_dimens.dart';

// class TextWidget extends StatelessWidget {
//   final String text;
//   final double? size;
//   final Color? color;
//   final int? maxLines;
//   final TextAlign? textAlign;
//   final FontWeight? fontWeight;
//   final List<Shadow>? listShadow;
//   final TextDecoration? textDecoration;
//   final FontStyle? fontStyle;
//   final String? fontFamily; // Add the fontFamily parameter
//   final int? wordLimit; // Add wordLimit parameter

//   const TextWidget({
//     super.key,
//     this.textAlign,
//     this.listShadow,
//     this.maxLines = 1000,
//     required this.text,
//     this.color,
//     this.size = AppDimens.textStandard,
//     this.fontWeight = FontWeight.normal,
//     this.fontStyle = FontStyle.normal,
//     this.textDecoration = TextDecoration.none,
//     this.fontFamily, // Initialize the fontFamily parameter
//     this.wordLimit, // Initialize the wordLimit parameter
//   });

//   String _getLimitedText(String text, int? wordLimit) {
//     if (wordLimit == null || wordLimit <= 0) {
//       return text; // Trả về chuỗi gốc nếu không có giới hạn từ
//     }

//     List<String> words = text.split(' '); // Chia chuỗi thành danh sách từ
//     if (words.length > wordLimit) {
//       return words.take(wordLimit).join(' ') +
//           '...'; // Lấy số từ theo giới hạn và thêm ...
//     }
//     return text; // Trả về chuỗi gốc nếu không vượt quá giới hạn
//   }

//   @override
//   Widget build(BuildContext context) {
//     String displayedText =
//         _getLimitedText(text.tr, wordLimit); // Giới hạn hiển thị theo số từ
//     return Text(
//       displayedText,
//       maxLines: maxLines,
//       textAlign: textAlign,
//       style: TextStyle(
//         color: color,
//         fontSize: size,
//         fontStyle: fontStyle,
//         shadows: listShadow,
//         fontWeight: fontWeight,
//         decoration: textDecoration,
//         overflow: TextOverflow.ellipsis,
//       ),
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';

class TextWidget extends StatelessWidget {
  final String text;
  final double? size;
  final Color? color;
  final int? maxLines; // Giới hạn số dòng
  final TextAlign? textAlign;
  final FontWeight? fontWeight;
  final List<Shadow>? listShadow;
  final TextDecoration? textDecoration;
  final FontStyle? fontStyle;
  final String? fontFamily;
  final int? wordLimit; // Giới hạn số từ

  const TextWidget({
    super.key,
    required this.text,
    this.size = AppDimens.textStandard,
    this.color,
    this.maxLines = 1000,
    this.textAlign,
    this.fontWeight = FontWeight.normal,
    this.fontStyle = FontStyle.normal,
    this.textDecoration = TextDecoration.none,
    this.fontFamily,
    this.wordLimit,
    this.listShadow,
  });

  // Hàm giới hạn số từ
  String _getLimitedText(String text, int? wordLimit) {
    if (wordLimit == null || wordLimit <= 0) return text;

    final words = text.split(' ');
    if (words.length > wordLimit) {
      return '${words.take(wordLimit).join(' ')}...';
    }
    return text;
  }

  @override
  Widget build(BuildContext context) {
    final displayedText = _getLimitedText(text.tr, wordLimit);

    return Text(
      displayedText,
      maxLines: maxLines, // Số dòng tối đa
      overflow: TextOverflow.ellipsis, // Hiển thị "..." nếu vượt quá số dòng
      textAlign: textAlign,
      style: TextStyle(
        color: color,
        fontSize: size,
        fontWeight: fontWeight,
        fontStyle: fontStyle,
        decoration: textDecoration,
        shadows: listShadow,
        fontFamily: fontFamily,
      ),
    );
  }
}
