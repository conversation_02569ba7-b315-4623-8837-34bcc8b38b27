import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class AppBarWidget extends StatelessWidget implements PreferredSizeWidget {
  final double? height;
  final String? title;
  final List<Widget>? actions;
  final Color? backgroundColor;
  final VoidCallback? onTapIconAction;
  final Widget? leading;
  final bool? centerTitle;
  final VoidCallback? callbackLeading;
  final Color titleColor;
  final double? titleSize;
  final Color arrowBackColor;
  final PreferredSizeWidget? bottom;
  final bool? hiddenLeading;

  const AppBarWidget(
      {super.key,
      this.height = 60,
      this.title,
      this.titleSize = AppDimens.textAppBarSize,
      this.actions,
      this.backgroundColor,
      this.onTapIconAction,
      this.leading,
      this.centerTitle = false,
      this.titleColor = Colors.white,
      this.arrowBackColor = Colors.white,
      this.bottom,
      this.callbackLeading,
      this.hiddenLeading});

  @override
  Size get preferredSize => Size.fromHeight(height!);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      elevation: 0.0,
      centerTitle: centerTitle,
      backgroundColor: AppColors.primary,
      title: title != null
          ? TextWidget(
              text: title!,
              fontWeight: FontWeight.bold,
              size: titleSize ?? AppDimens.textTitleAppbar.sp,
              color: titleColor,
            )
          : const SizedBox.shrink(),
      titleSpacing: 0.0,
      leading: (hiddenLeading == true)
          ? null // Không hiển thị nút leading nếu hiddenLeading là true
          : leading ??
              InkWell(
                onTap: callbackLeading ?? () => Get.back(),
                child: Icon(
                  size: 18.sp,
                  Icons.arrow_back_ios_new,
                  color: arrowBackColor,
                ),
              ),
      automaticallyImplyLeading: hiddenLeading != true,
      actions: actions ?? [],
      bottom: bottom,
    );
  }
}
