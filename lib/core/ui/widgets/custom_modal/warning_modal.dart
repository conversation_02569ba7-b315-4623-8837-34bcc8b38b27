import 'package:flutter/material.dart';
import 'package:attp_2024/app.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';

class WarningModalWidget extends StatelessWidget {
  final VoidCallback onConfirm;
  final VoidCallback onClose;
  final VoidCallback onClear;
  final String title;
  final String contentText;
  final Color? titleColor;
  final double? radius;

  const WarningModalWidget(
      {Key? key,
      required this.onConfirm,
      required this.onClose,
      required this.onClear,
      required this.title,
      required this.contentText,
      this.titleColor,
      this.radius})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
      titlePadding: EdgeInsets.zero,
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
      actionsPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      title: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 10.0),
            child: Center(
              child: Text(
                title, // <PERSON><PERSON>n thị tiêu đề được truyền vào
                style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: titleColor ?? AppColors.error),
              ),
            ),
          ),
          Positioned(
            right: 10,
            top: 10,
            child: IconButton(
              icon: const Icon(Icons.clear),
              onPressed: onClear,
            ),
          ),
        ],
      ),
      content: Text(
        contentText,
        style: TextStyle(fontSize: 16),
      ),
      actions: [
        ElevatedButton(
          style: ButtonStyle(
            side: MaterialStateProperty.all<BorderSide>(
                BorderSide(color: AppColors.green)),
            shape: MaterialStateProperty.all<RoundedRectangleBorder>(
              RoundedRectangleBorder(
                borderRadius:
                    BorderRadius.circular(12), // Bán kính bo góc là 12
              ),
            ),
          ),
          onPressed: onConfirm,
          child: const Text('Xác nhận'),
        ),
        ElevatedButton(
          style: ButtonStyle(
            shape: MaterialStateProperty.all<RoundedRectangleBorder>(
              RoundedRectangleBorder(
                borderRadius:
                    BorderRadius.circular(12), // Bán kính bo góc là 12
              ),
            ),
            backgroundColor:
                MaterialStateProperty.all<Color>(titleColor ?? AppColors.error),
          ),
          onPressed: onClose,
          child: Text(
            'Đóng',
            style: TextStyle(color: AppColors.white),
          ),
        ),
      ],
    );
  }
}
