import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';

class CustomCheckBox extends StatelessWidget {
  final String? label;
  final double? labelSize;
  final String? warningMessage;
  final String? errorMessage;
  final Color checkedIconColor;
  final Color checkedFillColor;
  final IconData checkedIcon;
  final Color uncheckedIconColor;
  final Color uncheckedFillColor;
  final IconData uncheckedIcon;
  final double? borderWidth;
  final double? checkBoxSize;
  final bool shouldShowBorder;
  final Color? borderColor;
  final double? borderRadius;
  final double? splashRadius;
  final Color? splashColor;
  final String? tooltip;
  final MouseCursor? mouseCursors;
  final Color? colorLabel;
  final RxBool checkValue;

  const CustomCheckBox({
    super.key,
    this.label,
    this.labelSize,
    this.warningMessage,
    this.errorMessage,
    this.checkedIconColor = Colors.white,
    this.checkedFillColor = const Color.fromARGB(255, 4, 110, 79),
    this.checkedIcon = Icons.check,
    this.uncheckedIconColor = AppColors.white,
    this.uncheckedFillColor = AppColors.white,
    this.uncheckedIcon = Icons.close,
    this.borderWidth,
    this.checkBoxSize,
    this.shouldShowBorder = false,
    this.borderColor,
    this.borderRadius = 3,
    this.splashRadius,
    this.splashColor,
    this.tooltip,
    this.mouseCursors,
    this.colorLabel,
    required this.checkValue,
  });

  Widget _buildIcon() {
    final fillColor = checkValue.value ? checkedFillColor : uncheckedFillColor;
    final iconColor = checkValue.value ? checkedIconColor : uncheckedIconColor;
    final iconData = checkValue.value ? checkedIcon : uncheckedIcon;

    return Container(
      padding: EdgeInsets.zero,
      decoration: BoxDecoration(
        color: fillColor,
        borderRadius: BorderRadius.all(Radius.circular(borderRadius ?? 6)),
        border: Border.all(
          color: shouldShowBorder
              ? (borderColor ?? AppColors.primary.withOpacity(0.6))
              : Colors.grey,
          width: shouldShowBorder ? borderWidth ?? 2.0 : 1.0,
        ),
      ),
      child: Icon(
        iconData,
        color: iconColor,
        size: checkBoxSize ?? 18,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              InkWell(
                onTap: () {
                  checkValue.toggle();
                },
                child: _buildIcon(),
              ),
              label != null
                  ? Padding(
                      padding: const EdgeInsets.only(left: 10),
                      child: TextWidget(
                        text: label ?? "",
                        size: labelSize ?? 16,
                        color: colorLabel,
                      ),
                    )
                  : const SizedBox()
            ],
          ),
          if (warningMessage != null)
            Padding(
              padding: EdgeInsets.only(
                  left: checkBoxSize != null ? (checkBoxSize! * 1.5) : 0),
              child: TextWidget(
                text: warningMessage ?? "",
                color: AppColors.warning,
                size: labelSize ?? 12,
              ),
            ),
          if (errorMessage != null)
            Padding(
              padding: EdgeInsets.only(
                  left: checkBoxSize != null ? (checkBoxSize! * 2) : 0),
              child: TextWidget(
                text: errorMessage ?? "",
                color: AppColors.error,
                size: labelSize ?? 12,
              ),
            ),
        ],
      );
    });
  }
}
