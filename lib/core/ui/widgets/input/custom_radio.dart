import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';

class CustomRadioController extends GetxController {
  RxString selectedValue = ''.obs;

  void setDefaultValue(String value) {
    selectedValue.value = value;
  }

  void updateValue(String value) {
    selectedValue.value = value;
  }
}

class CustomRadio extends StatelessWidget {
  final String text;
  final TextStyle? textStyle;
  final bool isDisabled;
  final String value;
  final String? warningText;
  final String? errorWidget;
  final Function(String) onChange;
  final double radioSize;
  final RadioShape shape;
  final Color? checkColors;
  final String? defaultValue;

  CustomRadio({
    super.key,
    required this.text,
    required this.value,
    required this.onChange,
    this.textStyle,
    this.isDisabled = false,
    this.warningText,
    this.errorWidget,
    this.shape = RadioShape.circle,
    this.radioSize = 18,
    this.checkColors = Colors.grey,
    this.defaultValue,
  }) {
    if (defaultValue != null) {
      _controller.setDefaultValue(defaultValue!);
    }
  }

  final CustomRadioController _controller = Get.put(CustomRadioController());

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: isDisabled
          ? null
          : () {
              if (_controller.selectedValue.value == value) {
                _controller.updateValue('');
                onChange('');
              } else {
                _controller.updateValue(value);
                onChange(value);
              }
            },
      child: Obx(
        () => Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                _buildRadioButton(),
                const SizedBox(width: 8),
                Text(text,
                    style: textStyle ??
                        const TextStyle(
                            fontSize: AppDimens.textStandard,
                            color: CupertinoColors.darkBackgroundGray)),
              ],
            ),
            if (warningText != null)
              Padding(
                padding: const EdgeInsets.only(left: 24),
                child: Text(
                  warningText ?? "",
                  style: const TextStyle(
                      color: Colors.orange, fontSize: AppDimens.textSize12),
                ),
              ),
            if (errorWidget != null)
              Padding(
                padding: const EdgeInsets.only(left: 24),
                child: Text(
                  errorWidget ?? "",
                  style: const TextStyle(
                      color: Colors.red, fontSize: AppDimens.textSize12),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildRadioButton() {
    final bool isSelected = _controller.selectedValue.value == value;

    return Container(
      height: radioSize,
      width: radioSize,
      decoration: BoxDecoration(
        shape:
            shape == RadioShape.circle ? BoxShape.circle : BoxShape.rectangle,
        border: Border.all(
            color: (isSelected ? checkColors : Colors.black) ?? AppColors.black,
            width: 1),
        borderRadius: shape == RadioShape.roundRectangle
            ? BorderRadius.circular(4)
            : null,
      ),
      child: isSelected
          ? Center(
              child: Container(
                height: radioSize / 2,
                width: radioSize / 2,
                decoration: BoxDecoration(
                  shape: shape == RadioShape.circle
                      ? BoxShape.circle
                      : BoxShape.rectangle,
                  color: Colors.black,
                  borderRadius: shape == RadioShape.roundRectangle
                      ? BorderRadius.circular(4)
                      : null,
                ),
              ),
            )
          : null,
    );
  }
}

enum RadioShape { roundRectangle, circle }
