import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SwitchCustom extends StatelessWidget {
  final Color activeTrackColor; // <PERSON><PERSON><PERSON> nền khi bật nút
  final Color inactiveTrackColor; // <PERSON><PERSON><PERSON> nền trước khi bật nút
  final Color activeThumbColor; // <PERSON>àu nút sau khi bật
  final Color inactiveThumbColor; // Màu nút trước khi bật
  final Color?
      borderColor; // Màu viền của ô, nếu null hoặc transparent thì bỏ viền
  final double switchWidth; // Chiều rộng của switch
  final double switchHeight; // <PERSON><PERSON><PERSON> cao của switch
  final double thumbSize; // <PERSON><PERSON>ch thước của nút tròn
  final Function(bool)? onChange; // Hàm callback khi trạng thái thay đổi
  final RxBool isActive; // Trạng thái hiện tại của switch

  const SwitchCustom({
    super.key,
    this.activeTrackColor = Colors.green,
    this.inactiveTrackColor = Colors.grey,
    this.activeThumbColor = Colors.white,
    this.inactiveThumbColor = Colors.white,
    this.borderColor = Colors.transparent,
    this.switchWidth = 60.0,
    this.switchHeight = 30.0,
    this.thumbSize = 26.0,
    this.onChange,
    required this.isActive,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // Đảo ngược trạng thái hiện tại
        bool newState = !isActive.value;
        if (onChange != null) {
          onChange!(newState); // Gọi callback với trạng thái mới
        }
      },
      child: Obx(() => AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            width: switchWidth,
            height: switchHeight,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              color: isActive.value ? activeTrackColor : inactiveTrackColor,
              border: borderColor != null && borderColor != Colors.transparent
                  ? Border.all(
                      color: borderColor!,
                      width: 2.0,
                    )
                  : null, // Không có viền nếu borderColor là null hoặc transparent
            ),
            child: Stack(
              children: [
                AnimatedAlign(
                  alignment: isActive.value
                      ? Alignment.centerRight
                      : Alignment.centerLeft,
                  duration: const Duration(milliseconds: 300),
                  child: Padding(
                    padding: const EdgeInsets.all(3.0),
                    child: Container(
                      width: thumbSize,
                      height: thumbSize,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isActive.value
                            ? activeThumbColor
                            : inactiveThumbColor,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          )),
    );
  }
}
