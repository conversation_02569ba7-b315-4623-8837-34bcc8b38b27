import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/ui/widgets/load/custom_empty_load.dart';

import 'load_more_controller.dart';

class LoadMoreWidget extends StatelessWidget {
  final int loadBatchSize;
  final List<dynamic> listData; // Nhận danh sách các widget
  final Widget Function(dynamic) itemBuilder;

  const LoadMoreWidget(
      {super.key,
      required this.loadBatchSize,
      required this.listData,
      required this.itemBuilder});

  @override
  Widget build(BuildContext context) {
    LoadMoreController controller = Get.put(LoadMoreController(
      loadBatchSize: loadBatchSize,
      listData: listData,
    ));

    return controller.maxData == 0
        ? const CustomEmptyLoad()
        : Obx(() {
            return ListView.builder(
              controller: ScrollController()
                ..addListener(() {
                  if (!controller.isLoading.value &&
                      (controller.items.length < controller.maxData)) {
                    controller.loadMoreData();
                  }
                }),
              itemCount: controller.items.length +
                  (controller.isLoading.value ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == controller.items.length) {
                  return const Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Center(child: CircularProgressIndicator()),
                  );
                }
                return itemBuilder(
                    controller.items[index]); // Render item với hàm itemBuilder
              },
            );
          });
  }
}
