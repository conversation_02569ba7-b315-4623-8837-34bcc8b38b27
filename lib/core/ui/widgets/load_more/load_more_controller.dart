import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LoadMoreController extends GetxController {
  var items = <Widget>[].obs;
  var isLoading = false.obs;
  int loadedDataCount = 0;
  final List<dynamic> listData;
  late final int loadBatchSize;
  late final int maxData;

  LoadMoreController({
    required this.loadBatchSize,
    required this.listData,
  });

  @override
  void onInit() {
    super.onInit();
    maxData = listData.length;
    loadMoreData();
  }

  Future<void> loadMoreData() async {
    if (isLoading.value || loadedDataCount >= maxData) return;
    isLoading.value = true;
    await Future.delayed(const Duration(seconds: 2));
    List<Widget> newItems = List.generate(loadBatchSize, (index) {
      int itemIndex = loadedDataCount + index;
      if (itemIndex < maxData) {
        final data = listData[itemIndex];
        return data;
      } else {
        return Container();
      }
    });

    items.addAll(newItems);
    loadedDataCount += loadBatchSize;
    isLoading.value = false;
  }
}
