import 'package:attp_2024/core/ui/widgets/videoplayer/video_player.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

class MediaCarousel extends StatefulWidget {
  final List<String> mediaUrls;
  final int styleBottom; // 0: <PERSON>hông có gì, 1: <PERSON><PERSON> các nốt nhỏ bên dưới
  final bool isNavigator; // true: Hiển thị nút trái/phải

  const MediaCarousel({
    Key? key,
    required this.mediaUrls,
    this.styleBottom = 0,
    this.isNavigator = false,
  }) : super(key: key);

  @override
  _MediaCarouselState createState() => _MediaCarouselState();
}

class _MediaCarouselState extends State<MediaCarousel> {
  late final List<Widget> mediaWidgets;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    mediaWidgets = widget.mediaUrls.map((url) {
      if (url.endsWith(".mp4") || url.endsWith(".mov")) {
        return VideoPlayerWidget(videoUrl: url);
      } else {
        return Image.network(url, fit: BoxFit.cover);
      }
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Stack(
          children: [
            CarouselSlider(
              options: CarouselOptions(
                height: 400.0,
                autoPlay: true,
                enlargeCenterPage: true,
                enlargeFactor: 0.2,
                onPageChanged: (index, reason) {
                  setState(() {
                    _currentIndex = index;
                  });
                },
              ),
              items: mediaWidgets,
            ),
            if (widget.isNavigator)
              Positioned(
                left: 0,
                child: IconButton(
                  icon: const Icon(Icons.arrow_back_ios),
                  onPressed: () {
                    setState(() {
                      _currentIndex = (_currentIndex - 1) % mediaWidgets.length;
                    });
                  },
                ),
              ),
            if (widget.isNavigator)
              Positioned(
                right: 0,
                child: IconButton(
                  icon: const Icon(Icons.arrow_forward_ios),
                  onPressed: () {
                    setState(() {
                      _currentIndex = (_currentIndex + 1) % mediaWidgets.length;
                    });
                  },
                ),
              ),
          ],
        ),
        if (widget.styleBottom == 1)
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: widget.mediaUrls.asMap().entries.map((entry) {
              return GestureDetector(
                onTap: () => setState(() {
                  _currentIndex = entry.key;
                }),
                child: Container(
                  width: 8.0,
                  height: 8.0,
                  margin: const EdgeInsets.symmetric(horizontal: 4.0),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color:
                        _currentIndex == entry.key ? Colors.blue : Colors.grey,
                  ),
                ),
              );
            }).toList(),
          ),
      ],
    );
  }
}

class VideoPlayerWidget extends StatefulWidget {
  final String videoUrl;

  const VideoPlayerWidget({
    Key? key,
    required this.videoUrl,
  }) : super(key: key);

  @override
  _VideoPlayerWidgetState createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> {
  late VideoPlayerController _controller;
  bool _isError = false;

  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.network(widget.videoUrl)
      ..initialize().then((_) {
        setState(() {});
      }).catchError((error) {
        setState(() {
          _isError = true;
        });
      });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isError) {
      return const Center(
        child: Text(
          'Không thể tải video',
          style: TextStyle(color: Colors.red),
        ),
      );
    }

    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) =>
                FullScreenVideoPlayer(videoUrl: widget.videoUrl),
          ),
        );
      },
      child: _controller.value.isInitialized
          ? AspectRatio(
              aspectRatio: _controller.value.aspectRatio,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  VideoPlayer(_controller),
                  const Icon(Icons.play_circle_fill,
                      color: Colors.white, size: 50),
                ],
              ),
            )
          : const Center(child: CircularProgressIndicator()),
    );
  }
}
