import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class GroupedBarChart extends StatefulWidget {
  final List<String> labels;
  final List<List<double>> groupValues;
  final String? title;
  final List<Color> groupColors;
  final List<String> groupNames;
  final Function(String label, int groupIndex)? onTapGroup; // Callback khi tap vào nhóm

  const GroupedBarChart({
    super.key,
    required this.labels,
    required this.groupValues,
    this.title,
    required this.groupColors,
    required this.groupNames,
    this.onTapGroup, // Thêm callback xử lý sự kiện onTap
  });

  @override
  State<GroupedBarChart> createState() => _GroupedBarChartState();
}

class _GroupedBarChartState extends State<GroupedBarChart> {
  int? touchedGroupIndex;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (widget.title != null)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Text(
              widget.title!,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ),
        SizedBox(
          height: 250,
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: SizedBox(
              width: widget.labels.length * 100.0,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: GestureDetector(
                  onTapDown: (TapDownDetails details) {
                    if (touchedGroupIndex != null) {
                      widget.onTapGroup?.call(
                        widget.labels[touchedGroupIndex!],
                        touchedGroupIndex!
                      );
                    }
                  },
                  child: BarChart(
                    BarChartData(
                      barTouchData: BarTouchData(
                        touchTooltipData: BarTouchTooltipData(

                        ),
                        touchCallback: (FlTouchEvent event, barTouchResponse) {
                          if (!event.isInterestedForInteractions ||
                              barTouchResponse == null ||
                              barTouchResponse.spot == null) {
                            setState(() {
                              touchedGroupIndex = null;
                            });
                            return;
                          }

                          setState(() {
                            touchedGroupIndex = barTouchResponse.spot!.touchedBarGroupIndex;
                          });
                        },
                      ),
                      titlesData: FlTitlesData(
                        bottomTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true,
                            reservedSize: 30,
                            getTitlesWidget: (value, meta) {
                              return SideTitleWidget(
                                axisSide: meta.axisSide,
                                child: Transform.rotate(
                                  angle: -75 / 180,
                                  child: GestureDetector(
                                    onTap: () {
                                      widget.onTapGroup?.call(widget.labels[value.toInt()], value.toInt());
                                    },
                                    child: Text(
                                      widget.labels[value.toInt()],
                                      style: TextStyle(
                                        fontSize: 11,
                                        fontWeight: touchedGroupIndex == value.toInt()
                                            ? FontWeight.bold
                                            : FontWeight.normal,
                                        color: touchedGroupIndex == value.toInt()
                                            ? Colors.red
                                            : Colors.black,
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                        leftTitles: const AxisTitles(
                          sideTitles: SideTitles(showTitles: true, reservedSize: 30),
                        ),
                        topTitles: const AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                        rightTitles: const AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                      ),
                      borderData: FlBorderData(
                        show: true,
                        border: Border(
                          left: BorderSide(color: Colors.black.withOpacity(0.6)),
                          bottom: BorderSide(color: Colors.black),
                        ),
                      ),
                      barGroups: widget.groupValues.asMap().entries.map((entry) {
                        int index = entry.key;
                        List<double> values = entry.value;
                        return BarChartGroupData(
                          x: index,
                          barsSpace: 3,
                          barRods: values.asMap().entries.map((subEntry) {
                            int subIndex = subEntry.key;
                            double value = subEntry.value;
                            return BarChartRodData(
                              toY: value,
                              color: widget.groupColors[subIndex],
                              width: 12,
                              borderRadius: BorderRadius.zero,
                            );
                          }).toList(),
                        );
                      }).toList(),
                      gridData: const FlGridData(show: true),
                      alignment: BarChartAlignment.spaceAround,
                      groupsSpace: 8.w,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          alignment: WrapAlignment.center,
          spacing: 16,
          children: widget.groupNames.asMap().entries.map((entry) {
            int index = entry.key;
            String name = entry.value;
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 16,
                  height: 16,
                  color: widget.groupColors[index],
                ),
                const SizedBox(width: 8),
                Text(name),
              ],
            );
          }).toList(),
        ),
      ],
    );
  }
}
