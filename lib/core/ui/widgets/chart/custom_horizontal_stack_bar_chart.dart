import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class HorizontalStackedBar<PERSON>hart extends StatefulWidget {
  final List<String> labels;
  final List<List<double>> groupValues;
  final String? title;
  final List<Color> groupColors;
  final List<String> groupNames;
  final Function(String label, int groupIndex)? onTapGroup;

  const HorizontalStackedBarChart({
    super.key,
    required this.labels,
    required this.groupValues,
    this.title,
    required this.groupColors,
    required this.groupNames,
    this.onTapGroup,
  });

  @override
  State<HorizontalStackedBarChart> createState() =>
      _HorizontalStackedBarChartState();
}

class _HorizontalStackedBarChartState extends State<HorizontalStackedBarChart> {
  int? touchedGroupIndex;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (widget.title != null)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Text(
              widget.title!,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ),
        SizedBox(
          height: 475,
          child: RotatedBox(
            quarterTurns: 1,
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: SizedBox(
                width: widget.labels.length * 60.0,
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: GestureDetector(
                    onTapDown: (TapDownDetails details) {
                      if (touchedGroupIndex != null) {
                        widget.onTapGroup?.call(
                          widget.labels[touchedGroupIndex!],
                          touchedGroupIndex!,
                        );
                      }
                    },
                    child: BarChart(
                      BarChartData(
                        barTouchData: BarTouchData(
                          touchTooltipData: BarTouchTooltipData(),
                          touchCallback:
                              (FlTouchEvent event, barTouchResponse) {
                            if (!event.isInterestedForInteractions ||
                                barTouchResponse == null ||
                                barTouchResponse.spot == null) {
                              setState(() {
                                touchedGroupIndex = null;
                              });
                              return;
                            }
                            setState(() {
                              touchedGroupIndex =
                                  barTouchResponse.spot!.touchedBarGroupIndex;
                            });
                          },
                        ),
                        titlesData: FlTitlesData(
                          bottomTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              reservedSize: 100,
                              getTitlesWidget: (value, meta) {
                                return SideTitleWidget(
                                  axisSide: meta.axisSide,
                                  child: Transform.rotate(
                                    angle: -90 / 180 * 3.14159265359,
                                    child: GestureDetector(
                                      onTap: () {
                                        if (widget.onTapGroup != null &&
                                            value.toInt() <
                                                widget.labels.length) {
                                          widget.onTapGroup!(
                                              widget.labels[value.toInt()],
                                              value.toInt());
                                        }
                                      },
                                      child: Align(
                                        alignment: Alignment.center,
                                        child: Text(
                                          value.toInt() < widget.labels.length
                                              ? widget.labels[value.toInt()]
                                              : '',
                                          style: TextStyle(
                                            fontSize: 10,
                                            fontWeight: touchedGroupIndex ==
                                                    value.toInt()
                                                ? FontWeight.bold
                                                : FontWeight.normal,
                                            color: touchedGroupIndex ==
                                                    value.toInt()
                                                ? Colors.red
                                                : Colors.black,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                          leftTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              reservedSize: 30,
                              getTitlesWidget: (value, meta) {
                                return SideTitleWidget(
                                  axisSide: meta.axisSide,
                                  space: 8,
                                  child: Transform.rotate(
                                    angle: -90 * (3.14159265359 / 180),
                                    child: Text(
                                      value.toInt().toString(),
                                      style: const TextStyle(fontSize: 12),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                          topTitles: const AxisTitles(
                            sideTitles: SideTitles(showTitles: false),
                          ),
                          rightTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              reservedSize: 30,
                              getTitlesWidget: (value, meta) {
                                return SideTitleWidget(
                                  axisSide: meta.axisSide,
                                  space: 8,
                                  child: Transform.rotate(
                                    angle: -90 * (3.14159265359 / 180),
                                    child: Text(
                                      value.toInt().toString(),
                                      style: const TextStyle(fontSize: 12),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                        borderData: FlBorderData(
                          show: true,
                          border: Border(
                            left: BorderSide(
                                color: Colors.black.withOpacity(0.6)),
                            bottom: const BorderSide(color: Colors.black),
                          ),
                        ),
                        barGroups:
                            widget.groupValues.asMap().entries.map((entry) {
                          int index = entry.key;
                          List<double> values = entry.value;

                          return BarChartGroupData(
                            x: index,
                            barsSpace: 10,
                            barRods: [
                              BarChartRodData(
                                toY: values.reduce((a, b) => a + b),
                                width: 30,
                                rodStackItems:
                                    values.asMap().entries.map((subEntry) {
                                  int subIndex = subEntry.key;
                                  double value = subEntry.value;
                                  double sumPrev = values
                                      .sublist(0, subIndex)
                                      .fold(0, (a, b) => a + b);

                                  return BarChartRodStackItem(
                                    sumPrev,
                                    sumPrev + value,
                                    widget.groupColors[subIndex],
                                  );
                                }).toList(),
                                borderRadius: BorderRadius.zero,
                              ),
                            ],
                          );
                        }).toList(),
                        gridData: const FlGridData(show: true),
                        alignment: BarChartAlignment.spaceAround,
                        groupsSpace: 10.w,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          alignment: WrapAlignment.center,
          spacing: 16,
          children: widget.groupNames.asMap().entries.map((entry) {
            int index = entry.key;
            String name = entry.value;
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 16,
                  height: 16,
                  color: widget.groupColors[index],
                ),
                const SizedBox(width: 8),
                Text(name),
              ],
            );
          }).toList(),
        ),
      ],
    );
  }
}
