import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

//FILE BIỂU ĐỒ
class BieuDo<PERSON>oi extends StatefulWidget {
  const BieuDoMoi({super.key});

  @override
  State<BieuDoMoi> createState() => _BieuDoMoiState();
}

class _BieuDoMoiState extends State<BieuDoMoi> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          color: const Color(0xFFEFEFEF),
          width: double.infinity,
          child: const Padding(
            padding: EdgeInsets.all(10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('3. Biểu đồ',
                    style: TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                      fontSize: 13,
                    )),
              ],
            ),
          ),
        ),
        Container(
          width: double.infinity,
          child: Center(
            child: PieChartCustom(
              list: [
                {'SoLuong': 3, 'label': 'Tỉnh'},
                {'SoLuong': 4, 'label': 'Huyện'},
                {'SoLuong': 5, 'label': 'Xã'},
              ],
              colors: [Colors.blue, Colors.red, Colors.green],
            ),
          ),
        ),
      ],
    );
  }
}

class PieChartCustom extends StatefulWidget {
  final List<Map<String, dynamic>> list;
  final List<Color> colors;

  PieChartCustom({
    super.key,
    required this.list,
    required this.colors,
  });

  @override
  _PieChartFromAPIState createState() => _PieChartFromAPIState();
}

class _PieChartFromAPIState extends State<PieChartCustom> {
  List<PieChartSectionData> get() {
    List<PieChartSectionData> section = [];
    int number = 0;
    double total = 0;
    for (var item in widget.list) {
      total += item['SoLuong']?.toDouble();
    }
    for (var item in widget.list) {
      section.add(PieChartSectionData(
        color: widget.colors[number],
        value: item['SoLuong']?.toDouble(),
        title: '${item['SoLuong']?.toString()}',
        radius: 30,
        titleStyle: const TextStyle(
          fontSize: 10,
          color: Colors.white,
        ),
      ));
      number++;
    }
    return section;
  }

  @override
  Widget build(BuildContext context) {
    if (widget.list.isEmpty) {
      return const Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          IntrinsicHeight(
            child: Padding(
              padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
              child: Text('Không có dữ liệu'),
            ),
          ),
        ],
      );
    } else {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(10, 10, 10, 10),
            child: SizedBox(
              width: 140,
              height: 140,
              child: PieChart(
                swapAnimationDuration: const Duration(milliseconds: 750),
                swapAnimationCurve: Curves.easeOutQuint,
                PieChartData(
                  sections: get(),
                  borderData: FlBorderData(show: false),
                  sectionsSpace: 0,
                  centerSpaceRadius: 40,
                  startDegreeOffset: 90,
                ),
              ),
            ),
          ),
          const SizedBox(width: 15),
          SizedBox(
            width: 140,
            height: 80,
            child: ListView.builder(
              itemCount: widget.list.length,
              itemBuilder: (context, index) {
                return Row(
                  children: [
                    Icon(Icons.circle, size: 10, color: widget.colors[index]),
                    const SizedBox(width: 5),
                    Text(
                      widget.list[index]['label'],
                      style: const TextStyle(fontSize: 11),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      );
    }
  }
}
