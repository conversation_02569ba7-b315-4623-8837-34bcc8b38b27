import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

class CustomPieChart extends StatefulWidget {
  final List<double> values;
  final List<String> labels;
  final List<String>? legends;
  final String title;
  final List<Color>? colors;
  final double sumary;

  // New adjustable parameters
  final double aspectRatio;
  final double centerSpaceRadius;
  final double sectionRadius;
  final double sectionSpace;
  final double legendSpacing;
  final double fontSize;
  final double legendBoxSize;
  final double legendBorderRadius;
  final double verticalSpace;
  final double horizontalSpace;

  const CustomPieChart(
      {super.key,
      required this.values,
      required this.labels,
      this.legends,
      required this.title,
      required this.sumary,
      this.colors,
      this.aspectRatio = 1.5,
      this.centerSpaceRadius = 30,
      this.sectionRadius = 15,
      this.sectionSpace = 0,
      this.legendSpacing = 8.0,
      this.fontSize = 11.0,
      this.legendBoxSize = 16.0,
      this.legendBorderRadius = 4.0,
      this.horizontalSpace = 10,
      this.verticalSpace = 10});

  @override
  _CustomPieChartState createState() => _CustomPieChartState();
}

class _CustomPieChartState extends State<CustomPieChart> {
  int touchedIndex = -1;

  @override
  Widget build(BuildContext context) {
    double totalValue = widget.values.reduce((a, b) => a + b);

    List<PieChartSectionData> sections =
        widget.values.asMap().entries.map((entry) {
      int index = entry.key;
      double value = entry.value;
      bool isTouched = index == touchedIndex;
      double radius =
          isTouched ? widget.sectionRadius + 10 : widget.sectionRadius;

      return PieChartSectionData(
        value: value,
        showTitle: false,
        color: widget.colors != null && widget.colors!.length > index
            ? widget.colors![index]
            : Colors.primaries[index % Colors.primaries.length],
        radius: radius,
      );
    }).toList();

    return Padding(
      padding: EdgeInsets.symmetric(
          vertical: widget.verticalSpace, horizontal: widget.horizontalSpace),
      child: Column(
        children: [
          Row(
            children: [
              Flexible(
                flex: 1,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          '${widget.sumary.toStringAsFixed(0)}',
                          style: const TextStyle(
                            fontSize: AppDimens.textSize13,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),

                        Text(
                          'Cơ sở',
                          style: TextStyle(
                            fontSize: widget.fontSize * 0.69,
                            color: Colors.grey[999],
                          ),
                        ),
                      ],
                    ),
                    AspectRatio(
                      aspectRatio: widget.aspectRatio,
                      child: PieChart(
                        PieChartData(
                          sections: sections,
                          borderData: FlBorderData(show: false),
                          centerSpaceRadius: widget.centerSpaceRadius,
                          sectionsSpace: widget.sectionSpace,
                          pieTouchData: PieTouchData(
                            touchCallback: (FlTouchEvent event, pieTouchResponse) {
                              setState(() {
                                if (!event.isInterestedForInteractions ||
                                    pieTouchResponse == null ||
                                    pieTouchResponse.touchedSection == null) {
                                  touchedIndex = -1;
                                  return;
                                }
                                touchedIndex =
                                    pieTouchResponse.touchedSection!.touchedSectionIndex;
                              });
                            },
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(width: widget.legendSpacing),
              Flexible(
                flex: 2,
                child: Table(
                  columnWidths: const {
                    0: FixedColumnWidth(20), // Fixed width for the color box column
                    1: FlexColumnWidth(2), // Flexible width for the labels
                  },
                  defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                  children: widget.labels.asMap().entries.map((entry) {
                    int index = entry.key;
                    double value = widget.values[index];
                    return TableRow(
                      children: [
                        Container(
                          height: widget.legendBoxSize,
                          width: widget.legendBoxSize,
                          margin:
                              EdgeInsets.symmetric(horizontal: 2, vertical: 2),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(
                                widget.legendBorderRadius),
                            color: widget.colors != null &&
                                    widget.colors!.length > index
                                ? widget.colors![index]
                                : Colors
                                    .primaries[index % Colors.primaries.length],
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 4),
                          child: Text(
                            widget.labels[index],
                            style: TextStyle(
                              fontSize: widget.fontSize,
                              fontWeight: FontWeight.normal,
                              color: Colors.black,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        SizedBox(),
                        Text(
                          '${(100 * (value / widget.sumary)).toStringAsFixed(0)}%',
                          style: TextStyle(
                            fontSize: widget.fontSize,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                          overflow: TextOverflow.ellipsis,
                        )
                      ],
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
