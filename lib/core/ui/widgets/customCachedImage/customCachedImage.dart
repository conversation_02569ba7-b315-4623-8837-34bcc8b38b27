// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:flutter/material.dart';
// import 'package:shimmer/shimmer.dart';

// class CustomCachedImage extends StatelessWidget {
//   final String? imageUrl; // <PERSON> phép null
//   final String defaultImage; // Ảnh mặc định
//   final double? height;
//   final double? width;
//   final BoxFit fit;
//   final Widget? placeholder;
//   final Widget? errorWidget;
//   final ColorFilter? colorFilter;
//   final Duration fadeInDuration;

//   const CustomCachedImage({
//     super.key,
//     required this.imageUrl,
//     required this.defaultImage, // Bắt buộc phải truyền ảnh mặc định
//     this.height,
//     this.width,
//     this.fit = BoxFit.cover,
//     this.placeholder,
//     this.errorWidget,
//     this.colorFilter,
//     this.fadeInDuration = const Duration(milliseconds: 300),
//   });

//   @override
//   Widget build(BuildContext context) {
//     // <PERSON><PERSON><PERSON> tra URL, nếu không có URL sẽ sử dụng ảnh mặc định
//     final String resolvedImageUrl =
//         imageUrl?.isNotEmpty == true ? imageUrl! : defaultImage;

//     return CachedNetworkImage(
//       imageUrl: resolvedImageUrl,
//       placeholder: (context, url) =>
//           placeholder ??
//           Shimmer.fromColors(
//             baseColor: Colors.grey[300]!,
//             highlightColor: Colors.grey[100]!,
//             child: Container(
//               height: height,
//               width: width,
//               color: Colors.grey[300],
//             ),
//           ),
//       errorWidget: (context, url, error) =>
//           errorWidget ??
//           Container(
//             height: height,
//             width: width,
//             color: Colors.grey[300],
//             child: Image.asset(
//               defaultImage,
//               fit: fit,
//             ),
//           ),
//       imageBuilder: (context, imageProvider) => Container(
//         height: height,
//         width: width,
//         decoration: BoxDecoration(
//           image: DecorationImage(
//             image: imageProvider,
//             fit: fit,
//             colorFilter: colorFilter,
//           ),
//         ),
//       ),
//       fadeInDuration: fadeInDuration,
//     );
//   }
// }
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class CustomCachedImage extends StatelessWidget {
  final String? imageUrl; // URL từ mạng (có thể null)
  final String? localImage; // Đường dẫn tới ảnh trong assets (có thể null)
  final String defaultImage; // Ảnh fallback mặc định
  final double? height;
  final double? width;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final ColorFilter? colorFilter;
  final Duration fadeInDuration;

  const CustomCachedImage({
    super.key,
    this.imageUrl,
    this.localImage,
    required this.defaultImage,
    this.height,
    this.width,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.colorFilter,
    this.fadeInDuration = const Duration(milliseconds: 300),
  });

  @override
  Widget build(BuildContext context) {
    // Ưu tiên hiển thị ảnh local nếu `localImage` không null
    if (localImage != null && localImage!.isNotEmpty) {
      return _buildLocalImage();
    }

    // Nếu URL hợp lệ, hiển thị ảnh từ mạng
    if (imageUrl != null && imageUrl!.isNotEmpty) {
      return _buildNetworkImage();
    }

    // Nếu không có ảnh local hoặc URL, hiển thị ảnh mặc định
    return _buildErrorWidget();
  }

  Widget _buildLocalImage() {
    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage(localImage!),
          fit: fit,
          colorFilter: colorFilter,
        ),
      ),
    );
  }

  Widget _buildNetworkImage() {
    return CachedNetworkImage(
      imageUrl: imageUrl!,
      placeholder: (context, url) =>
          placeholder ??
          Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              height: height,
              width: width,
              color: Colors.grey[300],
            ),
          ),
      errorWidget: (context, url, error) => _buildErrorWidget(),
      imageBuilder: (context, imageProvider) => Container(
        height: height,
        width: width,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: imageProvider,
            fit: fit,
            colorFilter: colorFilter,
          ),
        ),
      ),
      fadeInDuration: fadeInDuration,
    );
  }

  Widget _buildErrorWidget() {
    return errorWidget ??
        Container(
          height: height,
          width: width,
          color: Colors.grey[300],
          child: Image.asset(
            defaultImage,
            fit: fit,
          ),
        );
  }
}
