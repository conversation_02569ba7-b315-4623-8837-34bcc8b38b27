import 'package:flutter/material.dart';

class CustomToggleSwitchWidget extends StatefulWidget {
  final bool initialValue;
  final ValueChanged<bool> onToggle;
  final Color activeColor;
  final Color inactiveColor;
  final Duration animationDuration;
  final double width;
  final double height;
  final double circleSize;

  const CustomToggleSwitchWidget({
    super.key,
    required this.initialValue,
    required this.onToggle,
    this.activeColor = Colors.green,
    this.inactiveColor = Colors.grey,
    this.animationDuration = const Duration(milliseconds: 300),
    this.width = 75.0,
    this.height = 40.0,
    this.circleSize = 40.0,
  });

  @override
  _CustomToggleSwitchState createState() => _CustomToggleSwitchState();
}

class _CustomToggleSwitchState extends State<CustomToggleSwitchWidget> {
  late bool isToggled;

  @override
  void initState() {
    super.initState();
    isToggled = widget.initialValue;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        setState(() {
          isToggled = !isToggled;
        });
        widget.onToggle(isToggled);
      },
      child: AnimatedContainer(
        duration: widget.animationDuration,
        width: widget.width,
        height: widget.height,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(widget.height / 2),
          color: isToggled ? widget.activeColor : widget.inactiveColor,
        ),
        child: Stack(
          children: [
            AnimatedPositioned(
              duration: widget.animationDuration,
              curve: Curves.easeIn,
              left: isToggled ? widget.width - widget.circleSize : 0.0,
              right: isToggled ? 0.0 : widget.width - widget.circleSize,
              child: Container(
                width: widget.circleSize,
                height: widget.circleSize,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
