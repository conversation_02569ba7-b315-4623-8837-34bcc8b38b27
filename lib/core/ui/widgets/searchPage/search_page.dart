// // import 'package:flutter/material.dart';
// // import 'package:get/get.dart';
// // import 'package:responsive_sizer/responsive_sizer.dart';

// // class SearchPage extends StatefulWidget {
// //   final List<dynamic> data;
// //   final Widget Function(dynamic item) searchedItemBuilder;
// //   final String Function(dynamic item) searchByField;
// //   final bool? isInitialDataDisplayed;

// //   const SearchPage({
// //     super.key,
// //     required this.data,
// //     required this.searchedItemBuilder,
// //     required this.searchByField,
// //     this.isInitialDataDisplayed = false,
// //   });

// //   @override
// //   State<SearchPage> createState() => _SearchPageState();
// // }

// // class _SearchPageState extends State<SearchPage> {
// //   final TextEditingController _searchController = TextEditingController();
// //   List<dynamic> _filteredData = [];

// //   @override
// //   void initState() {
// //     super.initState();
// //     _filteredData = widget.isInitialDataDisplayed == true ? widget.data : [];
// //   }

// //   @override
// //   void dispose() {
// //     _searchController.dispose();
// //     super.dispose();
// //   }

// //   void handleSearch(String query) {
// //     setState(() {
// //       if (query == '') {
// //         _filteredData =
// //             widget.isInitialDataDisplayed == true ? widget.data : [];
// //       } else {
// //         _filteredData = widget.data.where((item) {
// //           final searchField = widget.searchByField(item).toLowerCase();
// //           return searchField.contains(query.toLowerCase());
// //         }).toList();
// //       }
// //     });
// //   }

// //   @override
// //   Widget build(BuildContext context) {
// //     return SafeArea(
// //       child: Scaffold(
// //         body: Padding(
// //           padding: const EdgeInsets.all(16.0),
// //           child: Column(
// //             mainAxisAlignment: MainAxisAlignment.start,
// //             crossAxisAlignment: CrossAxisAlignment.start,
// //             children: [
// //               Row(
// //                 children: [
// //                   GestureDetector(
// //                       onTap: Get.back,
// //                       child: const Icon(Icons.arrow_back,
// //                           size: 24.0, color: Colors.black)),
// //                   SizedBox(width: 5.w),
// //                   Expanded(
// //                     child: TextField(
// //                       controller: _searchController,
// //                       onChanged: handleSearch,
// //                       decoration: InputDecoration(
// //                         hintText: 'Tìm kiếm theo tên ...',
// //                         hintStyle: const TextStyle(
// //                           color: Colors.grey,
// //                         ),
// //                         prefixIcon: const Icon(Icons.search),
// //                         border: OutlineInputBorder(
// //                           borderRadius: BorderRadius.circular(8.0),
// //                           borderSide: BorderSide.none,
// //                         ),
// //                         filled: true,
// //                         fillColor: Colors.grey[200],
// //                         contentPadding:
// //                             const EdgeInsets.symmetric(vertical: 8.0),
// //                       ),
// //                     ),
// //                   ),
// //                   const SizedBox(width: 8),
// //                   // ElevatedButton(
// //                   //   onPressed: () => handleSearch(_searchController.text),
// //                   //   style: ElevatedButton.styleFrom(
// //                   //     backgroundColor: AppColors.activeColors,
// //                   //     shape: RoundedRectangleBorder(
// //                   //       borderRadius: BorderRadius.circular(5),
// //                   //     ),
// //                   //     padding: const EdgeInsets.symmetric(
// //                   //       horizontal: 13,
// //                   //       vertical: 13,
// //                   //     ),
// //                   //   ),
// //                   //   child: const Text(
// //                   //     'Tìm kiếm',
// //                   //     style: TextStyle(
// //                   //       color: Colors.white,
// //                   //       fontSize: 14,
// //                   //     ),
// //                   //   ),
// //                   // )
// //                 ],
// //               ),
// //               const SizedBox(height: 20),
// //               Text('Kết quả tìm kiếm (${_filteredData.length})',
// //                   textAlign: TextAlign.left),
// //               const SizedBox(height: 20),
// //               Expanded(
// //                 child: _filteredData.isEmpty
// //                     ? const Center(
// //                         child: Text('Không tìm thấy kết quả'),
// //                       )
// //                     : ListView.builder(
// //                         itemCount: _filteredData.length,
// //                         itemBuilder: (context, index) {
// //                           return widget
// //                               .searchedItemBuilder(_filteredData[index]);
// //                         },
// //                       ),
// //               ),
// //             ],
// //           ),
// //         ),
// //       ),
// //     );
// //   }
// // }

// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:responsive_sizer/responsive_sizer.dart';

// class SearchPage extends StatefulWidget {
//   final List<dynamic> data;
//   final Widget Function(dynamic item) searchedItemBuilder;
//   final String Function(dynamic item) searchByField;
//   final bool? isInitialDataDisplayed;

//   const SearchPage({
//     super.key,
//     required this.data,
//     required this.searchedItemBuilder,
//     required this.searchByField,
//     this.isInitialDataDisplayed = false,
//   });

//   @override
//   State<SearchPage> createState() => _SearchPageState();
// }

// class _SearchPageState extends State<SearchPage> {
//   final TextEditingController _searchController = TextEditingController();
//   List<dynamic> _filteredData = [];

//   @override
//   void initState() {
//     super.initState();
//     _filteredData = widget.isInitialDataDisplayed == true ? widget.data : [];
//   }

//   @override
//   void dispose() {
//     _searchController.dispose();
//     super.dispose();
//   }

//   void handleSearch(String query) {
//     setState(() {
//       if (query == '') {
//         _filteredData =
//             widget.isInitialDataDisplayed == true ? widget.data : [];
//       } else {
//         _filteredData = widget.data.where((item) {
//           final searchField = widget.searchByField(item).toLowerCase();
//           return searchField.contains(query.toLowerCase());
//         }).toList();
//       }
//     });
//   }

//   void clearSearch() {
//     setState(() {
//       _searchController.clear();
//       _filteredData = widget.isInitialDataDisplayed == true ? widget.data : [];
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     return SafeArea(
//       child: Scaffold(
//         body: Padding(
//           padding: const EdgeInsets.all(16.0),
//           child: Column(
//             mainAxisAlignment: MainAxisAlignment.start,
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Row(
//                 children: [
//                   GestureDetector(
//                       onTap: Get.back,
//                       child: const Icon(Icons.arrow_back,
//                           size: 24.0, color: Colors.black)),
//                   SizedBox(width: 5.w),
//                   Expanded(
//                     child: TextField(
//                       controller: _searchController,
//                       onChanged: handleSearch,
//                       decoration: InputDecoration(
//                         hintText: 'Tìm kiếm theo tên ...',
//                         hintStyle: const TextStyle(
//                           color: Colors.grey,
//                         ),
//                         prefixIcon: const Icon(Icons.search),
//                         suffixIcon: _searchController.text.isNotEmpty
//                             ? IconButton(
//                                 icon: const Icon(Icons.clear),
//                                 onPressed: clearSearch,
//                               )
//                             : null,
//                         border: OutlineInputBorder(
//                           borderRadius: BorderRadius.circular(8.0),
//                           borderSide: BorderSide.none,
//                         ),
//                         filled: true,
//                         fillColor: Colors.grey[200],
//                         contentPadding:
//                             const EdgeInsets.symmetric(vertical: 8.0),
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//               const SizedBox(height: 20),
//               Text('Kết quả tìm kiếm (${_filteredData.length})',
//                   textAlign: TextAlign.left),
//               const SizedBox(height: 20),
//               Expanded(
//                 child: _filteredData.isEmpty
//                     ? const Center(
//                         child: Text('Không tìm thấy kết quả'),
//                       )
//                     : ListView.builder(
//                         itemCount: _filteredData.length,
//                         itemBuilder: (context, index) {
//                           return widget
//                               .searchedItemBuilder(_filteredData[index]);
//                         },
//                       ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class SearchPage extends StatefulWidget {
  final List<dynamic> data;
  final Widget Function(dynamic item) searchedItemBuilder;
  final String Function(dynamic item) searchByField;
  final bool? isInitialDataDisplayed;

  const SearchPage({
    super.key,
    required this.data,
    required this.searchedItemBuilder,
    required this.searchByField,
    this.isInitialDataDisplayed = false,
  });

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  final TextEditingController _searchController = TextEditingController();
  List<dynamic> _filteredData = [];
  final List<String> _searchHistory = []; // Search history list
  String _currentSearchQuery = '';

  @override
  void initState() {
    super.initState();
    _filteredData = widget.isInitialDataDisplayed == true ? widget.data : [];
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void handleSearch(String query) {
    setState(() {
      _currentSearchQuery = query;
      if (query.isEmpty) {
        _filteredData =
            widget.isInitialDataDisplayed == true ? widget.data : [];
      } else {
        _filteredData = widget.data.where((item) {
          final searchField = widget.searchByField(item).toLowerCase();
          return searchField.contains(query.toLowerCase());
        }).toList();
      }
    });
  }

  void clearSearch() {
    setState(() {
      _searchController.clear();
      _currentSearchQuery = '';
      _filteredData = widget.isInitialDataDisplayed == true ? widget.data : [];
    });
  }

  void saveSearchQuery(String query) {
    if (query.isNotEmpty && !_searchHistory.contains(query)) {
      setState(() {
        _searchHistory.insert(0, query); // Add query to history
        if (_searchHistory.length > 10) {
          _searchHistory.removeLast(); // Limit history size to 10
        }
      });
    }
  }

  void useSearchQuery(String query) {
    _searchController.text = query;
    handleSearch(query);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  GestureDetector(
                      onTap: Get.back,
                      child: const Icon(Icons.arrow_back,
                          size: 24.0, color: Colors.black)),
                  SizedBox(width: 5.w),
                  Expanded(
                    child: TextField(
                      controller: _searchController,
                      onChanged: handleSearch,
                      onSubmitted: (query) {
                        saveSearchQuery(query);
                      },
                      decoration: InputDecoration(
                        hintText: 'Tìm kiếm theo tên ...',
                        hintStyle: const TextStyle(
                          color: Colors.grey,
                        ),
                        prefixIcon: const Icon(Icons.search),
                        suffixIcon: _searchController.text.isNotEmpty
                            ? IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: clearSearch,
                              )
                            : null,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.0),
                          borderSide: BorderSide.none,
                        ),
                        filled: true,
                        fillColor: Colors.grey[200],
                        contentPadding:
                            const EdgeInsets.symmetric(vertical: 8.0),
                      ),
                    ),
                  ),
                ],
              ),
              if (_searchHistory.isNotEmpty) ...[
                const SizedBox(height: 20),
                const Text('Lịch sử tìm kiếm',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                Wrap(
                  spacing: 8.0,
                  children: _searchHistory
                      .map(
                        (query) => GestureDetector(
                          onTap: () => useSearchQuery(query),
                          child: Chip(
                            label: Text(query),
                            deleteIcon: const Icon(Icons.close),
                            onDeleted: () {
                              setState(() {
                                _searchHistory.remove(query);
                              });
                            },
                          ),
                        ),
                      )
                      .toList(),
                ),
              ],
              const SizedBox(height: 20),
              Text('Kết quả tìm kiếm (${_filteredData.length})',
                  textAlign: TextAlign.left),
              const SizedBox(height: 20),
              Expanded(
                child: _filteredData.isEmpty
                    ? const Center(
                        child: Text('Không tìm thấy kết quả'),
                      )
                    : ListView.builder(
                        itemCount: _filteredData.length,
                        itemBuilder: (context, index) {
                          return widget
                              .searchedItemBuilder(_filteredData[index]);
                        },
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
