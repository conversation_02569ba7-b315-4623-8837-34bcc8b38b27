import 'package:flutter/material.dart';

class AnimatedFloatingActionButton extends StatelessWidget {
  final bool isVisible;
  final VoidCallback onPressed;
  final IconData icon;
  final Color iconColor;
  final Color backgroundColor;
  final Duration animationDuration;

  const AnimatedFloatingActionButton({
    super.key,
    required this.isVisible,
    required this.onPressed,
    this.icon = Icons.search,
    this.iconColor = Colors.white,
    this.backgroundColor = Colors.blue,
    this.animationDuration = const Duration(milliseconds: 300),
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedPositioned(
      duration: animationDuration,
      bottom: isVisible ? 16.0 : -60.0,
      right: 16.0,
      child: FloatingActionButton(
        onPressed: onPressed,
        backgroundColor: backgroundColor,
        child: Icon(icon, color: iconColor),
      ),
    );
  }
}
