import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart'; // Thêm GetX vào

class SnackbarUtil {
  static void showCustomSnackbar({
    required String title,
    required String message,
    bool hideTitle = false,
    Duration timeout = const Duration(seconds: 3),
    IconData? icon,
    Color? backgroundColor,
    String alignment = 'bottom', // Xác định vị trí hiển thị
  }) {
    // Lấy context từ GetX để tránh lỗi
    final BuildContext? context = Get.overlayContext;
    if (context == null) {
      debugPrint("Không tìm thấy overlayContext");
      return;
    }

    final overlay = Overlay.of(context);
    final overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: alignment == 'top' ? 50 : null,
        bottom: alignment == 'bottom' ? 50 : null,
        left: 20,
        right: 20,
        child: Material(
          color: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: backgroundColor ?? Colors.green,
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (!hideTitle)
                  Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 15,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                Text(
                  message,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 13,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );

    overlay?.insert(overlayEntry);
    Future.delayed(timeout, () {
      overlayEntry.remove();
    });
  }

  static void showSuccess(String message, {String alignment = 'bottom'}) {
    showCustomSnackbar(
      title: 'Thành công',
      message: message,
      icon: CupertinoIcons.checkmark_alt_circle_fill,
      alignment: alignment,
    );
  }

  static void showError(String message, {String alignment = 'bottom'}) {
    showCustomSnackbar(
      title: 'Lỗi',
      message: message,
      icon: CupertinoIcons.exclamationmark_circle_fill,
      backgroundColor: Colors.red,
      alignment: alignment,
    );
  }

  static void showWarning(String message, {String alignment = 'bottom'}) {
    showCustomSnackbar(
      title: 'Cảnh báo',
      message: message,
      icon: CupertinoIcons.exclamationmark_circle_fill,
      backgroundColor: Colors.orange,
      alignment: alignment,
    );
  }

  static void showInfo(String message, {String alignment = 'bottom'}) {
    showCustomSnackbar(
      title: 'Thông báo',
      message: message,
      icon: CupertinoIcons.info_circle_fill,
      backgroundColor: Colors.blue,
      alignment: alignment,
    );
  }
}
