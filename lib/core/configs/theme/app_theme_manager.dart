import 'package:flutter/material.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/data/models/theme_model.dart';

class AppThemeManager {
  final ThemeModel darkTheme;
  final ThemeModel lightTheme;
  final ThemeModel defaultTheme;
  final List<ThemeModel> customThemes;

  AppThemeManager({
    required this.defaultTheme,
    required this.darkTheme,
    required this.lightTheme,
    required this.customThemes,
  });

  static AppThemeManager initialize() {
    return AppThemeManager(
      darkTheme: ThemeModel.dark(),
      lightTheme: ThemeModel.light(),
      defaultTheme: ThemeModel.custom(
        primaryColor: AppColors.primary,
        accentColor: AppColors.secondary,
        backgroundColor: AppColors.white,
        textColor: AppColors.lightThemePrimaryText,
        buttonColor: AppColors.buttonColors,
        inputFieldColor: AppColors.grey2,
        fontSize: 14.0,
        fontFamily: 'Roboto',
        isDarkMode: false,
      ),
      customThemes: [
        ThemeModel.custom(
          primaryColor: const Color(0xFF146654),
          accentColor: const Color(0xFF2D9371),
          backgroundColor: Colors.white,
          textColor: const Color(0xFF424242),
          buttonColor: const Color(0xFF146755),
          inputFieldColor: const Color(0xFFF1F1F1),
          fontSize: 14.0,
          fontFamily: 'Roboto',
          isDarkMode: false,
        ),
        ThemeModel.custom(
          primaryColor: Colors.red,
          accentColor: Colors.orangeAccent,
          backgroundColor: Colors.red[100]!,
          textColor: Colors.red[800]!,
          buttonColor: Colors.redAccent,
          inputFieldColor: Colors.red[200]!,
          fontSize: 16.0,
          fontFamily: 'Montserrat',
          isDarkMode: false,
        ),
        ThemeModel.custom(
          primaryColor: Colors.green,
          accentColor: Colors.greenAccent,
          backgroundColor: Colors.green[100]!,
          textColor: Colors.green[800]!,
          buttonColor: Colors.green,
          inputFieldColor: Colors.green[200]!,
          fontSize: 16.0,
          fontFamily: 'Arial',
          isDarkMode: false,
        ),
        ThemeModel.custom(
          primaryColor: Colors.purple,
          accentColor: Colors.purpleAccent,
          backgroundColor: Colors.purple[100]!,
          textColor: Colors.purple[800]!,
          buttonColor: Colors.purple,
          inputFieldColor: Colors.purple[200]!,
          fontSize: 16.0,
          fontFamily: 'Verdana',
          isDarkMode: false,
        ),
        ThemeModel.custom(
          primaryColor: Colors.orange,
          accentColor: Colors.orangeAccent,
          backgroundColor: Colors.orange[100]!,
          textColor: Colors.orange[800]!,
          buttonColor: Colors.orangeAccent,
          inputFieldColor: Colors.orange[200]!,
          fontSize: 16.0,
          fontFamily: 'Helvetica',
          isDarkMode: false,
        ),
      ],
    );
  }
}
