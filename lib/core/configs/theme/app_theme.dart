import 'package:flutter/material.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';

class AppTheme {
  static ThemeData get light {
    return ThemeData(
      // cấu hình appbar theme mặc định
      appBarTheme: AppBarTheme(color: AppColors.accentColor),

      // cấu hình scaffold mặc định
      scaffoldBackgroundColor: AppColors.white,
      primaryColor: AppColors.primary,
      colorScheme: ColorScheme.light(
        primary: AppColors.primary,
        secondary: AppColors.primaryFocus,
        surface: AppColors.white,
        error: AppColors.error,
      ),

      // cấu hình text mặc định
      textTheme: const TextTheme(
        bodyLarge: TextStyle(
          color: AppColors.lightThemePrimaryText,
          fontFamily: 'SF Pro', // Set SF font family here
        ),
        bodyMedium: TextStyle(
          color: AppColors.lightThemePrimaryText,
          fontFamily: 'SF Pro',
        ),
        titleLarge: TextStyle(
          color: AppColors.lightThemePrimaryText,
          fontFamily: 'SF Pro',
        ),
        titleMedium: TextStyle(
          color: AppColors.lightThemePrimaryText,
          fontFamily: 'SF Pro',
        ),
        titleSmall: TextStyle(
          color: AppColors.lightThemePrimaryText,
          fontFamily: 'SF Pro',
        ),
        headlineLarge: TextStyle(
          color: AppColors.lightThemePrimaryText,
          fontFamily: 'SF Pro',
        ),
        headlineMedium: TextStyle(
          color: AppColors.lightThemePrimaryText,
          fontFamily: 'SF Pro',
        ),
        headlineSmall: TextStyle(
          color: AppColors.lightThemePrimaryText,
          fontFamily: 'SF Pro',
        ),
        labelLarge: TextStyle(
          color: AppColors.lightThemePrimaryText,
          fontFamily: 'SF Pro',
        ),
        labelMedium: TextStyle(
          color: AppColors.lightThemePrimaryText,
          fontFamily: 'SF Pro',
        ),
        labelSmall: TextStyle(
          color: AppColors.lightThemePrimaryText,
          fontFamily: 'SF Pro',
        ),
      ),
      // cấu hình button mặc định
      buttonTheme: ButtonThemeData(
        buttonColor: AppColors.primary,
        textTheme: ButtonTextTheme.primary,
      ),

      // cấu hình checkbox mặc định
      checkboxTheme: CheckboxThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(5.0),
        ),
        fillColor: WidgetStateProperty.all(AppColors.primary),
      ),
    );
  }

  static ThemeData get dark {
    return ThemeData(
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColors.black,
      ),
      scaffoldBackgroundColor: AppColors.black,
      primaryColor: AppColors.primary,
      colorScheme: ColorScheme.dark(
        primary: AppColors.primary,
        secondary: AppColors.primaryFocus,
        surface: AppColors.black,
        error: AppColors.error,
      ),
      textTheme: const TextTheme(
        bodyLarge: TextStyle(color: AppColors.darkThemePrimaryText),
        bodyMedium: TextStyle(color: AppColors.darkThemePrimaryText),
        titleLarge: TextStyle(color: AppColors.darkThemePrimaryText),
        titleMedium: TextStyle(color: AppColors.darkThemePrimaryText),
        titleSmall: TextStyle(color: AppColors.darkThemePrimaryText),
        headlineLarge: TextStyle(color: AppColors.darkThemePrimaryText),
        headlineMedium: TextStyle(color: AppColors.darkThemePrimaryText),
        headlineSmall: TextStyle(color: AppColors.darkThemePrimaryText),
        labelLarge: TextStyle(color: AppColors.darkThemePrimaryText),
        labelMedium: TextStyle(color: AppColors.darkThemePrimaryText),
        labelSmall: TextStyle(color: AppColors.darkThemePrimaryText),
      ),
      buttonTheme: ButtonThemeData(
        buttonColor: AppColors.primary,
        textTheme: ButtonTextTheme.primary,
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.grey,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.0),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.0),
          borderSide: BorderSide(color: AppColors.primary),
        ),
      ),
      checkboxTheme: CheckboxThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(5.0),
        ),
        fillColor: WidgetStateProperty.all(AppColors.primary),
      ),
    );
  }
}
