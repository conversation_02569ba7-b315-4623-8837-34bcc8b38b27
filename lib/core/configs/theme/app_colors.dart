import 'package:flutter/material.dart';
import 'package:attp_2024/core/configs/theme/app_theme_manager.dart';
import 'package:attp_2024/core/data/models/theme_model.dart';

class AppColors {
  static AppThemeManager appThemeManager = AppThemeManager.initialize();

  static ThemeModel _theme = ThemeModel(
    // primaryColor: const Color(0xFF146654),
    primaryColor: const Color(0xFF7AA802),
    accentColor: const Color(0xFF2D9371),
    backgroundColor: Colors.white,
    textColor: const Color(0xFF424242),
    buttonColor: const Color(0xFF146755),
    inputFieldColor: const Color(0xFFF1F1F1),
    fontSize: 14.0,
    fontFamily: 'Roboto',
    isDarkMode: false,
    themeID: 'DEFAULT',
  );

  static void setTheme(ThemeModel newTheme) {
    Color primaryColor = newTheme.primaryColor is MaterialColor
        ? (newTheme.primaryColor as MaterialColor)[500]!
        : newTheme.primaryColor;

    Color accentColor = newTheme.accentColor is MaterialColor
        ? (newTheme.accentColor as MaterialColor)[500]!
        : newTheme.accentColor;

    Color buttonColor = newTheme.buttonColor is MaterialColor
        ? (newTheme.buttonColor as MaterialColor)[500]!
        : newTheme.buttonColor;

    Color inputFieldColor = newTheme.inputFieldColor is MaterialColor
        ? (newTheme.inputFieldColor as MaterialColor)[500]!
        : newTheme.inputFieldColor;

    _theme = newTheme;

    primary = primaryColor;
    accentColor = accentColor;
    buttonColors = buttonColor;
    inputFieldColors = inputFieldColor;
  }

  // Static color properties
  static Color primary = _theme.primaryColor is MaterialColor
      ? (_theme.primaryColor as MaterialColor)[500]!
      : _theme.primaryColor;

  static Color accentColor = _theme.accentColor is MaterialColor
      ? (_theme.accentColor as MaterialColor)[500]!
      : _theme.accentColor;

  static Color secondary = _theme.accentColor is MaterialColor
      ? (_theme.accentColor as MaterialColor)[500]!
      : _theme.accentColor;

  static Color buttonColors = _theme.buttonColor is MaterialColor
      ? (_theme.buttonColor as MaterialColor)[500]!
      : _theme.buttonColor;

  static Color inputFieldColors = _theme.inputFieldColor is MaterialColor
      ? (_theme.inputFieldColor as MaterialColor)[500]!
      : _theme.inputFieldColor;

  // Other predefined static colors
  static const Color darkThemePrimaryText = Color(0xfff7ffff);
  static const Color lightThemePrimaryText = Color(0xFF424242);
  static const Color transparentColor = Color.fromARGB(0, 255, 255, 255);
  static const Color primaryFocus = Color(0xFF146654);
  static const Color grey = Color(0xFFACACAC);
  static const Color grey1 = Color(0xFF9DA8C3);
  static const Color grey2 = Color(0xFFF1F1F1);
  static const Color black = Color(0xFF020112);
  static const Color white = Color(0xFFFFFFFF);
  static const Color error = Color(0xFFf43850);
  static const Color success = Color(0xFF76d20f);
  static const Color warning = Color(0xFFffb70b);
  static const Color info = Color(0xff35c4fc);
  static const Color warningDark = Color(0xFFf57c00);
  static const Color green1 = Color(0xFF0DA03C);
  static const Color green2 = Color(0xFF10B648);
  static const Color green3 = Color.fromARGB(255, 177, 255, 205);
  static const Color blue1 = Color(0xFF1F82D6);
  static const Color blue2 = Color(0xFF228CE4);
  static const Color blue3 = Color(0xFF449CEA);
  static const Color yellow1 = Color(0xFFD4CC19);
  static const Color yellow2 = Color(0xFFD4CC19);
  static const Color yellow3 = Color(0xFFD4CC19);
  static const Color red1 = Color(0xFFB61C1C);
  static const Color red2 = Color.fromARGB(255, 241, 40, 40);
  static const Color red3 = Color(0xFFD4CC19);
  static const Color orange = Color(0xFFf57c00);
  static const Color iconColors = Color(0xFF7AA802);
  static const Color checkboxColors = Color(0xFF009eb2);
  static const Color focusColor = Color(0xFF146755);
  static const Color greenHeavy = Color.fromARGB(255, 11, 73, 59);
  static const Color boderCircleAvatar = Color(0xFF146755);
  static const Color submitButtonColor = Color(0xFF146755);
  static const Color darkGreen = Color(0xFF056A68);
  static const Color roleColors = Color(0xFFf43850);
  static const Color roleText = Color(0xFF020112);
  static const Color containerColors = Color(0xfff7ffff);
  static const Color borderColors = Color(0xFFC6C6C6);
  static const Color defaultColor1 = Color(0xFF2D9371);
  static const Color defaultColor2 = Color(0xFF9DF3DE);
  static const Color defaultColor3 = Color(0xFFB0B0B0);
  static const Color customColor1 = Color(0xFFFE5828);
  static const Color customColor2 = Color(0xFF003346);
  static const Color customColor3 = Color(0xFF3BB8E6);
  static const Color fingerprintContainer = Color(0xfff7ffff);
  static const Color titleFingerprint = Color(0xff29A016);
  static const Color deviceFingerprint = Color(0xffA7A7A7);
  static const Color customWitchColors = Color(0xFF2D9371);
  static const Color inactiveTrackColor = Color(0xffB0B0B0);
  static const Color activeThumbColor = Color(0xfff7ffff);
  static const Color inactiveThumbColor = Color(0xfff7ffff);
  static const Color borderSuggestion = Color(0xFF16A085);
  static const Color titleSuggestion = Color(0xFF128A73);
  static const Color activeColors = Color(0xFF128A73);
  static const Color checkColors = Color(0xfff7ffff);
  static const Color gray1 = Color.fromARGB(255, 228, 228, 228);
  static const Color gray2 = Color(0xff999999);
  static const Color gray3 = Color.fromARGB(255, 84, 85, 85);
  static const Color green = Color.fromARGB(255, 0, 255, 13);
  static const Color blue = Color(0xff3E8BE2);
  static const Color bottomNav = Color(0xffF2F2F2);
  static const Color borderInput1 = Color.fromARGB(255, 42, 41, 41);
  static const Color borderInputDisabled = Color(0xff918e8e);
  static const Color backgroundColorPrimary = Color(0xffEAF1FC);
  static const Color bgChatbot = Color(0xff01c2ff);
  // backgroundColorPrimary: const Color(0xFF146654),

  //Statistical
  static const Color ConHieuLucTren6Thang = Color(0xff4CAF50);
  static const Color ConHieuLucDuoi6Thang = Color(0xff2196F3);
  static const Color DaThuHoi = Color(0xffFF9800);
  static const Color HetHieuLuc = Color(0xffF44336);
  static const Color NhacNhoCanhBao = Color(0xffe8c410);
  static const Color ThuHoiGiayCamKet = Color(0xffa83d03);
  static const Color XuPhatHanhChinh = Color(0xff1db597);
  static const Color HinhThucKhac = Color(0xff202cb0);
  static const Color ThuHoiGiayChungNhan = Color(0xffF44336);

  //Profile
  static const Color background1 = Color(0xffc2f2de);

  // Driver
  static const Color divider_primary = Color(0xFF16A085);
  static const Color textColorPrimary = Color(0xFF16A085);
}
