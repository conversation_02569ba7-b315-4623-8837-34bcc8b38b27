enum Status { success, error, wating, loading, none }

// ignore: camel_case_types
enum TypeInput { text, number, password }

enum EmailErrors { format, already }

enum DailyMeals { breakfast, lunch, dinner, snack }

enum TypeDialog { success, warning, error }

enum TagMarker { normal, newUpdate, justPosted, trending }

enum ApiError {
  badRequest,
  unauthorized,
  notFound,
  serverError,
  unknown,
}
