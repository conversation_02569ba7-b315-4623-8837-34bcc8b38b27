import 'package:responsive_sizer/responsive_sizer.dart';

class AppDimens {
  static double largeText = 18.sp;
  static double mediumText = 16.sp;
  static double defaultText = 15.sp;
  static double subText = 14.sp;
  static double smallText = 13.sp;

  static const double textStandard = 16.0;

  static double textTitleAppbar = 14.sp;

  static double textBase = 14.0.sp;

  static const double textTitle = 20.0;

  static const double textAppBarSize = 17;

  // Profile
  static double textBaseProfile = 16.sp;
  static double textHeaderProfile = 18.sp;
  static double textHeaderProfile16 = 16.sp;

  static const double textSize10 = 10.0;
  static const double textSize11 = 11.0;
  static const double textSize12 = 12.0;
  static const double textSize13 = 13.0;
  static const double textSize14 = 14.0;
  static const double textSize15 = 15.0;
  static const double textSize16 = 16.0;
  static const double textSize17 = 17.0;
  static const double textSize18 = 18.0;
  static const double textSize20 = 20.0;
  static const double textSize22 = 22.0;
  static const double textSize24 = 24.0;
  static const double textSize26 = 26.0;
  static const double textSize28 = 28.0;
  static const double textSize32 = 32.0;
  static const double textSize42 = 42.0;
  static const double textSize48 = 48.0;

  //Xai cho font CustomTextfield,...
  static const double sizeTitle = 15;
  static const double sizeText = 15;

  //
}
