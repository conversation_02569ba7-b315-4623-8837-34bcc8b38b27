import 'package:attp_2024/features/Chatbot/di/chatbot_binding.dart';
import 'package:attp_2024/features/Chatbot/presentation/pages/chat_history_page.dart';
import 'package:attp_2024/features/Chatbot/presentation/pages/chatbot_page.dart';
import 'package:attp_2024/features/auth/DangNhap/presentation/pages/login_page_.dart';
import 'package:attp_2024/features/expanded/CaNhan/developer/di/developer_binding.dart';
import 'package:attp_2024/features/expanded/CaNhan/developer/presentation/page/developer_page.dart';
import 'package:attp_2024/features/tra_cuu/giayxacnhan_kienthuc/presentation/pages/giay_xac_nhan_kien_thuc_detail_page.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/routes/routes.dart';
import 'package:attp_2024/features/auth/DangNhap/di/login_binding.dart';
import 'package:attp_2024/features/auth/LichSuDangNhap/di/login_history_binding.dart';
import 'package:attp_2024/features/auth/LichSuDangNhap/presentation/pages/fingerprint_auth_page.dart';
import 'package:attp_2024/features/auth/LichSuDangNhap/presentation/pages/login_history_page.dart';
import 'package:attp_2024/features/auth/TaiKhoanNguoiDung/di/used_account_binding.dart';
import 'package:attp_2024/features/auth/TaiKhoanNguoiDung/presentation/pages/used_accounts_page.dart';
import 'package:attp_2024/features/expanded/CaNhan/changePassword/di/changePassword_binding.dart';
import 'package:attp_2024/features/expanded/CaNhan/changePassword/presentation/page/changePassword_page.dart';
import 'package:attp_2024/features/expanded/CaNhan/contactInfo/di/contactInfo_binding.dart';
import 'package:attp_2024/features/expanded/CaNhan/contactInfo/presentation/page/contactInfo_page.dart';
import 'package:attp_2024/features/expanded/CaNhan/customInterface/di/customInterface_binding.dart';
import 'package:attp_2024/features/expanded/CaNhan/customInterface/presentation/page/customInterface_page.dart';
import 'package:attp_2024/features/expanded/CaNhan/editProfile/di/editProfile_binding.dart';
import 'package:attp_2024/features/expanded/CaNhan/editProfile/presentation/page/editProfile_page.dart';
import 'package:attp_2024/features/expanded/CaNhan/fingerPrint/di/fingerPrint_binding.dart';
import 'package:attp_2024/features/expanded/CaNhan/fingerPrint/presentation/page/fingerPrint_page.dart';
import 'package:attp_2024/features/expanded/CaNhan/privacy/di/privacy_binding.dart';
import 'package:attp_2024/features/expanded/CaNhan/privacy/presentation/page/privacy_page.dart';
import 'package:attp_2024/features/expanded/CaNhan/suggestion/di/suggestion_binding.dart';
import 'package:attp_2024/features/expanded/CaNhan/suggestion/presentation/page/suggestion_page.dart';
import 'package:attp_2024/features/expanded/CaNhan/termOfUsers/di/termOfUsers_binding.dart';
import 'package:attp_2024/features/expanded/CaNhan/termOfUsers/presentation/page/termOfUsers_page.dart';
import 'package:attp_2024/features/expanded/CaNhan/versionInfo/di/versionInfo_binding.dart';
import 'package:attp_2024/features/expanded/CaNhan/versionInfo/presentation/page/versionInfo_page.dart';
import 'package:attp_2024/features/expanded/appInfo/di/appInfo_binding.dart';
import 'package:attp_2024/features/expanded/appInfo/presentation/page/appInfo_page.dart';
import 'package:attp_2024/features/main/di/main_binding.dart';
import 'package:attp_2024/features/main/presentation/pages/main_page.dart';
import 'package:attp_2024/features/nav/featureList/ThongKeList/di/ThongKeList_binding.dart';
import 'package:attp_2024/features/nav/featureList/ThongKeList/presentation/pages/ThongKeList_page.dart';
import 'package:attp_2024/features/nav/featureList/TraCuuBanDoSoList/di/TraCuuBanDoSoList_binding.dart';
import 'package:attp_2024/features/nav/featureList/TraCuuBanDoSoList/presentation/pages/TraCuuBanDoSoList_page.dart';
import 'package:attp_2024/features/nav/home/<USER>/home_binding.dart';
import 'package:attp_2024/features/nav/home/<USER>/pages/home_page.dart';
import 'package:attp_2024/features/phan_anh_cssxkd/di/phan_anh_bingding.dart';
import 'package:attp_2024/features/phan_anh_cssxkd/presentation/page/danh_sach_phan_anh.dart';
import 'package:attp_2024/features/phan_anh_cssxkd/presentation/page/phan_anh_page.dart';
import 'package:attp_2024/features/phan_hoi_cssxkd/di/phan_hoi_cssxkd_binding.dart';
import 'package:attp_2024/features/phan_hoi_cssxkd/presentation/page/phan_hoi_cssxkd_page.dart';
import 'package:attp_2024/features/splash/di/splash_binding.dart';
import 'package:attp_2024/features/splash/presentation/pages/splash_page.dart';
import 'package:attp_2024/features/statistial/food_safety_certs/di/food_safety_binding.dart';
import 'package:attp_2024/features/statistial/food_safety_certs/presentation/page/food_safety_statistial_page.dart';
import 'package:attp_2024/features/tinTuc/di/tinTuc_binding.dart';
import 'package:attp_2024/features/tinTuc/presentation/page/tinTuc_detail.dart';
import 'package:attp_2024/features/tinTuc/presentation/page/tinTuc_page.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/di/cs_ddkien_binding.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/di/cs_ddkien_detail_binding.dart';
import 'package:attp_2024/features/tra_cuu/TraCuu_BanDoSo/TraCuu_CSSXKD_DuDK_ATTP_BanDoSo/presentation/page/TraCuu_CSSXKD_DuDK_ATTP_BanDoSo_Page.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/presentation/pages/coso_du_dieukien_page.dart';
import 'package:attp_2024/features/tra_cuu/coso_khong_dudieukien/di/cs_kddkien_binding.dart';
import 'package:attp_2024/features/tra_cuu/coso_khong_dudieukien/di/cs_kddkien_detail_binding.dart';
import 'package:attp_2024/features/tra_cuu/coso_khong_dudieukien/presentation/pages/coso_kdu_dieukien_detail_page.dart';
import 'package:attp_2024/features/tra_cuu/coso_khong_dudieukien/presentation/pages/coso_kdu_dieukien_page.dart';
import 'package:attp_2024/features/tra_cuu/giay_camket/di/giay_camket_binding.dart';
import 'package:attp_2024/features/tra_cuu/giay_camket/di/giay_camket_detail_binding.dart';
import 'package:attp_2024/features/tra_cuu/giay_camket/presentation/pages/giay_camket_detail_page.dart';
import 'package:attp_2024/features/tra_cuu/giay_camket/presentation/pages/giay_camket_page.dart';
import 'package:attp_2024/features/tra_cuu/giayxacnhan_kienthuc/di/giay_xac_nhan_kien_thuc_attp_binding.dart';
import 'package:attp_2024/features/tra_cuu/giayxacnhan_kienthuc/presentation/pages/giay_xac_nhan_kien_thuc_page.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/di/thong_tin_ket_qua_kiem_tra_binding.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/presentation/pages/thong_tin_ket_qua_kiem_tra_detail.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/presentation/pages/thong_tin_ket_qua_kiem_tra_page.dart';
import '../../features/nav/featureList/TraCuuList/di/TraCuuList_binding.dart';
import '../../features/nav/featureList/TraCuuList/presentation/pages/TraCuuList_page.dart';
import '../../features/statistial/CSSXKD_ChuaDuDK_CapGCN/di/CSSXKD_ChuaDuDK_CapGCN_binding.dart';
import '../../features/statistial/CSSXKD_ChuaDuDK_CapGCN/presentation/page/CSSXKD_ChuaDuDK_CapGCN_page.dart';
import '../../features/statistial/CSSXKD_ViPham_ATTP/di/CSSXKD_ViPham_binding.dart';
import '../../features/statistial/CSSXKD_ViPham_ATTP/presentation/page/CSSXKD_ViPham_page.dart';
import '../../features/tra_cuu/giay_chungnhan/di/ThongTin_GCN_Binding.dart';
import '../../features/tra_cuu/giay_chungnhan/di/ThongTin_GCN_Detail_Binding.dart';
import '../../features/tra_cuu/giay_chungnhan/presentation/pages/thongtin_gcn_detail.dart';
import '../../features/tra_cuu/giay_chungnhan/presentation/pages/thongtin_gcn.dart';
import '../../features/tra_cuu/TraCuu_BanDoSo/TraCuu_CSSXKD_ChuaDuDK_ATTP_BanDoSo/di/TraCuu_CSSXKD_ChuaDuDK_ATTP_BanDoSo_Binding.dart';
import '../../features/tra_cuu/TraCuu_BanDoSo/TraCuu_CSSXKD_ChuaDuDK_ATTP_BanDoSo/presentation/page/TraCuu_CSSXKD_ChuaDuDK_ATTP_BanDoSo_Page.dart';
import '../../features/tra_cuu/TraCuu_BanDoSo/TraCuu_CSSXKD_DuDK_ATTP_BanDoSo/di/TraCuu_CSSXKD_DuDK_ATTP_BanDoSo_Binding.dart';
import '../../features/tra_cuu/coso_du_dieukien/presentation/pages/coso_du_dieukien_detail_page.dart';
import '../../features/tra_cuu/giayxacnhan_kienthuc/di/giay_xac_nhan_kien_thuc_detail_attp_binding.dart';

class Pages {
  static const String initial = Routes.splash;

  static const String main = Routes.main;
  static final routes = [
    GetPage(
        name: Routes.splash,
        page: () => const SplashPage(),
        binding: SplashBinding()),
    GetPage(
        name: Routes.main,
        page: () => const MainPage(),
        binding: MainBinding()),
    GetPage(
        name: Routes.home,
        page: () => const HomePage(),
        binding: HomeBinding()),
    // GetPage(
    //     name: Routes.login, page: () => LoginScreen(), binding: LoginBinding()),
    GetPage(
        name: Routes.login, page: () => LoginPage(), binding: LoginBinding()),
    GetPage(
        name: Routes.food_safety_certs_statistial,
        page: () => const FoodSafetyCertsStatisticalPage(),
        binding: FoodSafeTyCertsStatistialBinding()),
    GetPage(
        name: Routes.loginHistory,
        page: () => const LoginFromHistoryPage(),
        binding: LoginHistoryBinding()),
    GetPage(
      name: Routes.usedAccount,
      page: () => const UsedAccountsPage(),
      binding: UsedAccountBinding(),
    ),
    GetPage(
        name: Routes.editProfile,
        page: () => const EditProfilePage(),
        binding: EditProfileBinding()),
    GetPage(
      name: Routes.changePassword,
      page: () => const ChangePasswordPage(),
      binding: ChangePasswordBinding(),
    ),
    GetPage(
      name: Routes.customInterface,
      page: () => const CustomInterfacePage(),
      binding: CustomInterfaceBinding(),
    ),
    GetPage(
      name: Routes.fingerprint,
      page: () => const FingerprintPage(),
      binding: FingerprintBinding(),
    ),
    GetPage(
      name: Routes.fingerprintAuth,
      page: () => const FingerprintAuthPage(),
      binding: LoginHistoryBinding(),
    ),

    GetPage(
      name: Routes.privacy,
      page: () => const PrivacyPage(),
      binding: PrivacyBinding(),
    ),
    GetPage(
      name: Routes.termOfUsers,
      page: () => const TermofusersPage(),
      binding: TermofusersBinding(),
    ),
    GetPage(
      name: Routes.developer,
      page: () => const DeveloperPage(),
      binding: DeveloperBinding(),
    ),
    GetPage(
      name: Routes.appInfo,
      page: () => const AppinfoPage(),
      binding: AppinfoBinding(),
    ),
    GetPage(
      name: Routes.contactInfo,
      page: () => const ContactInfoPage(),
      binding: ContactInfoBinding(),
    ),
    GetPage(
      name: Routes.versionInfo,
      page: () => const VersionInfoPage(),
      binding: VersionInfoBinding(),
    ),
    GetPage(
      name: Routes.suggestion,
      page: () => SuggestionPage(),
      binding: SuggestionBinding(),
    ),
    GetPage(
        name: Routes.tinTuc,
        page: () => TinTucPage(),
        binding: TinTucBinding()),

    GetPage(
        name: Routes.danhSachPhanAnh,
        page: () => const DanhSachPhanAnh(),
        binding: PhanAnhBinding()),
    GetPage(
        name: Routes.phanAnh,
        page: () => const PhanAnhPage(),
        binding: PhanAnhBinding()),
    GetPage(
        name: Routes.phanHoiCSSXKD,
        page: () => const PhanHoiCssxkdPage(),
        binding: PhanHoiCssxkdBinding()),
    // TraCuu
    GetPage(
        name: Routes.coSoDuDieuKien,
        page: () => const CoSoDuDieuKienPage(),
        binding: CoSoDuDieuKienBinding()),
    GetPage(
      name: Routes.coSoDuDieuKienDetail,
      page: () => const CoSoDuDieuKienDetailPage(),
      binding: CoSoDuDieuKienDetailBinding(),
    ),
    // CS khong du dieu kien
    GetPage(
        name: Routes.coSoKhongDuDieuKien,
        page: () => const CoSoKhongDuDieuKienPage(),
        binding: CoSoKDuDieuKienBinding()),
    GetPage(
      name: Routes.coSoKhongDuDieuKienDetail,
      page: () => const CoSoKDuDieuKienDetailPage(),
      binding: CoSoKhongDuDieuKienDetailBinding(),
    ),
    // End CS khong du dieu kien
    GetPage(
      name: Routes.thongTinGCN,
      page: () => const ThongTinGcnPage(),
      binding: ThongTinGcnBinding(),
    ),
    GetPage(
      name: Routes.thongTinGCNDetail,
      page: () => const ThongtinGcnDetailPage(),
      binding: ThongtinGcnDetailBinding(),
    ),
    GetPage(
        name: Routes.thongTinketQuaKiemTra,
        page: () => const ThongTinKetQuaKiemTraPage(),
        binding: ThongTinKetQuaKiemTraBinding()),
    GetPage(
        name: Routes.thongTinketQuaKiemTraDetail,
        page: () => const ThongTinKetQuaKiemTraDetailPage(),
        binding: ThongTinKetQuaKiemTraBinding()),
    GetPage(
        name: Routes.thongTinGiayXacNhanKienThucATTP,
        page: () => const GiayXacNhanKienThucATTPPage(),
        binding: GiayXacNhanKienThucATTPBinding()),
    GetPage(
        name: Routes.thongTinGiayXacNhanKienThucDetail,
        page: () => const GiayXacNhanKienThucATTPDetailPage(),
        binding: GiayXacNhanKienThucDetailAttpBinding()),

    // End Tra Cuu
    GetPage(
        name: Routes.CSSXKD_ChuaDuDK_CapGCN,
        page: () => const CSSXKDChuaDuDKCapGCNPage(),
        binding: CSSXKDChuaDuDKCapGCNBinding()),
    GetPage(
        name: Routes.CSSXKD_ViPham,
        page: () => const CSSXKDViPhamPage(),
        binding: CSSXKDViPhamBinding()),
    GetPage(
        name: Routes.traCuuCSSXKDDuDKATTPBanDoSo,
        page: () => const TracuuCssxkdDudkAttpBandosoPage(),
        binding: TracuuCssxkdDudkAttpBandosoBinding()),
    GetPage(
        name: Routes.traCuuCSSXKDChuaDuDKATTPBanDoSo,
        page: () => const TracuuCssxkdChuadudkAttpBandosoPage(),
        binding: TracuuCssxkdChuadudkAttpBandosoBinding()),

    // Giấy cam kết
    GetPage(
        name: Routes.giayCamKet,
        page: () => GiayCamKetPage(),
        binding: GiayCamKetBinding()),
    GetPage(
        name: Routes.giayCamKetDetail,
        page: () => GiayCamKetDetailPage(),
        binding: GiayCamKetDetailBinding()),

    // Feature list
    GetPage(
        name: Routes.traCuuList,
        page: () => const TracuulistPage(),
        binding: TracuulistBinding()),
    GetPage(
        name: Routes.thongKeList,
        page: () => const ThongkelistPage(),
        binding: ThongkelistBinding()),
    GetPage(
        name: Routes.traCuuBanDoSoList,
        page: () => const TracuubandosolistPage(),
        binding: TracuubandosolistBinding()),
    GetPage(
        name: Routes.chatbot,
        page: () => const ChatBotPage(),
        binding: ChatBotBinding()),
    GetPage(
        name: Routes.chatbotHistory,
        page: () => const ChatHistoryPage(),
        binding: ChatBotBinding()),
  ];
}
