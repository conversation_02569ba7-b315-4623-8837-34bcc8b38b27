class DatabaseModel {
  final String id;
  final String tenApp;
  final String tenSite;
  final String siteURL;
  final String tenTinh;
  final String? huyenID;
  final String? huyenCode;
  final String dbDataName;
  final String dbDUserName;
  final bool checkLicense;
  final String apiUrl;
  final String apiUpFile;
  final String apiDeleteFile;

  DatabaseModel({
    required this.id,
    required this.tenApp,
    required this.tenSite,
    required this.siteURL,
    required this.tenTinh,
    this.huyenID,
    this.huyenCode,
    required this.dbDataName,
    required this.dbDUserName,
    required this.checkLicense,
    required this.apiUrl,
    required this.apiUpFile,
    required this.apiDeleteFile,
  });

  @override
  String toString() {
    return 'DatabaseModel{id: $id, tenApp: $tenApp, tenSite: $tenSite, siteURL: $siteURL, tenTinh: $tenTinh, huyenID: $huyenID, huyenCode: $huyenCode, dbDataName: $dbDataName, dbDUserName: $dbDUserName, checkLicense: $checkLicense, apiUrl: $apiUrl, apiUpFile: $apiUpFile, apiDeleteFile: $apiDeleteFile}';
  }

  factory DatabaseModel.fromJson(Map<String, dynamic> json) {
    return DatabaseModel(
      id: json['ID'] ?? '',
      tenApp: json['TenApp'] ?? '',
      tenSite: json['TenSite'] ?? '',
      siteURL: json['SiteURL'] ?? '',
      tenTinh: json['TenTinh'] ?? '',
      huyenID: json['HuyenID'],
      huyenCode: json['HuyenCode'],
      dbDataName: json['DBData_Name'] ?? '',
      dbDUserName: json['DBDUser_Name'] ?? '',
      checkLicense: json['CheckLicense'] ?? false,
      apiUrl: json['apiUrl'] ?? '',
      apiUpFile: json['apiUpFile'] ?? '',
      apiDeleteFile: json['apiDeleteFile'] ?? '',
    );
  }
}
