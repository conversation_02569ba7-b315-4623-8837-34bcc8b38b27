import 'package:flutter/material.dart';

class ThemeModel {
  final String themeID;
  final Color primaryColor;
  final Color accentColor;
  final Color backgroundColor;
  final Color textColor;
  final Color buttonColor;
  final Color inputFieldColor;
  final double fontSize;
  final String fontFamily;
  final bool isDarkMode;

  ThemeModel({
    required this.themeID,
    required this.primaryColor,
    required this.accentColor,
    required this.backgroundColor,
    required this.textColor,
    required this.buttonColor,
    required this.inputFieldColor,
    required this.fontSize,
    required this.fontFamily,
    required this.isDarkMode,
  });

  // Light Theme
  factory ThemeModel.light() {
    return ThemeModel(
      themeID: 'LIGHT',
      primaryColor: Colors.blue,
      accentColor: Colors.blueAccent,
      backgroundColor: Colors.white,
      textColor: Colors.black,
      buttonColor: Colors.blue,
      inputFieldColor: Colors.grey[200]!,
      fontSize: 14.0,
      fontFamily: 'Roboto',
      isDarkMode: false,
    );
  }

  // Dark Theme
  factory ThemeModel.dark() {
    return ThemeModel(
      themeID: 'DARK',
      primaryColor: Colors.black,
      accentColor: Colors.deepPurpleAccent,
      backgroundColor: Colors.grey[900]!,
      textColor: Colors.white,
      buttonColor: Colors.deepPurple,
      inputFieldColor: Colors.grey[700]!,
      fontSize: 14.0,
      fontFamily: 'Roboto',
      isDarkMode: true,
    );
  }

  // Custom theme
  factory ThemeModel.custom({
    required Color primaryColor,
    required Color accentColor,
    required Color backgroundColor,
    required Color textColor,
    required Color buttonColor,
    required Color inputFieldColor,
    required double fontSize,
    required String fontFamily,
    required bool isDarkMode,
  }) {
    return ThemeModel(
      themeID: 'CUSTOM',
      primaryColor: primaryColor,
      accentColor: accentColor,
      backgroundColor: backgroundColor,
      textColor: textColor,
      buttonColor: buttonColor,
      inputFieldColor: inputFieldColor,
      fontSize: fontSize,
      fontFamily: fontFamily,
      isDarkMode: isDarkMode,
    );
  }

  // Convert the ThemeModel to a JSON encodable map
  Map<String, dynamic> toJson() {
    return {
      'themeID': themeID,
      'primaryColor': primaryColor.value, // Color is converted to an integer
      'accentColor': accentColor.value,
      'backgroundColor': backgroundColor.value,
      'textColor': textColor.value,
      'buttonColor': buttonColor.value,
      'inputFieldColor': inputFieldColor.value,
      'fontSize': fontSize,
      'fontFamily': fontFamily,
      'isDarkMode': isDarkMode,
    };
  }

  // Create a ThemeModel from a JSON map
  factory ThemeModel.fromJson(Map<String, dynamic> json) {
    return ThemeModel(
      themeID: json['themeID'],
      primaryColor: Color(json['primaryColor']),
      accentColor: Color(json['accentColor']),
      backgroundColor: Color(json['backgroundColor']),
      textColor: Color(json['textColor']),
      buttonColor: Color(json['buttonColor']),
      inputFieldColor: Color(json['inputFieldColor']),
      fontSize: json['fontSize'],
      fontFamily: json['fontFamily'],
      isDarkMode: json['isDarkMode'],
    );
  }
}
