class UserAccessModel {
  final String? databaseId;
  final String userID; // <PERSON>ới thêm
  final String tenDangNhap; // Mới thêm
  final String donViID; // Mới thêm
  final String userGroupCode; // Mới thêm
  final String token; // Mới thêm
  final String tenSite;
  final String siteURL;
  final String tenTinh;
  final String huyenID;
  final String huyenCode;
  final String dbDataName;
  final String dbDUserName;
  final bool checkLicense;
  final String userName;
  final String password;

  final String year;
  final String apiUrl;
  final String apiUpFile;
  final String apiDeleteFile;

  final String hoTen;
  final String TenDonVi;
  final String anhDaiDien;

  UserAccessModel({
    this.databaseId,
    required this.userID,
    required this.tenDangNhap,
    required this.donViID,
    required this.userGroupCode,
    required this.token,
    required this.tenSite,
    required this.siteURL,
    required this.tenTinh,
    required this.huyenID,
    required this.huyenCode,
    required this.dbDataName,
    required this.dbDUserName,
    required this.checkLicense,
    required this.userName,
    required this.password,
    String? year,
    required this.apiUrl,
    required this.apiUpFile,
    required this.apiDeleteFile,
    required this.hoTen,
    required this.TenDonVi,
    required this.anhDaiDien
  }) : year = year ?? DateTime.now().year.toString();

  factory UserAccessModel.fromJson(Map<String, dynamic> json) {
    return UserAccessModel(
      databaseId: json['databaseId'],
      userID: json['UserID'], // Thêm trường UserID
      tenDangNhap: json['TenDangNhap'], // Thêm trường TenDangNhap
      donViID: json['DonViID'], // Thêm trường DonViID
      userGroupCode: json['UserGroupCode'], // Thêm trường UserGroupCode
      token: json['Token'], // Thêm trường Token
      tenSite: json['tenSite'],
      siteURL: json['siteURL'],
      tenTinh: json['tenTinh'],
      huyenID: json['huyenID'],
      huyenCode: json['huyenCode'],
      dbDataName: json['dbDataName'],
      dbDUserName: json['dbDUserName'],
      checkLicense: json['checkLicense'],
      userName: json['userName'],
      password: json['password'],
      year: json['year'] ?? DateTime.now().year.toString(),
      apiUrl: json['apiUrl'] ?? '',
      apiUpFile: json['apiUpFile'] ?? '',
      apiDeleteFile: json['apiDeleteFile'] ?? '',
      hoTen: json['HoTen'] ?? '',
      TenDonVi: json['TenDonVi'] ?? '',
      anhDaiDien: json['anhDaiDien'] ?? '',
    );
  }

  // Phương thức để chuyển UserAccessModel thành Map
  Map<String, dynamic> toJson() {
    return {
      "databaseId": databaseId,
      'UserID': userID, // Thêm trường UserID
      'TenDangNhap': tenDangNhap, // Thêm trường TenDangNhap
      'DonViID': donViID, // Thêm trường DonViID
      'UserGroupCode': userGroupCode, // Thêm trường UserGroupCode
      'Token': token, // Thêm trường Token
      'tenSite': tenSite,
      'siteURL': siteURL,
      'tenTinh': tenTinh,
      'huyenID': huyenID,
      'huyenCode': huyenCode,
      'dbDataName': dbDataName,
      'dbDUserName': dbDUserName,
      'checkLicense': checkLicense,
      'userName': userName,
      'password': password,
      'year': year,
      'apiUrl': apiUrl,
      'apiUpFile': apiUpFile,
      'apiDeleteFile': apiDeleteFile,
      'HoTen': hoTen,
      'TenDonVi': TenDonVi,
      'anhDaiDien': anhDaiDien,
    };
  }

  static final UserAccessModel defaultAccount = UserAccessModel(
    databaseId: '',
    userID: '',
    tenDangNhap: '',
    donViID: '',
    userGroupCode: '',
    token: '',
    tenSite: '',
    siteURL: '',
    tenTinh: '',
    huyenID: '',
    huyenCode: '',
    dbDataName: '',
    dbDUserName: '',
    checkLicense: true,
    userName: '',
    password: '',
    apiUrl: '',
    apiUpFile: '',
    apiDeleteFile: '',
    hoTen: '',
    TenDonVi: '',
    anhDaiDien: '',
  );
}

class Permission {
  String name;
  String menuCode;
  bool permis1;
  bool permis2;
  bool permis3;
  bool permis4;
  bool permis5;
  bool permis6;
  bool permis7;
  bool permis8;
  bool permis9;

  Permission({
    required this.name,
    required this.menuCode,
    required this.permis1,
    required this.permis2,
    required this.permis3,
    required this.permis4,
    required this.permis5,
    required this.permis6,
    required this.permis7,
    required this.permis8,
    required this.permis9,
  });
}
