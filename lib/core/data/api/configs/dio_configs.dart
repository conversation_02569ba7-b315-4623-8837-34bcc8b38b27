
import 'package:dio/dio.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_disposable.dart';
import 'package:logger/logger.dart';
import '../../../services/auth_use_case/auth_use_case.dart';

// class DioService extends GetxService {
//   late Dio _dio;
//   late Dio _dioPublic;
//   var logger = Logger();
//
//   Future<DioService> init() async {
//     final String baseUrl =
//         dotenv.env['BASE_URL'] ?? (dotenv.env['BACKUP_URL'] ?? "");
//     final String congigDio = dotenv.env["configDIO"] ?? '';
//     _dio = Dio(BaseOptions(
//       baseUrl: baseUrl,
//       connectTimeout: const Duration(seconds: 10),
//       receiveTimeout: const Duration(seconds: 15),
//       headers: {
//         "Content-Type": "application/json",
//       },
//     ));
//
//     _dio.interceptors.add(InterceptorsWrapper(
//       onRequest: (options, handler) async {
//         try {
//           // String token =
//           //     "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJUZW5EYW5nTmhhcCI6InNveXRlIiwiQ29ubmVjdFVTRVJfRW5jb2RlIjoiWUtRdFQ1ZHZQZFpXLy9mQ2FpZ0o5QlFSVWdjUjY1eTFqTzJER1pKeGVSUmpTUWZ2NFlGTzRNRnRoeVMranZjb0JJUVBnN2pnNzkrN2hQeThFZ2JHUHlEMFZRQm9SNkxqaVVnN1VPQUdyaThpL0dKb09obnY0ZTVpcjBScUxrSnFCaDVJQmNjNXhFOFBCYVZRUDZ4VUdpQmRKQ0NJMFI4YmZLYlpwdlU1eEgzUG5Bc0pYTmdOckE9PSIsIkNvbm5lY3REYXRhX0VuY29kZSI6IllLUXRUNWR2UGRaVy8vZkNhaWdKOUJRUlVnY1I2NXkxak8yREdaSnhlUlJqU1FmdjRZRk80TUZ0aHlTK2p2Y29CSVFQZzdqZzc5KzdoUHk4RWdiR1B5RDBWUUJvUjZMamlVZzdVT0FHcmk4aS9HSm9PaG52NGU1aXIwUnFMa0pxQmg1SUJjYzV4RThQQmFWUVA2eFVHaUJkSkNDSTBSOGJmS2JacHZVNXhIM1BuQXNKWE5nTnJBPT0iLCJuYmYiOjE3MzI5MzQwNjUsImV4cCI6MTczNTUyNjA2NSwiaWF0IjoxNzMyOTM0MDY1fQ.p0zQIsj_S9GD08cKjPa1R3Hvk3fUcOrUtSU4YxrPnvE";
//           //
//           final token = await AuthUseCase.getTokenMemory();
//           if (token.isNotEmpty) {
//             options.headers['Authorization'] =
//                 'Bearer ${token.replaceAll('"', '')}';
//           }
//           return handler.next(options);
//         } catch (e) {
//         }
//       },
//       onResponse: (response, handler) {
//         return handler.next(response);
//       },
//       onError: (DioException error, handler) {
//         if (error.response?.statusCode == 401) {
//           logger.w(error);
//         }
//         return handler.next(error);
//       },
//     ));
//
//     _dioPublic = Dio(BaseOptions(
//       baseUrl: baseUrl,
//       connectTimeout: const Duration(seconds: 10),
//       receiveTimeout: const Duration(seconds: 15),
//       headers: {
//         "Content-Type": "application/json",
//       },
//     ));
//
//     return this;
//   }
//
//   void updateBaseUrl(String newBaseUrl) {
//     if (newBaseUrl.isNotEmpty) {
//       _dio.options.baseUrl = newBaseUrl;
//       _dioPublic.options.baseUrl = newBaseUrl;
//     }
//   }
//
//   Dio get dio => _dio;
//
//   Dio get dioPublic => _dioPublic;
// }

class DioService extends GetxService {
  late Dio _dio;
  late Dio _dioPublic;
  var logger = Logger();

  DioService() {
    final String baseUrl = dotenv.env['BASE_URL'] ?? (dotenv.env['BACKUP_URL'] ?? "");
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 60),
      receiveTimeout: const Duration(seconds: 60),
      headers: {
        "Content-Type": "application/json",
      },
    ));

    _dioPublic = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 60),
      receiveTimeout: const Duration(seconds: 60),
      headers: {
        "Content-Type": "application/json",
      },
    ));
  }

  Future<DioService> init() async {
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        try {
          final token = await AuthUseCase.getTokenMemory();
          if (token.isNotEmpty) {
            options.headers['Authorization'] = 'Bearer ${token.replaceAll('"', '')}';
          }
          return handler.next(options);
        } catch (e) {
        }
      },
      onResponse: (response, handler) {
        return handler.next(response);
      },
      onError: (DioException error, handler) {
        if (error.response?.statusCode == 401) {
          logger.w(error);
        }
        return handler.next(error);
      },
    ));

    return this;
  }

  void updateBaseUrl(String newBaseUrl) {
    if (newBaseUrl.isNotEmpty) {
      _dio.options.baseUrl = newBaseUrl;
      _dioPublic.options.baseUrl = newBaseUrl;
    }
  }

  Dio get dio => _dio;
  Dio get dioPublic => _dioPublic;
}
