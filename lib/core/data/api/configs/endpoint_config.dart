class EndpointConfig {
  //========================= DATABASE =====================
  static String getDataBaseEndpoint({required String appName}) =>
      "/CSDL_laptrinh?app=$appName";

  //========================= AUTHENTICATION ================
  static String loginEndpoint = "/login_moi";
  static String permissonEndpoint = '/getPerMission?UserID=';

  //========================= PROC ============================
  static String procEndPoint({required String proc}) => "/api/?proc=$proc";

  //========================= API ============================
  static String apiEndPoint({required String api}) => "/$api";

  //========================= Upload ============================
  static String uploadEndPoint() =>
      "/api/UploadMultifiles?loaiVB=HA&donViCode=84_000_001&formName=CungLD";
//=========================End Upload ==========================
}
