import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:attp_2024/core/configs/enum.dart';
import 'package:attp_2024/core/data/api/configs/dio_configs.dart';
import 'package:attp_2024/core/data/api/configs/endpoint_config.dart';
import 'package:attp_2024/core/data/models/result.dart';

class CoreService {
  final String? DATABASE_NAME = dotenv.env['DATABASE_NAME'];
  final DioService dioService;

  CoreService(this.dioService);

  CoreService.create() : dioService = DioService() {
    dioService.init();
  }

  Future<Result<T>> fetchData<T>({
    required String endpoint,
    required T Function(dynamic data) parse,
  }) async {
    return _request<T>(method: 'GET', endpoint: endpoint, parse: parse);
  }

  Future<Result<T>> postData<T>({
    required String endpoint,
    required dynamic data,
    required T Function(dynamic data) parse,
  }) async {
    return _request<T>(
        method: 'POST', endpoint: endpoint, data: data, parse: parse);
  }

  Future<Result<T>> _request<T>({
    required String method,
    required String endpoint,
    dynamic data,
    required T Function(dynamic data) parse,
  }) async {
    try {
      await dioService.init();
      final response = method == 'POST'
          ? await dioService.dio.post(endpoint, data: data)
          : await dioService.dio.get(endpoint);
      return _handleResponse<T>(response, parse);
    } catch (e) {
      return Result.error(ApiError.badRequest);
    }
  }

  Result<T> _handleResponse<T>(
      dynamic response, T Function(dynamic data) parse) {
    if (response.statusCode == 200) {
      final data = response.data["data"];
      return Result.success(parse(data));
    } else {
      return Result.error(ApiError.badRequest);
    }
  }

  Future<Result<List<T>>> generateData<T>(
      {required String proc,
      required T Function(Map<String, dynamic>) fromJson,
      List request = const []}) {
    return postData<List<T>>(
      endpoint: EndpointConfig.procEndPoint(proc: proc),
      data: request,
      parse: (data) {
        if (data is List) {
          return data.map((item) => fromJson(item)).toList();
        }
        return [];
      },
    );
  }
}
