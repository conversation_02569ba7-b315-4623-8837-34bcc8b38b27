import 'dart:developer';

import 'package:attp_2024/core/data/api/configs/endpoint_config.dart';
import 'package:attp_2024/core/data/api/services/core_service.dart';

class APIService extends CoreService {
  APIService(super.dioService);

  Future<dynamic> callAPI(String api, [dynamic body = const []]) async {
    try {
      final response = await dioService.dio.post(
        EndpointConfig.apiEndPoint(api: api),
        data: body,
      );
      if (response.statusCode == 401) {
        throw Exception("Unauthorized: Token is invalid or expired");
      }
      final data = response.data;
      if (data == null) {
        throw Exception("No response data received from the server");
      }
      log(response.toString(), name: 'akr');
      log(data.toString(), name: 'akr');
      return data;
    } catch (e) {
      log("Error calling procedure: $e", name: 'akr');
      throw Exception("Error calling procedure: $e");
    }
  }
}
