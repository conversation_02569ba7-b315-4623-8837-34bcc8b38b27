// import 'package:logger/logger.dart';
// import 'package:attp_2024/core/data/api/configs/endpoint_config.dart';
// import 'package:attp_2024/core/data/api/services/core_service.dart';
//
// class ProcService extends CoreService {
//   final Logger _logger = Logger();
//   Future<List<dynamic>> callProc(String proc,
//       [List<dynamic> body = const []]) async {
//     try {
//       final response = await dioService.dio.post(
//         EndpointConfig.procEndPoint(proc: proc),
//         data: body,
//       );
//       if (response.statusCode == 401) {
//         throw Exception("Unauthorized: Token is invalid or expired");
//       }
//
//       final data = response.data;
//       if (data == null || data['success'] != true) {
//         throw Exception(
//             "Request failed: ${data?['message'] ?? 'Unknown error'}");
//       }
//
//       return data['data'] as List<dynamic>;
//     } catch (e) {
//       _logger.e(e);
//       throw Exception("Error calling procedure: $e");
//     }
//   }
// }
import 'package:attp_2024/core/data/api/configs/endpoint_config.dart';
import 'package:attp_2024/core/data/api/services/core_service.dart';

import '../../configs/dio_configs.dart';

class ProcService extends CoreService {
  final DioService dioService;
  ProcService(this.dioService) : super(dioService);
  Future<List<dynamic>> callProc(String proc,
      [List<dynamic> body = const []]) async {
    try {
      final response = await dioService.dio.post(
        EndpointConfig.procEndPoint(proc: proc),
        data: body,
      );
      if (response.statusCode == 401) {
        throw Exception("Unauthorized: Token is invalid or expired");
      }

      final data = response.data;
      if (data == null || data['success'] != true) {
        throw Exception(
            "Request failed: ${data?['message'] ?? 'Unknown error'}");
      }

      return data['data'] as List<dynamic>;
    } catch (e) {
      // print("Error calling procedure: $e");
      throw Exception("Error calling procedure: $e");
    }
  }
}
