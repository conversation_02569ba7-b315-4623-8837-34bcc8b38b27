import 'package:dio/dio.dart';
import 'package:attp_2024/core/data/api/configs/endpoint_config.dart';
import 'package:attp_2024/core/data/api/services/core_service.dart';

class UploadService extends CoreService {
  UploadService(super.dioService);

  Future<void> uploadImage(String filePath) async {
    try {
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(filePath, filename: "image.jpg"),
      });
      final response = await dioService.dio.post(
        EndpointConfig.uploadEndPoint(),
        data: formData,
        options: Options(
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        ),
      );
      if (response.statusCode == 401) {
        throw Exception("Unauthorized: Token is invalid or expired");
      }
      final data = response.data;
      if (data == null || data['success'] != true) {
        throw Exception(
            "Image upload failed: ${data?['message'] ?? 'Unknown error'}");
      }
      print("Image upload successful");
    } catch (e) {
      print("Error uploading image: $e");
      throw Exception("Error uploading image: $e");
    }
  }
}
