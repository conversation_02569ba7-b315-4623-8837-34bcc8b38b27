// ignore: file_names
class TinhTrangHoatDongModel {
  final String maTinhTrangTGHDKTID; // ID của trạng thái
  final String maTinhTrangTGHDKT; // Mã của trạng thái
  final String tenTinhTrangTGHDKT; // Tên trạng thái
  final String dienGiai; // <PERSON><PERSON> tả (nếu có)
  final bool trangThai; // Trạng thái hoạt động

  // Constructor
  const TinhTrangHoatDongModel({
    required this.maTinhTrangTGHDKTID,
    required this.maTinhTrangTGHDKT,
    required this.tenTinhTrangTGHDKT,
    required this.dienGiai,
    required this.trangThai,
  });

  // Phương thức để khởi tạo từ JSON
  factory TinhTrangHoatDongModel.fromJson(Map<String, dynamic> json) {
    return TinhTrangHoatDongModel(
      maTinhTrangTGHDKTID: json['TinhTrangTGHDKTID'] as String,
      maTinhTrangTGHDKT: json['MaTinhTrangTGHDKT'] as String,
      tenTinhTrangTGHDKT: json['TenTinhTrangTGHDKT'] as String,
      dienGiai: json['DienGiai'] as String,
      trangThai: json['TrangThai'] as bool,
    );
  }
}
