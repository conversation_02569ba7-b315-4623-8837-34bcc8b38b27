class PermissionResponseModel {
  String menuCode;
  String tenMenu;
  String permiss;

  PermissionResponseModel({
    required this.menuCode,
    required this.permiss,
    required this.tenMenu,
  });

  // Hàm fromJson
  factory PermissionResponseModel.fromJson(Map<String, dynamic> json) {
    return PermissionResponseModel(
      menuCode: json['MenuCode'],
      permiss: json['Permiss'],
      tenMenu: json['TenMenu'],
    );
  }

  // Hàm toJson
  Map<String, dynamic> toJson() {
    return {
      'MenuCode': menuCode,
      'TenMenu': tenMenu,
      'Permiss': permiss,
    };
  }
}
