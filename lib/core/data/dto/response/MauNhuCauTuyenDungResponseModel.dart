// ignore: file_names
class MauNhuCauTuyenDungResponseModel {
  final String cauLaoDongTDID;
  final String cauLaoDongID;
  final String ngheNghiepID;
  final double tongSo;
  final double soLuongNu;
  final String ngheNghiepCLDID;
  final String ngheNghiepCLDCode;
  final String tenNgheNghiepCLD;
  final String dienGiai;
  final bool trangThai;
  final bool apDungCauLaoDong;
  final String ngheNghiepCLDIDCha;
  final int cap;
  final double tongSo1;
  final double soLuongNu1;
  final String ngheNghiepCLDCode1;
  final String tenNgheNghiepCLD1;

  MauNhuCauTuyenDungResponseModel({
    required this.cauLaoDongTDID,
    required this.cauLaoDongID,
    required this.ngheNghiepID,
    required this.tongSo,
    required this.soLuongNu,
    required this.ngheNghiepCLDID,
    required this.ngheNghiepCLDCode,
    required this.tenNgheNghiepCLD,
    required this.dienGiai,
    required this.trangThai,
    required this.apDungCauLaoDong,
    required this.ngheNghiepCLDIDCha,
    required this.cap,
    required this.tongSo1,
    required this.soLuongNu1,
    required this.ngheNghiepCLDCode1,
    required this.tenNgheNghiepCLD1,
  });

  factory MauNhuCauTuyenDungResponseModel.fromJson(Map<String, dynamic> json) {
    return MauNhuCauTuyenDungResponseModel(
      cauLaoDongTDID: json['CauLaoDongTDID'],
      cauLaoDongID: json['CauLaoDongID'],
      ngheNghiepID: json['NgheNghiepID'],
      tongSo: json['TongSo'].toDouble(),
      soLuongNu: json['SoLuongNu'].toDouble(),
      ngheNghiepCLDID: json['NgheNghiepCLDID'],
      ngheNghiepCLDCode: json['NgheNghiepCLDCode'],
      tenNgheNghiepCLD: json['TenNgheNghiepCLD'],
      dienGiai: json['DienGiai'],
      trangThai: json['TrangThai'],
      apDungCauLaoDong: json['ApDungCauLaoDong'],
      ngheNghiepCLDIDCha: json['NgheNghiepCLDID_Cha'],
      cap: json['Cap'],
      tongSo1: json['TongSo1'].toDouble(),
      soLuongNu1: json['SoLuongNu1'].toDouble(),
      ngheNghiepCLDCode1: json['NgheNghiepCLDCode1'],
      tenNgheNghiepCLD1: json['TenNgheNghiepCLD1'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'CauLaoDongTDID': cauLaoDongTDID,
      'CauLaoDongID': cauLaoDongID,
      'NgheNghiepID': ngheNghiepID,
      'TongSo': tongSo,
      'SoLuongNu': soLuongNu,
      'NgheNghiepCLDID': ngheNghiepCLDID,
      'NgheNghiepCLDCode': ngheNghiepCLDCode,
      'TenNgheNghiepCLD': tenNgheNghiepCLD,
      'DienGiai': dienGiai,
      'TrangThai': trangThai,
      'ApDungCauLaoDong': apDungCauLaoDong,
      'NgheNghiepCLDID_Cha': ngheNghiepCLDIDCha,
      'Cap': cap,
      'TongSo1': tongSo1,
      'SoLuongNu1': soLuongNu1,
      'NgheNghiepCLDCode1': ngheNghiepCLDCode1,
      'TenNgheNghiepCLD1': tenNgheNghiepCLD1,
    };
  }
}
