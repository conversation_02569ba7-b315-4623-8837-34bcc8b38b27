// ignore: file_names
class CauLaoDongModel {
  final String cauLaoDongID;
  final DateTime ngayThuThap;
  final String toChucID;
  final String matHang;
  final String quyMoLaoDongID;
  final String loaiHinhDNID;
  final String nganhKinhTeID;
  final String soNha;
  final String diaBanHCIDThon;
  final String diaBanHCIDXa;
  final String diaBanHCIDHuyen;
  final String diaBanHCIDTinh;
  final String diaChiCuThe;
  final String soDienThoai;
  final String? diDong;
  final String email;
  final String tinhTrangTGHDKTID;
  final String noiDungThuThap;
  final bool namTrongKCN;
  final String kinhDo;
  final String viDo;
  final int nienDo;
  final int trangThaiID;
  final DateTime? ngayDuyet;
  final String nguoiDuyet;
  final DateTime? ngayTC;
  final String nguoiTC;
  final String? noiDungDuyetTC;
  final String dinhKem;
  final String donViID;
  final String userID;
  final DateTime ngayThaoTac;
  final bool bienDong;
  final String chuKy;
  final String nguoiCungCapTT;

  CauLaoDongModel({
    required this.cauLaoDongID,
    required this.ngayThuThap,
    required this.toChucID,
    required this.matHang,
    required this.quyMoLaoDongID,
    required this.loaiHinhDNID,
    required this.nganhKinhTeID,
    required this.soNha,
    required this.diaBanHCIDThon,
    required this.diaBanHCIDXa,
    required this.diaBanHCIDHuyen,
    required this.diaBanHCIDTinh,
    required this.diaChiCuThe,
    required this.soDienThoai,
    this.diDong,
    required this.email,
    required this.tinhTrangTGHDKTID,
    required this.noiDungThuThap,
    required this.namTrongKCN,
    required this.kinhDo,
    required this.viDo,
    required this.nienDo,
    required this.trangThaiID,
    this.ngayDuyet,
    required this.nguoiDuyet,
    this.ngayTC,
    required this.nguoiTC,
    this.noiDungDuyetTC,
    required this.dinhKem,
    required this.donViID,
    required this.userID,
    required this.ngayThaoTac,
    required this.bienDong,
    required this.chuKy,
    required this.nguoiCungCapTT,
  });

  // Phương thức fromJson để chuyển đổi từ JSON sang đối tượng Dart
  factory CauLaoDongModel.fromJson(Map<String, dynamic> json) {
    return CauLaoDongModel(
      cauLaoDongID: json['CauLaoDongID'],
      ngayThuThap: DateTime.parse(json['NgayThuThap']),
      toChucID: json['ToChucID'],
      matHang: json['MatHang'],
      quyMoLaoDongID: json['QuyMoLaoDongID'],
      loaiHinhDNID: json['LoaiHinhDNID'],
      nganhKinhTeID: json['NganhKinhTeID'],
      soNha: json['SoNha'],
      diaBanHCIDThon: json['DiaBanHCID_Thon'],
      diaBanHCIDXa: json['DiaBanHCID_Xa'],
      diaBanHCIDHuyen: json['DiaBanHCID_Huyen'],
      diaBanHCIDTinh: json['DiaBanHCID_Tinh'],
      diaChiCuThe: json['DiaChiCuThe'],
      soDienThoai: json['SoDienThoai'],
      diDong: json['DiDong'],
      email: json['Email'],
      tinhTrangTGHDKTID: json['TinhTrangTGHDKTID'],
      noiDungThuThap: json['NoiDungThuThap'],
      namTrongKCN: json['NamTrongKCN'],
      kinhDo: json['KinhDo'],
      viDo: json['ViDo'],
      nienDo: json['NienDo'],
      trangThaiID: json['TrangThaiID'],
      ngayDuyet:
          json['NgayDuyet'] != null ? DateTime.parse(json['NgayDuyet']) : null,
      nguoiDuyet: json['NguoiDuyet'],
      ngayTC: json['NgayTC'] != null ? DateTime.parse(json['NgayTC']) : null,
      nguoiTC: json['NguoiTC'],
      noiDungDuyetTC: json['NoiDungDuyetTC'],
      dinhKem: json['DinhKem'],
      donViID: json['DonViID'],
      userID: json['UserID'],
      ngayThaoTac: DateTime.parse(json['NgayThaoTac']),
      bienDong: json['BienDong'],
      chuKy: json['ChuKy'],
      nguoiCungCapTT: json['NguoiCungCapTT'],
    );
  }

  // Phương thức toJson để chuyển đổi từ đối tượng Dart sang JSON
  Map<String, dynamic> toJson() {
    return {
      'CauLaoDongID': cauLaoDongID,
      'NgayThuThap': ngayThuThap.toIso8601String(),
      'ToChucID': toChucID,
      'MatHang': matHang,
      'QuyMoLaoDongID': quyMoLaoDongID,
      'LoaiHinhDNID': loaiHinhDNID,
      'NganhKinhTeID': nganhKinhTeID,
      'SoNha': soNha,
      'DiaBanHCID_Thon': diaBanHCIDThon,
      'DiaBanHCID_Xa': diaBanHCIDXa,
      'DiaBanHCID_Huyen': diaBanHCIDHuyen,
      'DiaBanHCID_Tinh': diaBanHCIDTinh,
      'DiaChiCuThe': diaChiCuThe,
      'SoDienThoai': soDienThoai,
      'DiDong': diDong,
      'Email': email,
      'TinhTrangTGHDKTID': tinhTrangTGHDKTID,
      'NoiDungThuThap': noiDungThuThap,
      'NamTrongKCN': namTrongKCN,
      'KinhDo': kinhDo,
      'ViDo': viDo,
      'NienDo': nienDo,
      'TrangThaiID': trangThaiID,
      'NgayDuyet': ngayDuyet?.toIso8601String(),
      'NguoiDuyet': nguoiDuyet,
      'NgayTC': ngayTC?.toIso8601String(),
      'NguoiTC': nguoiTC,
      'NoiDungDuyetTC': noiDungDuyetTC,
      'DinhKem': dinhKem,
      'DonViID': donViID,
      'UserID': userID,
      'NgayThaoTac': ngayThaoTac.toIso8601String(),
      'BienDong': bienDong,
      'ChuKy': chuKy,
      'NguoiCungCapTT': nguoiCungCapTT,
    };
  }
}
