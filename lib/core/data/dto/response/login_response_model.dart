class LoginResponseModel {
  final String userId;
  final String tenDangNhap;
  final String donViId;
  final String userGroupCode;
  final String token;

  LoginResponseModel({
    required this.userId,
    required this.tenDangNhap,
    required this.donViId,
    required this.userGroupCode,
    required this.token,
  });

  // Hàm chuyển từ JSON thành đối tượng LoginResponseModel
  factory LoginResponseModel.fromJson(Map<String, dynamic> json) {
    return LoginResponseModel(
      userId: json['UserID'],
      tenDangNhap: json['TenDangNhap'],
      donViId: json['DonViID'],
      userGroupCode: json['UserGroupCode'],
      token: json['Token'],
    );
  }

  // Hàm chuyển đối tượng thành JSON
  Map<String, dynamic> toJson() {
    return {
      'UserID': userId,
      'TenDangNhap': tenDangNhap,
      'DonViID': donViId,
      'UserGroupCode': userGroupCode,
      'Token': token,
    };
  }

  // Hàm để lấy phần tử đầu tiên từ mảng JSON
  static LoginResponseModel fromJsonArray(List<dynamic> jsonArray) {
    // Lấy phần tử đầu tiên từ mảng JSON và chuyển thành LoginResponseModel
    return LoginResponseModel.fromJson(jsonArray[0]);
  }
}
