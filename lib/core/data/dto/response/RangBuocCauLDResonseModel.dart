// ignore: file_names
class RangBuocCauLDResonseModel {
  final String code;
  final String phepSoSanh;
  final String canhBao;
  final String loaiThongBao;

  RangBuocCauLDResonseModel({
    required this.code,
    required this.phepSoSanh,
    required this.canhBao,
    required this.loaiThongBao,
  });

  // Factory method to create an instance from JSON
  factory RangBuocCauLDResonseModel.fromJson(Map<String, dynamic> json) {
    return RangBuocCauLDResonseModel(
      code: json['Code'],
      phepSoSanh: json['PhepSoSanh'],
      canhBao: json['CanhBao'],
      loaiThongBao: json['LoaiThongBao'],
    );
  }

  // Method to convert instance to JSON
  Map<String, dynamic> toJson() {
    return {
      'Code': code,
      'PhepSoSanh': phepSoSanh,
      'CanhBao': canh<PERSON>ao,
      'LoaiThongBao': loai<PERSON><PERSON><PERSON><PERSON>,
    };
  }
}
