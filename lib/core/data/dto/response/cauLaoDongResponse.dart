// ignore: file_names
class CauLaoDongResponse {
  final String id;
  final String ngayThuThap;
  final String toChucID;
  final String maToChuc;
  final String soDienThoai;
  final String email;
  final String phuongXa;
  final String tenToChuc;
  final String maSoThue;
  final String ngayHoatDong;
  final String diaChiCuThe;
  final String? tinhTrang;
  final String? loaiHinh;
  final String? nganhNghe;
  final String matHang;
  final String? quyMo;
  final String noiDungThuThap;
  final String dinhKemVB;
  final String nguoiCungCapTT;
  final bool namTrongKCN;
  final int trangThai;
  final String kinhDo;
  final String viDo;

  CauLaoDongResponse({
    required this.id,
    required this.ngayThuThap,
    required this.toChucID,
    required this.maToChuc,
    required this.soDienThoai,
    required this.email,
    required this.phuongXa,
    required this.tenToChuc,
    required this.maSoThue,
    required this.ngayHoatDong,
    required this.diaChiCuThe,
    this.tinhTrang,
    this.loaiHinh,
    this.nganhNghe,
    required this.matHang,
    this.quyMo,
    required this.noiDungThuThap,
    required this.dinhKemVB,
    required this.nguoiCungCapTT,
    required this.namTrongKCN,
    required this.trangThai,
    required this.kinhDo,
    required this.viDo,
  });

  factory CauLaoDongResponse.fromJson(Map<String, dynamic> json) {
    return CauLaoDongResponse(
      id: json['ID'],
      ngayThuThap: json['NgayThuThap'],
      toChucID: json['ToChucID'],
      maToChuc: json['MaToChuc'],
      soDienThoai: json['SoDienThoai'],
      email: json['Email'],
      phuongXa: json['PhuongXa'],
      tenToChuc: json['TenToChuc'],
      maSoThue: json['MaSoThue'],
      ngayHoatDong: json['NgayHoatDong'],
      diaChiCuThe: json['DiaChiCuThe'],
      tinhTrang: json['TinhTrang'],
      loaiHinh: json['LoaiHinh'],
      nganhNghe: json['NganhNghe'],
      matHang: json['MatHang'],
      quyMo: json['QuyMo'],
      noiDungThuThap: json['NoiDungThuThap'],
      dinhKemVB: json['DinhKemVB'],
      nguoiCungCapTT: json['NguoiCungCapTT'],
      namTrongKCN: json['NamTrongKCN'],
      trangThai: json['trangThai'],
      kinhDo: json['KinhDo'],
      viDo: json['ViDo'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ID': id,
      'NgayThuThap': ngayThuThap,
      'ToChucID': toChucID,
      'MaToChuc': maToChuc,
      'SoDienThoai': soDienThoai,
      'Email': email,
      'PhuongXa': phuongXa,
      'TenToChuc': tenToChuc,
      'MaSoThue': maSoThue,
      'NgayHoatDong': ngayHoatDong,
      'DiaChiCuThe': diaChiCuThe,
      'TinhTrang': tinhTrang,
      'LoaiHinh': loaiHinh,
      'NganhNghe': nganhNghe,
      'MatHang': matHang,
      'QuyMo': quyMo,
      'NoiDungThuThap': noiDungThuThap,
      'DinhKemVB': dinhKemVB,
      'NguoiCungCapTT': nguoiCungCapTT,
      'NamTrongKCN': namTrongKCN,
      'trangThai': trangThai,
      'KinhDo': kinhDo,
      'ViDo': viDo,
    };
  }
}
