// ignore: file_names
class LoaiHinhDoanhNghiepModel {
  final String value;
  final String code;
  final String display;

  const LoaiHinhDoanhNghiepModel({
    required this.value,
    required this.code,
    required this.display,
  });

  // <PERSON><PERSON><PERSON><PERSON> thức để khởi tạo từ JSON
  factory LoaiHinhDoanhNghiepModel.fromJson(Map<String, dynamic> json) {
    return LoaiHinhDoanhNghiepModel(
      value: json['value'],
      code: json['code'],
      display: json['display'],
    );
  }
}
