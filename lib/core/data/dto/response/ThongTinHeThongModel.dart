// ignore_for_file: public_member_api_docs, sort_constructors_first

class ThongTinHeThongModel {
  final String HeThong_MobileID;
  final String TenHeThong_Mobile;
  final int ViTri;
  final String MoTa;
  final String NoiDung;
  final String DinhDanh;
  final String PhienBan;
  final bool TrangThai;
  final String Icon;

  ThongTinHeThongModel({
    required this.HeThong_MobileID,
    required this.TenHeThong_Mobile,
    required this.ViTri,
    required this.MoTa,
    required this.NoiDung,
    required this.DinhDanh,
    required this.PhienBan,
    required this.TrangThai,
    required this.Icon,
  });

  @override
  String toString() {
    return '''
    ThongTinHeThongModel(
      ID: $HeThong_MobileID, 
      TenHeThong_Mobile: $TenHeThong_Mobile, 
      ViTri: $ViTri, 
      MoTa: $MoTa, 
      NoiDung: $NoiDung, 
      DinhDanh: $DinhDanh, 
      PhienBan: $PhienBan, 
      TrangThai: $TrangThai
      Icon: $Icon
    )
    ''';
  }
}
