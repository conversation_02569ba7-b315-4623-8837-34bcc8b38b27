class DeviceResponseModel {
  final String thietBiID;
  final String thietBiCode;
  final String tenThietBi;
  final String hangSX;
  final String platform;
  final String version;
  final String loaiThietBi;
  final bool ngungSD;
  final String dienGiai;
  final String userID;

  DeviceResponseModel({
    required this.thietBiID,
    required this.thietBiCode,
    required this.tenThietBi,
    required this.hangSX,
    required this.platform,
    required this.version,
    required this.loaiThietBi,
    required this.ngungSD,
    required this.dienGiai,
    required this.userID,
  });

  // Factory constructor to create a DeviceResponseModel from a JSON map
  factory DeviceResponseModel.fromJson(Map<String, dynamic> json) {
    return DeviceResponseModel(
      thietBiID: json['ThietBiID'] as String,
      thietBiCode: json['MaThietBi'] as String,
      tenThietBi: json['TenThietBi'] as String,
      hangSX: json['HangSX'] as String,
      platform: json['Platform'] as String,
      version: json['Version'] as String,
      loaiThietBi: json['LoaiThietBi'] as String,
      ngungSD: json['TrangThai'] as bool,
      dienGiai: json['DienGiai'] as String,
      userID: json['UserID'] as String,
    );
  }

  // Method to convert a DeviceResponseModel instance to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'ThietBiID': thietBiID,
      'MaThietBi': thietBiCode,
      'TenThietBi': tenThietBi,
      'HangSX': hangSX,
      'Platform': platform,
      'Version': version,
      'LoaiThietBi': loaiThietBi,
      'TrangThai': ngungSD,
      'DienGiai': dienGiai,
      'UserID': userID,
    };
  }
}
