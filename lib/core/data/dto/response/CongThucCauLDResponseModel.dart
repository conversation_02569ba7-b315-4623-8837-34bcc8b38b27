// ignore: file_names
class CongThucCauLDResponseModel {
  final String code;
  final String congThuc;

  CongThucCauLDResponseModel({
    required this.code,
    required this.congThuc,
  });

  // Factory method to create an instance from JSON
  factory CongThucCauLDResponseModel.fromJson(Map<String, dynamic> json) {
    return CongThucCauLDResponseModel(
      code: json['Code'],
      congThuc: json['CongThuc'],
    );
  }

  // Method to convert instance to JSON
  Map<String, dynamic> toJson() {
    return {
      'Code': code,
      'CongThuc': congThuc,
    };
  }
}
