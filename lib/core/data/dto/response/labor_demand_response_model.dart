class LaborDemandResponseModel {
  final String id;
  final String ngayThuThap;
  final int loaiThuThap;
  final int trangThai;
  final String tenToChuc;
  final String maSoThue;
  final String diaChiCuThe;
  final String soDienThoai;
  final String chuKy;
  final String ngayHoatDong;
  final String dinhKem;

  LaborDemandResponseModel({
    required this.id,
    required this.ngayThuThap,
    required this.loaiThuThap,
    required this.trangThai,
    required this.tenToChuc,
    required this.maSoThue,
    required this.diaChiCuThe,
    required this.soDienThoai,
    required this.chuKy,
    required this.ngayHoatDong,
    required this.dinhKem,
  });

  factory LaborDemandResponseModel.fromJson(Map<String, dynamic> json) {
    return LaborDemandResponseModel(
      id: json['ID'] as String,
      ngayThuThap: json['NgayThuThap'] as String,
      loaiThuThap: json['LoaiThuThap'] as int,
      trangThai: json['TrangThai'] as int,
      tenToChuc: json['TenToChuc'] as String,
      maSoThue: json['MaSoThue'] as String,
      diaChiCuThe: json['DiaChiCuThe'] as String,
      soDienThoai: json['SoDienThoai'] as String,
      chuKy: json['ChuKy'] as String,
      ngayHoatDong: json['NgayHoatDong'] as String,
      dinhKem: json['DinhKem'] as String,
    );
  }

  // Method to convert the instance to JSON
  Map<String, dynamic> toJson() {
    return {
      'ID': id,
      'NgayThuThap': ngayThuThap,
      'LoaiThuThap': loaiThuThap,
      'TrangThai': trangThai,
      'TenToChuc': tenToChuc,
      'MaSoThue': maSoThue,
      'DiaChiCuThe': diaChiCuThe,
      'SoDienThoai': soDienThoai,
      'ChuKy': chuKy,
      'NgayHoatDong': ngayHoatDong,
      'DinhKem': dinhKem,
    };
  }
}

List<LaborDemandResponseModel> parseLaborDemandResponse(
    List<dynamic> jsonList) {
  return jsonList
      .map((json) => LaborDemandResponseModel.fromJson(json))
      .toList();
}
