class OrganizationResponseModel {
  final String toChucID;
  final String maToChuc;
  String tenToChuc;
  String tenKCN;
  String tenNguoiSuDungLD;
  String soCCCD;
  String maSoThue;
  String? ngayHoatDong;
  String? tenLoaiHinhDoanhNghiep;
  List<String> nganhKinhTeID;
  String diaChiCuThe;
  String soNha;
  String soDienThoai;
  String email;
  String tenTinhTrangHDKinhTe;
  String ghiChu;
  bool namTrongKCN;
  bool trangThai;
  String tenTinh;
  String tenHuyen;
  String tenXa;
  String? tenThon;
  String tenNganhNgheKD;

  OrganizationResponseModel({
    required this.toChucID,
    required this.maToChuc,
    required this.tenToChuc,
    required this.tenKCN,
    required this.tenNguoiSuDungLD,
    required this.soCCCD,
    required this.maSoThue,
    this.ngayHoatDong,
    this.tenLoaiHinhDoanhNghiep,
    required this.nganhKinhTeID,
    required this.diaChiCuThe,
    required this.soNha,
    required this.so<PERSON><PERSON><PERSON><PERSON><PERSON>,
    required this.email,
    required this.tenTinhTrangHDKinhTe,
    required this.ghi<PERSON>hu,
    required this.namTrongKCN,
    required this.trangThai,
    required this.tenTinh,
    required this.tenHuyen,
    required this.tenXa,
    this.tenThon,
    required this.tenNganhNgheKD,
  });

  factory OrganizationResponseModel.fromJson(Map<String, dynamic> json) {
    return OrganizationResponseModel(
      toChucID: json['ToChucID'] ?? '',
      maToChuc: json['MaToChuc'] ?? '',
      tenToChuc: json['TenToChuc'] ?? '',
      tenKCN: json['TenKCN'] ?? '',
      tenNguoiSuDungLD: json['TenNguoiSuDungLD'] ?? '',
      soCCCD: json['SoCCCD'] ?? '',
      maSoThue: json['MaSoThue'] ?? '',
      ngayHoatDong: json['NgayHoatDong'] ?? null,
      tenLoaiHinhDoanhNghiep: json['TenLoaiHinhDoanhNghiep'],
      nganhKinhTeID: (json['NganhKinhTeID'] as String)
          .replaceAll(RegExp(r'[\[\]"]'), '')
          .split(',')
          .map((e) => e.trim())
          .toList(),
      diaChiCuThe: json['DiaChiCuThe'] ?? '',
      soNha: json['SoNha'] ?? '',
      soDienThoai: json['SoDienThoai'] ?? '',
      email: json['Email'] ?? '',
      tenTinhTrangHDKinhTe: json['TenTinhTrangHDKinhTe'] ?? '',
      ghiChu: json['GhiChu'] ?? '',
      namTrongKCN: json['NamTrongKCN'] ?? false,
      trangThai: json['TrangThai'] ?? false,
      tenTinh: json['TenTinh'] ?? '',
      tenHuyen: json['TenHuyen'] ?? '',
      tenXa: json['TenXa'] ?? '',
      tenThon: json['TenThon'],
      tenNganhNgheKD: json['TenNganhNgheKD'] ?? '',
    );
  }

  // Tạo dữ liệu mặc định
  factory OrganizationResponseModel.defaultModel() {
    return OrganizationResponseModel(
      toChucID: '',
      maToChuc: '',
      tenToChuc: '',
      tenKCN: '',
      tenNguoiSuDungLD: '',
      soCCCD: '',
      maSoThue: '',
      ngayHoatDong: null,
      tenLoaiHinhDoanhNghiep: null,
      nganhKinhTeID: [],
      diaChiCuThe: '',
      soNha: '',
      soDienThoai: '',
      email: '',
      tenTinhTrangHDKinhTe: '',
      ghiChu: '',
      namTrongKCN: false,
      trangThai: true,
      tenTinh: '',
      tenHuyen: '',
      tenXa: '',
      tenThon: null,
      tenNganhNgheKD: '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ToChucID': toChucID,
      'MaToChuc': maToChuc,
      'TenToChuc': tenToChuc,
      'TenKCN': tenKCN,
      'TenNguoiSuDungLD': tenNguoiSuDungLD,
      'SoCCCD': soCCCD,
      'MaSoThue': maSoThue,
      'NgayHoatDong': ngayHoatDong,
      'TenLoaiHinhDoanhNghiep': tenLoaiHinhDoanhNghiep,
      'NganhKinhTeID': nganhKinhTeID,
      'DiaChiCuThe': diaChiCuThe,
      'SoNha': soNha,
      'SoDienThoai': soDienThoai,
      'Email': email,
      'TenTinhTrangHDKinhTe': tenTinhTrangHDKinhTe,
      'GhiChu': ghiChu,
      'NamTrongKCN': namTrongKCN,
      'TrangThai': trangThai,
      'TenTinh': tenTinh,
      'TenHuyen': tenHuyen,
      'TenXa': tenXa,
      'TenThon': tenThon,
      'TenNganhNgheKD': tenNganhNgheKD,
    };
  }
}
