// ignore_for_file: public_member_api_docs, sort_constructors_first
class ProfileUserModel {
  String diaChi;
  String tenDangNhap;
  String hoTen;
  String gioiTinh;
  String diDong;
  String email;
  String ngaySinh;
  String hinhDaiDien;
  String donViCode;
  String tenDonVi;
  String donViID;
  String tinhID;
  String huyenID;
  String xaID;
  String thonID;
  String tenTinh;
  String tenHuyen;
  String tenXa;
  String tenThon;
  String userGroupID;
  String userGroupCode;

  ProfileUserModel({
    required this.diaChi,
    required this.tenDangNhap,
    required this.hoTen,
    required this.gioiTinh,
    required this.diDong,
    required this.email,
    required this.ngaySinh,
    required this.hinhDaiDien,
    required this.donViCode,
    required this.tenDonVi,
    required this.donViID,
    required this.tinhID,
    required this.huyenID,
    required this.xaID,
    required this.thonID,
    required this.tenTinh,
    required this.tenHuyen,
    required this.tenXa,
    required this.tenThon,
    required this.userGroupID,
    required this.userGroupCode,
  });

  factory ProfileUserModel.fromJson(Map<String, dynamic> json) {
    return ProfileUserModel(
      diaChi: json['diaChi'] as String,
      tenDangNhap: json['tenDangNhap'] as String,
      hoTen: json['hoTen'] as String,
      gioiTinh: json['gioiTinh'] as String,
      diDong: json['diDong'] as String,
      email: json['email'] as String,
      ngaySinh: json['ngaySinh'] as String,
      hinhDaiDien: json['hinhDaiDien'] as String,
      donViCode: json['donViCode'] as String,
      tenDonVi: json['tenDonVi'] as String,
      donViID: json['donViID'] as String,
      tinhID: json['tinhID'] as String,
      huyenID: json['huyenID'] as String,
      xaID: json['xaID'] as String,
      thonID: json['thonID'] as String,
      tenTinh: json['tenTinh'] as String,
      tenHuyen: json['tenHuyen'] as String,
      tenXa: json['tenXa'] as String,
      tenThon: json['tenThon'] as String,
      userGroupID: json['userGroupID'] as String,
      userGroupCode: json['userGroupCode'] as String,
    );
  }
}
