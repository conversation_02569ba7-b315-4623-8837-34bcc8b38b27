import 'package:uuid/uuid.dart';

class DeviceResponse {
  String thietBiID;
  String thietBiCode;
  String tenThietBi;
  String hangSX;
  String platform;
  String version;
  String loaiThietBi;

  DeviceResponse({
    required this.thietBiID,
    required this.thietBiCode,
    required this.tenThietBi,
    required this.hangSX,
    required this.platform,
    required this.version,
    required this.loaiThietBi,
  });

  factory DeviceResponse.createDefault() {
    const uuid = Uuid();
    return DeviceResponse(
      thietBiID: uuid.v4(),
      thietBiCode: "Device_${DateTime.now().millisecondsSinceEpoch}",
      tenThietBi: "",
      hangSX: "",
      platform: "",
      version: "",
      loaiThietBi: "",
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ThietBiID': thietBiID,
      'MaThietBi': thietBiCode,
      'TenThietBi': tenThietBi,
      'HangSX': hangSX,
      'Platform': platform,
      'Version': version,
      'LoaiThietBi': loaiThietBi,
    };
  }

  factory DeviceResponse.fromMap(Map<String, dynamic> map) {
    return DeviceResponse(
      thietBiID: map['ThietBiID'] ?? '',
      thietBiCode: map['MaThietBi'] ?? '',
      tenThietBi: map['TenThietBi'] ?? '',
      hangSX: map['HangSX'] ?? '',
      platform: map['Platform'] ?? '',
      version: map['Version'] ?? '',
      loaiThietBi: map['LoaiThietBi'] ?? '',
    );
  }
}
