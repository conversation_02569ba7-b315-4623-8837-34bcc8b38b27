class DataBaseResponseModel {
  final String id;
  final String tenApp;
  final String tenSite;
  final String siteURL;
  final String tenTinh;
  final String huyenID;
  final String huyenCode;
  final String dbDataName;
  final String dbDUserName;
  final bool checkLicense;

  DataBaseResponseModel({
    required this.id,
    required this.tenApp,
    required this.tenSite,
    required this.siteURL,
    required this.tenTinh,
    required this.huyenID,
    required this.huyenCode,
    required this.dbDataName,
    required this.dbDUserName,
    required this.checkLicense,
  });

  // Tạo phương thức để chuyển đổi từ JSON
  factory DataBaseResponseModel.fromJson(Map<String, dynamic> json) {
    return DataBaseResponseModel(
      id: json['id'],
      tenApp: json['tenApp'],
      tenSite: json['tenSite'],
      siteURL: json['siteURL'],
      tenTinh: json['tenTinh'],
      huyenID: json['huyenID'],
      huyenCode: json['huyenCode'],
      dbDataName: json['dbDataName'],
      dbDUserName: json['dbDUserName'],
      checkLicense: json['checkLicense'],
    );
  }

  // Tạo phương thức để chuyển đổi sang JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'tenApp': tenApp,
      'tenSite': tenSite,
      'siteURL': siteURL,
      'tenTinh': tenTinh,
      'huyenID': huyenID,
      'huyenCode': huyenCode,
      'dbDataName': dbDataName,
      'dbDUserName': dbDUserName,
      'checkLicense': checkLicense,
    };
  }
}
