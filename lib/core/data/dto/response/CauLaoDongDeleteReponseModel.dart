// ignore: file_names
class CauLaoDongDeleteResponseModel {
  String cauLaoDongID;
  DateTime ngayThuThap;
  String toChucID;
  String matHang;
  String quyMoLaoDongID;
  String loaiHinhDNID;
  List<String> nganhKinhTeID;
  String? soNha;
  String diaBanHCIDThon;
  String diaBanHCIDXa;
  String diaBanHCIDHuyen;
  String diaBanHCIDTinh;
  String diaChiCuThe;
  String soDienThoai;
  String? diDong;
  String email;
  String tinhTrangTGHDKTID;
  String? noiDungThuThap;
  bool namTrongKCN;
  double? kinhDo;
  double? viDo;
  int nienDo;
  int trangThaiID;
  DateTime? ngayDuyet;
  String nguoiDuyet;
  DateTime? ngayTC;
  String nguoiTC;
  String? noiDungDuyetTC;
  String dinhKem;
  String donViID;
  String userID;
  DateTime ngayThaoTac;
  bool bienDong;
  String chuKy;
  String nguoiCungCapTT;

  CauLaoDongDeleteResponseModel({
    required this.cauLaoDongID,
    required this.ngayThuThap,
    required this.toChucID,
    required this.matHang,
    required this.quyMoLaoDongID,
    required this.loaiHinhDNID,
    required this.nganhKinhTeID,
    this.soNha,
    required this.diaBanHCIDThon,
    required this.diaBanHCIDXa,
    required this.diaBanHCIDHuyen,
    required this.diaBanHCIDTinh,
    required this.diaChiCuThe,
    required this.soDienThoai,
    this.diDong,
    required this.email,
    required this.tinhTrangTGHDKTID,
    this.noiDungThuThap,
    required this.namTrongKCN,
    this.kinhDo,
    this.viDo,
    required this.nienDo,
    required this.trangThaiID,
    this.ngayDuyet,
    required this.nguoiDuyet,
    this.ngayTC,
    required this.nguoiTC,
    this.noiDungDuyetTC,
    required this.dinhKem,
    required this.donViID,
    required this.userID,
    required this.ngayThaoTac,
    required this.bienDong,
    required this.chuKy,
    required this.nguoiCungCapTT,
  });

  factory CauLaoDongDeleteResponseModel.fromJson(Map<String, dynamic> json) {
    return CauLaoDongDeleteResponseModel(
      cauLaoDongID: json['CauLaoDongID'],
      ngayThuThap: DateTime.parse(json['NgayThuThap']),
      toChucID: json['ToChucID'],
      matHang: json['MatHang'],
      quyMoLaoDongID: json['QuyMoLaoDongID'],
      loaiHinhDNID: json['LoaiHinhDNID'],
      nganhKinhTeID: List<String>.from(json['NganhKinhTeID']),
      soNha: json['SoNha'],
      diaBanHCIDThon: json['DiaBanHCID_Thon'],
      diaBanHCIDXa: json['DiaBanHCID_Xa'],
      diaBanHCIDHuyen: json['DiaBanHCID_Huyen'],
      diaBanHCIDTinh: json['DiaBanHCID_Tinh'],
      diaChiCuThe: json['DiaChiCuThe'],
      soDienThoai: json['SoDienThoai'],
      diDong: json['DiDong'],
      email: json['Email'],
      tinhTrangTGHDKTID: json['TinhTrangTGHDKTID'],
      noiDungThuThap: json['NoiDungThuThap'],
      namTrongKCN: json['NamTrongKCN'],
      kinhDo: json['KinhDo'],
      viDo: json['ViDo'],
      nienDo: json['NienDo'],
      trangThaiID: json['TrangThaiID'],
      ngayDuyet:
          json['NgayDuyet'] != null ? DateTime.parse(json['NgayDuyet']) : null,
      nguoiDuyet: json['NguoiDuyet'],
      ngayTC: json['NgayTC'] != null ? DateTime.parse(json['NgayTC']) : null,
      nguoiTC: json['NguoiTC'],
      noiDungDuyetTC: json['NoiDungDuyetTC'],
      dinhKem: json['DinhKem'],
      donViID: json['DonViID'],
      userID: json['UserID'],
      ngayThaoTac: DateTime.parse(json['NgayThaoTac']),
      bienDong: json['BienDong'],
      chuKy: json['ChuKy'],
      nguoiCungCapTT: json['NguoiCungCapTT'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'CauLaoDongID': cauLaoDongID,
      'NgayThuThap': ngayThuThap.toIso8601String(),
      'ToChucID': toChucID,
      'MatHang': matHang,
      'QuyMoLaoDongID': quyMoLaoDongID,
      'LoaiHinhDNID': loaiHinhDNID,
      'NganhKinhTeID': nganhKinhTeID,
      'SoNha': soNha,
      'DiaBanHCID_Thon': diaBanHCIDThon,
      'DiaBanHCID_Xa': diaBanHCIDXa,
      'DiaBanHCID_Huyen': diaBanHCIDHuyen,
      'DiaBanHCID_Tinh': diaBanHCIDTinh,
      'DiaChiCuThe': diaChiCuThe,
      'SoDienThoai': soDienThoai,
      'DiDong': diDong,
      'Email': email,
      'TinhTrangTGHDKTID': tinhTrangTGHDKTID,
      'NoiDungThuThap': noiDungThuThap,
      'NamTrongKCN': namTrongKCN,
      'KinhDo': kinhDo,
      'ViDo': viDo,
      'NienDo': nienDo,
      'TrangThaiID': trangThaiID,
      'NgayDuyet': ngayDuyet?.toIso8601String(),
      'NguoiDuyet': nguoiDuyet,
      'NgayTC': ngayTC?.toIso8601String(),
      'NguoiTC': nguoiTC,
      'NoiDungDuyetTC': noiDungDuyetTC,
      'DinhKem': dinhKem,
      'DonViID': donViID,
      'UserID': userID,
      'NgayThaoTac': ngayThaoTac.toIso8601String(),
      'BienDong': bienDong,
      'ChuKy': chuKy,
      'NguoiCungCapTT': nguoiCungCapTT,
    };
  }
}
