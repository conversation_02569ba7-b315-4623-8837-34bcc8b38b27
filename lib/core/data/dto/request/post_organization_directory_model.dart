class PostOrganizationRequest {
  final String? loai;
  final String? toChucID;
  final String? maToChucUs;
  final bool? namTrongKCN;
  final String? tenKCNUs;
  final String? maSoThueUs;
  final String? tenToChucUs;
  final String? tenNguoiSDLDUs;
  final String? soCCCDUs;
  final String? loaiHinhDNIDUsTC;
  final String? tinhTrangHDIDUs;
  final DateTime? ngayHoatDongUsTC;
  final String? soDienThoaiUs;
  final String? emailUs;
  final String? tinhIDUsTC;
  final String? huyenIDUsTC;
  final String? xaIDUsTC;
  final String? thonIDUsTC;
  final String? soNhaUsTC;
  final String? diaChiUsTC;
  final String? ghiChuTCUs;
  final String? trangThaiToChucUs;
  final String? nganhNgheIDUsTC;
  final String? donViID;
  final String? userID;

  PostOrganizationRequest({
    this.loai,
    this.toChucID,
    this.maToChucUs,
    this.namTrongKCN,
    this.tenKCNUs,
    this.maSoThueUs,
    this.tenToChucUs,
    this.tenNguoiSDLDUs,
    this.soCCCDUs,
    this.loaiHinhDNIDUsTC,
    this.tinhTrangHDIDUs,
    this.ngayHoatDongUsTC,
    this.soDienThoaiUs,
    this.emailUs,
    this.tinhIDUsTC,
    this.huyenIDUsTC,
    this.xaIDUsTC,
    this.thonIDUsTC,
    this.soNhaUsTC,
    this.diaChiUsTC,
    this.ghiChuTCUs,
    this.trangThaiToChucUs,
    this.nganhNgheIDUsTC,
    this.donViID,
    this.userID,
  });

  // Method to convert the request object to a map for API calls
  Map<String, dynamic> toJson() {
    return {
      "Loai": loai,
      "ToChucID": toChucID,
      "MaToChuc_us": maToChucUs,
      "NamTrongKCN": namTrongKCN,
      "TenKCN_us": tenKCNUs,
      "MaSoThue_us": maSoThueUs,
      "TenToChuc_us": tenToChucUs,
      "TenNguoiSDLD_us": tenNguoiSDLDUs,
      "SoCCCD_us": soCCCDUs,
      "LoaiHinhDNID_us_TC": loaiHinhDNIDUsTC,
      "TinhTrangHDID_us": tinhTrangHDIDUs,
      "NgayHoatDong_us_TC": ngayHoatDongUsTC?.toIso8601String(),
      "SoDienThoai_us": soDienThoaiUs,
      "Email_us": emailUs,
      "TinhID_us_TC": tinhIDUsTC,
      "HuyenID_us_TC": huyenIDUsTC,
      "XaID_us_TC": xaIDUsTC,
      "ThonID_us_TC": thonIDUsTC,
      "SoNha_us_TC": soNhaUsTC,
      "DiaChi_us_TC": diaChiUsTC,
      "GhiChuTC_us": ghiChuTCUs,
      "TrangThaiToChuc_us": trangThaiToChucUs,
      "NganhNgheID_us_TC": nganhNgheIDUsTC,
      "DonViID": donViID,
      "UserID": userID,
    };
  }
}
