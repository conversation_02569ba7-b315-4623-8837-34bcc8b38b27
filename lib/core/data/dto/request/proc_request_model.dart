class FieldModel {
  final String name;
  final String type;
  final dynamic value;

  FieldModel({
    required this.name,
    required this.type,
    required this.value,
  });

  factory FieldModel.fromJson(Map<String, dynamic> json) {
    return FieldModel(
      name: json['name'] as String,
      type: json['type'] as String,
      value: json['value'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'type': type,
      'value': value,
    };
  }
}

class ProcRequestModel {
  final List<FieldModel> fields;
  ProcRequestModel({required this.fields});
  factory ProcRequestModel.fromJson(List<dynamic> jsonList) {
    return ProcRequestModel(
      fields: jsonList
          .map((json) => FieldModel.fromJson(json as Map<String, dynamic>))
          .toList(),
    );
  }

  List<Map<String, dynamic>> toJson() {
    return fields.map((field) => field.toJson()).toList();
  }
}
