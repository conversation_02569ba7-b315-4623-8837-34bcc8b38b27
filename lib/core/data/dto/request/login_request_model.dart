class LoginRequestModel {
  final String userName;
  final String password;
  final String idDatabase;

  LoginRequestModel({
    required this.userName,
    required this.password,
    required this.idDatabase,
  });

  // Convert the object to a Map (JSON)
  Map<String, dynamic> toJson() {
    return {
      'UserName': userName,
      'Password': password,
      'Database': idDatabase,
    };
  }

  // Create an instance from a Map (JSON)
  factory LoginRequestModel.fromJson(Map<String, dynamic> json) {
    return LoginRequestModel(
      userName: json['UserName'],
      password: json['Password'],
      idDatabase: json['Database'],
    );
  }
}
