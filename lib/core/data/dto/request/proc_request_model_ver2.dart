class ProcRequestModelVer2 {
  final String procName;
  final List<Map<String, dynamic>> fields;

  ProcRequestModelVer2({
    required this.procName,
    required this.fields,
  });

  // Override phương thức toString()
  @override
  String toString() {
    return 'ProcRequestModelVer2(procName: $procName, fields: $fields)';
  }

  Map<String, dynamic> toJson() {
    return {
      'procName': procName,
      'fields': fields,
    };
  }
}
