import 'package:attp_2024/core/configs/contanst/proc_constants.dart';
import 'package:attp_2024/core/data/api/services/core_service.dart';
import 'package:attp_2024/core/data/dto/request/login_request_model.dart';
import 'package:attp_2024/core/data/dto/response/login_response_model.dart';
import 'package:attp_2024/core/data/models/result.dart';

class UserData extends CoreService {
  UserData(super.dioService);

  // Fetch dữ liệu loại hình
  <PERSON><Result<List<LoginResponseModel>>> login(
      {required LoginRequestModel request}) {
    final body = request.toJson();
    return generateData<LoginResponseModel>(
      proc: ProcConstants.getMauDuLieuNguoiLaoDong,
      fromJson: LoginResponseModel.fromJson,
      // request: body,
    );
  }
}
