import 'package:attp_2024/core/configs/contanst/proc_constants.dart';
import 'package:attp_2024/core/data/api/services/core_service.dart';
import 'package:attp_2024/core/data/dto/response/RangBuocCauLDResonseModel.dart';
import 'package:attp_2024/core/data/models/result.dart';

class RangBuocCauLaoDongData extends CoreService {
  RangBuocCauLaoDongData(super.dioService);

  // Fetch dữ liệu loại hình doanh nghiệp

  // Fetch dữ liệu loại hình
  Future<Result<List<RangBuocCauLDResonseModel>>> fetchAllRangBuocCauLaoDong(
      {List request = const []}) {
    return generateData<RangBuocCauLDResonseModel>(
      proc: ProcConstants.getAllRangBuocCauLaoDong,
      fromJson: RangBuocCauLDResonseModel.fromJson,
      request: request,
    );
  }
}
