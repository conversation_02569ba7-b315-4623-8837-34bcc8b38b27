import 'package:attp_2024/core/configs/contanst/proc_constants.dart';
import 'package:attp_2024/core/data/api/services/core_service.dart';
import 'package:attp_2024/core/data/dto/request/proc_request_model.dart';
import 'package:attp_2024/core/data/dto/response/TinhTrangHoatDongModel.dart';
import 'package:attp_2024/core/data/dto/response/category_abs_response.dart';
import 'package:attp_2024/core/data/models/result.dart';

class CategoryUntilData extends CoreService {
  CategoryUntilData(super.dioService);

  // Fetch dữ liệu loại hình hoạt động
  Future<Result<List<TinhTrangHoatDongModel>>> fetchAllTinhTrangHoatDong(
      {List request = const []}) {
    return generateData<TinhTrangHoatDongModel>(
      proc: ProcConstants.getAllTinhTrangHoatDong,
      fromJson: TinhTrangHoatDongModel.fromJson,
      request: request,
    );
  }

  // Fetch dữ liệu loại hình
  Future<Result<List<CategoryAbsResponse>>> fetchAllTinh(
      {List request = const []}) {
    return generateData<CategoryAbsResponse>(
      proc: ProcConstants.getAllTinh,
      fromJson: CategoryAbsResponse.fromJson,
      request: request,
    );
  }

  Future<Result<List<CategoryAbsResponse>>> fetchAllHuyen(
      {required ProcRequestModel tinhRequest}) {
    final body = tinhRequest.toJson();
    return generateData<CategoryAbsResponse>(
      proc: ProcConstants.getAllHuyen,
      fromJson: CategoryAbsResponse.fromJson,
      request: body,
    );
  }

  Future<Result<List<CategoryAbsResponse>>> fetchAllXa(
      {required ProcRequestModel huyenRequest}) {
    final body = huyenRequest.toJson();
    return generateData<CategoryAbsResponse>(
      proc: ProcConstants.getAllXa,
      fromJson: CategoryAbsResponse.fromJson,
      request: body,
    );
  }

  Future<Result<List<CategoryAbsResponse>>> fetchAllThon(
      {required ProcRequestModel xaRequest}) {
    final body = xaRequest.toJson();
    return generateData<CategoryAbsResponse>(
      proc: ProcConstants.getAllThon,
      fromJson: CategoryAbsResponse.fromJson,
      request: body,
    );
  }

  Future<Result<List<CategoryAbsResponse>>> fetchAllQuyMoLaoDong(
      {List request = const []}) {
    return generateData<CategoryAbsResponse>(
      proc: ProcConstants.getAllQuyMoLaoDong,
      fromJson: CategoryAbsResponse.fromJson,
      request: request,
    );
  }

  Future<Result<List<CategoryAbsResponse>>> fetchAllNghanhNghekinhDoanh(
      {List request = const []}) {
    return generateData<CategoryAbsResponse>(
      proc: ProcConstants.getAllNghanhNgheKinhDoanh,
      fromJson: CategoryAbsResponse.fromJson,
      request: request,
    );
  }

  Future<Result<List<CategoryAbsResponse>>> fetchAllData(
      {List request = const [], required String procName}) {
    return generateData<CategoryAbsResponse>(
      proc: procName,
      fromJson: CategoryAbsResponse.fromJson,
      request: request,
    );
  }
}
