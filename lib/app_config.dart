import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/configs/theme/app_theme_manager.dart';
import 'package:attp_2024/core/data/models/theme_model.dart';
import 'package:attp_2024/core/services/theme_use_case.dart';

Future<void> appConfig() async {
  WidgetsFlutterBinding.ensureInitialized();
  await dotenv.load(fileName: ".env");
  // setup theme
  try {
    ThemeModel current = await ThemeUseCase.getThemes();
    AppColors.setTheme(current);
  } catch (e) {
    AppThemeManager appThemeManager = AppThemeManager.initialize();
    AppColors.setTheme(appThemeManager.defaultTheme);
  }
}
