import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:attp_2024/features/main/model/item_navigation_bar_model.dart';
import 'package:attp_2024/features/nav/featureList/ThongKeList/di/ThongKeList_binding.dart';
import 'package:attp_2024/features/nav/home/<USER>/home_binding.dart';
import 'package:attp_2024/features/nav/home/<USER>/pages/home_page.dart';
import 'package:attp_2024/features/nav/profile/di/profile_binding.dart';
import 'package:attp_2024/features/nav/profile/presentation/pages/profile_page.dart';

import '../../../nav/featureList/ThongKeList/presentation/pages/ThongKeList_page.dart';
import '../../../nav/featureList/TraCuuList/di/TraCuuList_binding.dart';
import '../../../nav/featureList/TraCuuList/presentation/pages/TraCuuList_page.dart';
import '../../../tinTuc/di/tinTuc_binding.dart';
import '../../../tinTuc/presentation/page/tinTuc_page.dart';

class MainController extends GetxController {
  // index active nav
  var selectedPageIndex = 0.obs;

  // value nav items
  List<ItemNavigationBarModel> itemsNav = [
    ItemNavigationBarModel(
        label: "Tra cứu", route: "/tracuu", icon: CupertinoIcons.search),
    ItemNavigationBarModel(
        label: "Tin tức", route: "/tintuc", icon: CupertinoIcons.news),
    ItemNavigationBarModel(
        label: "Thống kê", route: "/thongke", icon: CupertinoIcons.chart_bar),
    ItemNavigationBarModel(
        label: "Cá nhân", route: "/canhan", icon: CupertinoIcons.person_fill),
    ItemNavigationBarModel(
        label: "Home", route: "/home", icon: CupertinoIcons.house),
  ];

  void openPage(Widget page) {
    Get.to(() => page);
  }

  var showChatTooltip = true.obs;
  void hideChatbotIntro() {
    showChatTooltip.value = false;
  }
  @override
  void onInit() {
    super.onInit();
    selectedPageIndex.value =
        itemsNav.indexWhere((item) => item.route == "/home");
  }

  Route? onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case '/home':
        return GetPageRoute(
          settings: settings,
          page: () => HomePage(),
          binding: HomeBinding(),
          transition: Transition.fadeIn,
        );
      case '/tracuu':
        return GetPageRoute(
          settings: settings,
          page: () => const TracuulistPage(),
          binding: TracuulistBinding(),
          transition: Transition.fadeIn,
        );
      case '/tintuc':
        return GetPageRoute(
          settings: settings,
          page: () => TinTucPage(),
          binding: TinTucBinding(),
          transition: Transition.fadeIn,
        );
      case '/thongke':
        return GetPageRoute(
          settings: settings,
          page: () => const ThongkelistPage(),
          binding: ThongkelistBinding(),
          transition: Transition.fadeIn,
        );
      case '/canhan':
        return GetPageRoute(
          settings: settings,
          page: () => const ProfilePage(),
          binding: ProfileBinding(),
          transition: Transition.fadeIn,
        );
    }
    return null;
  }

  void onChangeItemBottomBar(int index) {
    if (selectedPageIndex.value == index) return;
    selectedPageIndex.value = index;
    Get.offAndToNamed(itemsNav[index].route, id: 10);
  }
}
