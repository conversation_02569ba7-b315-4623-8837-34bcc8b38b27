import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/routes/routes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/features/main/presentation/controller/main_controller.dart';
import 'package:attp_2024/features/main/presentation/widgets/customs_navigation_bar_v1.dart';

class MainPage extends GetView<MainController> {
  const MainPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: LayoutBuilder(
        builder: (context, constraints) {
          double chatbotSize = constraints.maxWidth * 0.12;
          double tooltipBottomSpacing = chatbotSize + 90;

          return Stack(
            children: [
              Navigator(
                key: Get.nestedKey(10),
                initialRoute: "/home",
                onGenerateRoute: controller.onGenerateRoute,
              ),
              Obx(() {
                return controller.showChatTooltip.value
                    ? Positioned(
                  right: 15,
                  bottom: tooltipBottomSpacing,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 5,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Text("Chat với trợ lý ảo NTSOFT SOFI", style: TextStyle(color: AppColors.bgChatbot),),
                        const SizedBox(width: 8),
                        GestureDetector(
                          onTap: () => controller.showChatTooltip.value = false,
                          child: const Icon(Icons.close, size: 16),
                        ),
                      ],
                    ),
                  ),
                )
                    : const SizedBox.shrink();
              }),
              Positioned(
                right: 15,
                bottom: 80,
                child: GestureDetector(
                  onTap: () {
                    Get.toNamed(Routes.chatbot);
                  },
                  child: Image.asset(
                    AppImageString.chatbotImgGIF,
                    width: chatbotSize,
                    height: chatbotSize,
                  ),
                ),
              ),
              const Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: BottomNavigationBarV1(),
              ),
            ],
          );
        },
      ),
    );
  }
}