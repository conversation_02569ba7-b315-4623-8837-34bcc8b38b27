import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/routes/routes.dart';
import 'package:attp_2024/features/main/model/dropdown_menu_item_model.dart';

class CustomDropdownShowMore extends StatelessWidget {
  const CustomDropdownShowMore({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: DropdownButtonHideUnderline(
        child: DropdownButton2(
          customButton: const Icon(
            CupertinoIcons.house,
            size: AppDimens.textSize24,
            color: AppColors.white,
          ),
          items: [
            ...MenuItems.firstItems.map(
              (item) => DropdownMenuItem<DropdownMenuItemModel>(
                value: item,
                child: MenuItems.buildItem(item),
              ),
            ),
            const DropdownMenuItem<Divider>(enabled: true, child: SizedBox()),
          ],
          onChanged: (value) {
            MenuItems.onChanged(value as DropdownMenuItemModel);
          },
          dropdownStyleData: DropdownStyleData(
            width: 80.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: Colors.white,
            ),
            offset: Offset(-35.w, 22.5.h),
          ),
          menuItemStyleData: MenuItemStyleData(
            customHeights: [
              ...List<double>.filled(MenuItems.firstItems.length, 48),
              8,
            ],
          ),
        ),
      ),
    );
  }
}

abstract class MenuItems {
  static const List<DropdownMenuItemModel> firstItems = [home, share];
  static const home = DropdownMenuItemModel(
      text: 'Thu thập cung lao động ban đầu', icon: CupertinoIcons.house);

  static const share = DropdownMenuItemModel(
      text: 'Thu thập cầu lao động ban đầu',
      icon: CupertinoIcons.building_2_fill);

  static Widget buildItem(DropdownMenuItemModel item) {
    return Row(
      children: [
        Container(
            width: 35,
            height: 35,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(100),
                color: AppColors.primary.withOpacity(.1)),
            child: Icon(item.icon, color: AppColors.primary, size: 18)),
        SizedBox(
          width: 2.w,
        ),
        Expanded(
          child: Text(
            item.text,
            style: const TextStyle(
              color: AppColors.lightThemePrimaryText,
            ),
          ),
        ),
      ],
    );
  }

  // Handle what happens when each menu item is selected
  static void onChanged(DropdownMenuItemModel item) {
    switch (item) {
      case MenuItems.home:
        Get.toNamed("");
        break;
      case MenuItems.share:
        Get.toNamed(Routes.thongTinChung);
        break;
    }
  }
}
