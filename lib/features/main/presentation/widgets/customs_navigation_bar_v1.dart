import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/features/main/presentation/controller/main_controller.dart';
import 'package:attp_2024/features/main/presentation/widgets/custom_dropdown_show_more.dart';

import '../../../nav/home/<USER>/pages/home_page.dart';

class BottomNavigationBarV1 extends GetView<MainController> {
  final double heightNav;
  const BottomNavigationBarV1({
    super.key,
    this.heightNav = 85,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: heightNav,
      child: Stack(
        children: [
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 5),
              decoration: const BoxDecoration(
                color: AppColors.bottomNav,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              height: heightNav * .75,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: controller.itemsNav
                        .sublist(0, 2)
                        .map((navItem) => _buildItemNav(
                            title: navItem.label,
                            icon: navItem.icon,
                            index: controller.itemsNav.indexOf(navItem)))
                        .toList(),
                  ),
                  Row(
                    children: controller.itemsNav
                        .sublist(2, 4)
                        .map((navItem) => _buildItemNav(
                            title: navItem.label,
                            icon: navItem.icon,
                            index: controller.itemsNav.indexOf(navItem)))
                        .toList(),
                  ),
                ],
              ),
            ),
          ),

          // Add Button
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Align(
              alignment: Alignment.center,
              child: _buildAddButtonNav(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddButtonNav() {
    return InkWell(
      onTap: () {
        controller.onChangeItemBottomBar(4);
      },
      child: Container(
        width: 70,
        height: 70,
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(1000),
          border: Border.all(width: 5, color: AppColors.white),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10000),
            color: AppColors.primary,
          ),
          child: Obx(() {
            bool isActive = controller.selectedPageIndex.value == 4;
            return const Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  CupertinoIcons.house,
                  color: AppColors.white,
                ),
              ],
            );
          }),
        ),
      ),
    );
  }

  Widget _buildItemNav({
    required String title,
    required IconData icon,
    required int index,
  }) {
    return Obx(() {
      bool isActive = controller.selectedPageIndex.value == index;
      return InkWell(
        borderRadius: BorderRadius.circular(1000),
        onTap: () {
          controller.onChangeItemBottomBar(index);
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          padding: const EdgeInsets.all(5),
          width: 85,
          height: 60,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(1000),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: isActive ? AppColors.primary : AppColors.grey,
              ),
              TextWidget(
                text: title,
                maxLines: 1,
                size: AppDimens.textSize12,
                color: isActive ? AppColors.primary : AppColors.grey,
              ),
            ],
          ),
        ),
      );
    });
  }
}
