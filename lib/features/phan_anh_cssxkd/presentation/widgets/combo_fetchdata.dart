import 'dart:developer';

import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';

// ignore: must_be_immutable
class CustomComboBoxFetchData extends StatelessWidget {
  final RxString selectedItem = ''.obs;
  final RxString selectedItemDisplay = ''.obs;
  final Function(DropdownModel? selected) onChange;
  final Function(String query)? onChangeSearch;
  final RxList<dynamic> dropDownList;
  final bool isEnabled;
  final bool isRequired;
  final String? errorText;
  final double? vertical;
  final double? horizontal;
  final bool? showDelete;
  final Function? delete;
  final double? borderRadius;
  String valueSelected;
  final String title;
  final double? height;
  final String hintSearch;
  final String? titleSearch;
  final double? heighCombo;
  final bool showCloseButton;
  final double weight;
  final DropdownModel? defaultSelectedItem;
  final String? Function(DropdownModel?)? validator;
  final FocusNode _focusNode = FocusNode();
  Widget? prefixIcon;
  final bool hideTitle;
  final bool isLoading;

  CustomComboBoxFetchData({
    super.key,
    this.delete,
    this.errorText,
    this.showDelete,
    required this.dropDownList,
    required this.onChange,
    this.onChangeSearch,
    this.isEnabled = true,
    this.isRequired = false,
    this.vertical,
    this.horizontal,
    this.borderRadius,
    this.weight = 200,
    this.height,
    required this.title,
    this.hintSearch = "Tìm kiếm ...",
    this.titleSearch,
    this.valueSelected = "",
    this.defaultSelectedItem,
    this.heighCombo,
    this.showCloseButton = true,
    this.validator,
    this.prefixIcon,
    this.hideTitle = false,
    this.isLoading = false,
  }) {
    selectedItem.value = defaultSelectedItem?.id ?? "";
    selectedItemDisplay.value = defaultSelectedItem?.display ?? "";
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!hideTitle)
          Row(
            children: [
              Text(
                title,
                style: const TextStyle(
                    fontSize: AppDimens.sizeTitle,
                    fontWeight: FontWeight.w500,
                    color: CupertinoColors.black),
              ),
              if (isRequired)
                const Text(
                  ' *',
                  style: TextStyle(color: Colors.red, fontSize: 16),
                ),
            ],
          ),
        if (!hideTitle) const SizedBox(height: 4),
        InkWell(
          onTap: isEnabled
              ? () {
                  FocusScope.of(context).requestFocus(FocusNode());
                  _showDropdown(context);
                }
              : null,
          child: Container(
            width: weight,
            height: height ?? 45,
            padding: EdgeInsets.symmetric(
              horizontal: horizontal ?? 10,
              vertical: vertical ?? 12,
            ),
            decoration: BoxDecoration(
              color: isEnabled ? Colors.white : Colors.grey[200],
              border: Border.all(
                  color: errorText?.isNotEmpty ?? false
                      ? AppColors.error
                      : AppColors.borderInput1,
                  width: .3),
              borderRadius: BorderRadius.circular(borderRadius ?? 5),
            ),
            child: Row(
              children: [
                if (prefixIcon != null) ...[
                  prefixIcon!,
                  const SizedBox(width: 10),
                ],
                Obx(() => Padding(
                      padding: const EdgeInsets.fromLTRB(0.0, 0.0, 0.0, 0.0),
                      child: SizedBox(
                        width: weight * .6,
                        child: TextWidget(
                          text: selectedItemDisplay.value.isNotEmpty
                              ? selectedItemDisplay.value
                              : '-Chọn-',
                          color: isEnabled ? Colors.black : Colors.grey,
                          maxLines: 1,
                        ),
                      ),
                    )),
                const Spacer(),
                Obx(() => selectedItem.value.isNotEmpty && (showDelete ?? true) && isEnabled
                    ? InkWell(
                  child: const Icon(
                    CupertinoIcons.clear_circled_solid,
                    color: AppColors.iconColors,
                    size: 18,
                  ),
                  onTap: () {
                    if (delete != null) {
                      delete?.call();
                    }
                    selectedItem.value = '';
                    onChange(null);
                  },
                )
                    : const SizedBox()),
                const Padding(
                  padding: EdgeInsets.only(right: 0.0),
                  child: Icon(Icons.arrow_drop_down_outlined),
                ),
              ],
            ),
          ),
        ),
        Column(
          children: [
            SizedBox(
              height: 2.h,
              child: Text(
                errorText ?? '',
                style: const TextStyle(color: Colors.red, fontSize: 12),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _showDropdown(BuildContext context) {
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      shape: const BeveledRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(5),
          topRight: Radius.circular(5),
        ),
      ),
      builder: (BuildContext context) {
        return Padding(
          padding: MediaQuery.of(context).viewInsets,
          child: SingleChildScrollView(
            child: Container(
              height: heighCombo ?? 90.h,
              padding: const EdgeInsets.all(8.0),
              child: Column(
                children: [
                  if (titleSearch != null)
                    Padding(
                      padding: EdgeInsets.only(
                        top: 1.h,
                        bottom: 2.5.h,
                      ),
                      child: Center(
                        child: Text(
                          titleSearch ?? "",
                          style: TextStyle(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 0.w),
                    child: TextField(
                      focusNode: _focusNode,
                      onChanged: (value) {
                        if (onChangeSearch != null) {
                          onChangeSearch!(value);
                        }
                      },
                      decoration: InputDecoration(
                        contentPadding:
                            const EdgeInsets.symmetric(vertical: 8.0),
                        hintText: hintSearch,
                        hintStyle: const TextStyle(color: Colors.grey),
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(3.0),
                          borderSide: BorderSide.none,
                        ),
                        filled: true,
                        fillColor: Colors.grey[200],
                      ),
                    ),
                  ),
                  const SizedBox(height: 8.0),
                  Expanded(
                    child: Obx(() {
                      if (isLoading) {
                        return const Center(
                          child: CircularProgressIndicator(),
                        );
                      }

                      if (dropDownList.isEmpty) {
                        return Center(
                          child: Image.asset(
                            width: 30.w,
                            'assets/images/hover.png',
                          ),
                        );
                      }

                      return ListView.builder(
                          itemCount: dropDownList.length,
                          itemBuilder: (context, index) {
                            final item = DropdownModel(
                              id: dropDownList[index]['CoSoSXKDID'],
                              display: dropDownList[index]['TenCoSoSXKD'],
                            );
                            bool isSelected = item.id == selectedItem.value;

                            return ListTile(
                              minTileHeight: 2.h,
                              tileColor: isSelected ? Colors.blue[100] : null,
                              title: Text(item.display ?? ''),
                              onTap: () {
                                selectedItem.value = item.display ?? '';
                                valueSelected = item.display ?? "";
                                Navigator.pop(context);
                                onChange(item);
                              },
                              trailing: isSelected
                                  ? const Icon(Icons.check, color: Colors.blue)
                                  : null,
                            );
                          });
                    }),
                  ),
                  showCloseButton
                      ? InkWell(
                          onTap: () {
                            Get.back();
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            decoration: BoxDecoration(
                              color: Colors.red.withOpacity(.2),
                              borderRadius: BorderRadius.circular(5),
                            ),
                            margin: EdgeInsets.all(2.w),
                            child: const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  size: 20,
                                  Icons.close,
                                  color: Colors.red,
                                ),
                                SizedBox(width: 5),
                                Text(
                                  "Đóng",
                                  style: TextStyle(
                                    color: Colors.red,
                                    fontSize: 16,
                                  ),
                                )
                              ],
                            ),
                          ),
                        )
                      : const SizedBox(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class DropdownModel {
  String? display;
  String? id;

  DropdownModel({this.display, this.id});

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DropdownModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
