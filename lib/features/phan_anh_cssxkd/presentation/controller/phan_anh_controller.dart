import 'dart:async';
import 'dart:developer';

import 'package:attp_2024/core/data/models/user_access_model.dart';
import 'package:attp_2024/core/routes/routes.dart';
import 'package:attp_2024/core/ui/snackbar/snackbar_until.dart';
import 'package:dart_ipify/dart_ipify.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/data/api/configs/dio_configs.dart';
import 'package:attp_2024/core/data/api/services/proc/proc_service.dart';
import 'package:attp_2024/core/services/uploads/upload_service.dart';
import 'package:attp_2024/core/services/user_use_case.dart';

class PhanAnhController extends GetxController {
  var phanAnhItemData = [].obs;
  var isLoading = false.obs;
  var hoVaTen = ''.obs;
  var soDienThoai = ''.obs;
  var email = ''.obs;
  var cssxkdID = ''.obs;
  var tenCssxkd = ''.obs;
  var provinceID = ''.obs;
  var districtID = ''.obs;
  var communeID = ''.obs;
  var villageID = ''.obs;
  var nguoiDaiDien = ''.obs;
  var diaChi = ''.obs;
  var tieuDe = ''.obs;
  var noiDung = ''.obs;
  var anDanh = false.obs;
  var donViId = '';
  var userId = '';
  var ipv4 = ''.obs;
  var kinhDo = ''.obs;
  var viDo = ''.obs;

  var choTiepNhan = '80'.obs;
  var dangXuLy = '81'.obs;
  var daXuLy = '82'.obs;
  var tuChoi = '83'.obs;

  var arguments = {}.obs;
  var cssxkdNameArg = ''.obs;
  var cssxkdIDArg = ''.obs;
  var checked = true.obs;
  final isLoadingVideo = false.obs;
  var isCompressingVideo = false.obs;

  UserAccessModel? userAccessModel;

  @override
  void onInit() async {
    super.onInit();
    checked.value = true;
    _dioService.init();
    await Future.wait([fetchAllProvinces()]);
    donViId = await _getDonViID();
    userId = await _getUserId();
    fetchIpAddress();
    fetchAllPAATTPByTrangThai(daXuLy.value, false);
    await loadInfoProfile();
    log('${userAccessModel?.siteURL}', name: 'akr');
  }

  Future<void> loadInfoProfile() async {
    userAccessModel = await UserUseCase.getUser();
    // update(["bodyID"]);
  }

  void updateArguments(dynamic newArgs) {
    arguments.value = newArgs;
    cssxkdID.value = newArgs['CoSoSXKDID'];
    tenCssxkd.value = newArgs['TenCoSoSXKD'];
    nguoiDaiDien.value = newArgs['NguoiDaiDien'];
    diaChi.value = newArgs['DiaChiCS'];

    log(hoVaTen.value, name: 'akr');
    checked.value = false;
  }

  @override
  void onClose() {
    clear();
    super.onClose();
  }

  final ProcService _procService = Get.find<ProcService>();
  final DioService _dioService = Get.find<DioService>();

  var cssxkdList = [].obs;
  var provinces = [].obs;
  var districts = [].obs;
  var communes = [].obs;
  var villages = [].obs;
  var cSSXKDSelectedData = [].obs;
  var phanHoiSelectedData = [].obs;

  var isLoadingProvinces = false.obs;
  var isLoadingDistricts = false.obs;
  var isLoadingCommunes = false.obs;
  var isLoadingVillages = false.obs;

  var errPhoneNumber = ''.obs;
  var errEmail = ''.obs;
  var errName = ''.obs;
  var errTitle = ''.obs;
  var errNoiDung = ''.obs;
  var errCssxkd = ''.obs;
  var defaultValue = 'Không có thông tin'.obs;

  var numselectedFiles = 0.obs;
  var selectedFiles = <String>[].obs;
  var selectedValueString = '';

  Timer? _debounce;

  void addFiles(List<String> files) {
    selectedFiles.addAll(files);
    numselectedFiles.value = selectedFiles.length;
  }

  void removeFile(String file) {
    selectedFiles.remove(file);
  }

  void clearFiles() {
    selectedFiles.clear();
  }

  Future<String> _getDonViID() async {
    final userId = await UserUseCase.getUser();
    return userId!.donViID;
  }

  Future<String> _getUserId() async {
    final userId = await UserUseCase.getUser();
    return userId!.userID;
  }

  Future<void> fetchIpAddress() async {
    ipv4.value = await Ipify.ipv4();
  }

  Future<void> fetchCSSXKDbyID(String id) async {
    isLoading.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "CoSoSXKDID", "type": "Guid", "value": id}
    ];
    try {
      List<dynamic> resItem = await _procService.callProc(
          "Proc_Mobile_Get_CoSoSXKD_By_CoSoSXKDID", body);
      List<Map<String, dynamic>> mappedCssxkdResponse =
          resItem.cast<Map<String, dynamic>>();
      cSSXKDSelectedData.assignAll(
        mappedCssxkdResponse.map((Map<String, dynamic> itemJson) {
          return {
            "CoSoSXKDID": itemJson['CoSoSXKDID'],
            "MaCoSoSXKD": itemJson['MaCoSoSXKD'],
            "TenCoSoSXKD": itemJson['TenCoSoSXKD'],
            "SoGPKD": itemJson['SoGPKD'],
            "EmailCS": itemJson['EmailCS'],
            "DiaBanHCID_Tinh": itemJson['DiaBanHCID_Tinh'],
            "DiaBanHCID_Huyen": itemJson['DiaBanHCID_Huyen'],
            "DiaBanHCID_Xa": itemJson['DiaBanHCID_Xa'],
            "DiaBanHCID_Thon": itemJson['DiaBanHCID_Thon'],
            "DiaChiCS": itemJson['DiaChiCS'],
            "LoaiHinhCoSoID": itemJson['LoaiHinhCoSoID'],
            "HoVaTen": itemJson['HoVaTen'],
            "ChucVuID": itemJson['ChucVuID'],
            "DiDong": itemJson['DiDong'], // Người đại diện pháp luật
            "SoDienThoai": itemJson['SoDienThoai'], // Sđt cơ quan
            "TrangThai_GCN": itemJson['TrangThai_GCN'],
            "CoQuanCapGPKD": itemJson['CoQuanCapGPKD'],
            "NgayCapGCN": itemJson['NgayCapGCN'],
            "NgayHetHanGCN": itemJson['NgayHetHanGCN'],
          };
        }).toList(),
      );
      // log(cSSXKDSelectedData.toString(), name: 'akr2');
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> fetchATTPPhanAnhbyATTPID(String id) async {
    isLoading.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "PAATTPID", "type": "Guid", "value": id}
    ];
    try {
      List<dynamic> resItem = await _procService.callProc(
          "Proc_Mobile_GetPAATTPPhanHoi_ByPAATTPID", body);
      List<Map<String, dynamic>> mappedCssxkdResponse =
          resItem.cast<Map<String, dynamic>>();
      phanHoiSelectedData.assignAll(
        mappedCssxkdResponse.map((Map<String, dynamic> itemJson) {
          return {
            "PAATTPPhanHoiID": itemJson['PAATTPPhanHoiID'],
            "TuChoi": itemJson['TuChoi'],
            "CongKhai": itemJson['CongKhai'],
            "SoGPKD": itemJson['SoGPKD'],
            "PAATTPID": itemJson['PAATTPID'],
            "CoSoSXKDID": itemJson['CoSoSXKDID'],
            "MaCoSoSXKD": itemJson['MaCoSoSXKD'],
            "TenCoSoSXKD": itemJson['TenCoSoSXKD'],
            "NgayCapGPKD": itemJson['NgayCapGPKD'],
            "NgayCapGCN": itemJson['NgayCapGCN'],
            "NgayHetHanGCN": itemJson['NgayHetHanGCN'],
            "DiaBanHCID_Tinh": itemJson['DiaBanHCID_Tinh'],
            "TinhName": itemJson['TinhName'],
            "DiaBanHCID_Huyen": itemJson['DiaBanHCID_Huyen'],
            "HuyenName": itemJson['HuyenName'],
            "DiaBanHCID_Xa": itemJson['DiaBanHCID_Xa'],
            "XaName": itemJson['XaName'],
            "DiaBanHCID_Thon": itemJson['DiaBanHCID_Thon'],
            "ThonName": itemJson['ThonName'],
            "DiaChiCoSo": itemJson['DiaChiCoSo'],
            "LoaiHinhCoSoID": itemJson['LoaiHinhCoSoID'],
            "HoVaTen": itemJson['HoVaTen'],
            "ChucVuID": itemJson['ChucVuID'],
            "ChucVuNguoiDaiDien": itemJson['ChucVuNguoiDaiDien'],
            "SoDienThoai": itemJson['SoDienThoai'],
            "EmailCS": itemJson['EmailCS'],
            "SoGCN": itemJson['SoGCN'],
            "TrangThai_GCN": itemJson['TrangThai_GCN'],
            "NoiDung": itemJson['NoiDung'],
            "TrangThai_XuLy": itemJson['TrangThai_XuLy'],
            "DinhKem": itemJson['DinhKem'],
            "UserID": itemJson['UserID'],
            "DonViID": itemJson['DonViID'],
            "NgayTao": itemJson['NgayTao'],
            "NgayCapNhat": itemJson['NgayCapNhat'],
            "NgayPhanHoi": itemJson['NgayPhanHoi'],
            "NhanVienID": itemJson['NhanVienID'],
            "TenNhanVien": itemJson['TenNhanVien'],
            "ChucVuID_NhanVien": itemJson['ChucVuID_NhanVien'],
            "ChucVuNhanVien": itemJson['ChucVuNhanVien'],
            "ViPhamATTPID": itemJson['ViPhamATTPID'],
            "NoiDungXuLy": itemJson['NoiDungXuLy'],
            "HinhThucXuLy": itemJson['HinhThucXuLy']
          };
        }).toList(),
      );
      // log(phanHoiSelectedData.toString(), name: 'PhanHoiData');
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> fetchAllPAATTPByTrangThai(
      String trangThai, bool fetchWithUserID) async {
    isLoading.value = true;
    // log(userId, name: 'akr');
    final List<Map<String, dynamic>> body = fetchWithUserID
        ? [
            {"name": "UserID", "type": "Guid", "value": userId}
          ]
        : [
            {"name": "TrangThai", "type": "String", "value": trangThai},
            {"name": "DonViID", "type": "Guid", "value": donViId},
            {"name": "CongKhai", "type": "Boolean", "value": true}
          ];
    try {
      List<dynamic> resItem = await _procService.callProc(
          fetchWithUserID
              ? "Proc_Mobile_GetAll_PAATTP_ByUserID"
              : "Proc_Mobile_GetAll_PAATTP_ByTrangThai",
          body);
      List<Map<String, dynamic>> mappedResponse =
          resItem.cast<Map<String, dynamic>>();
      phanAnhItemData.assignAll(
        mappedResponse.map((Map<String, dynamic> itemJson) {
          return {
            "PAATTPID": itemJson['PAATTPID'],
            "MaPAATTP": itemJson['MaPAATTP'],
            "TieuDe": itemJson['TieuDe'],
            "NoiDung": itemJson['NoiDung'],
            "HoVaTen": itemJson['HoVaTen'],
            "SoDienThoai": itemJson['SoDienThoai'],
            "Email": itemJson['Email'],
            "AnDanh": itemJson['AnDanh'],
            "NgayGui": itemJson['NgayGui'],
            "DinhKem_PA": itemJson['DinhKem_PA'],
            "CoSoSXKDID": itemJson['CoSoSXKDID'],
            "MaCoSoSXKD": itemJson['MaCoSoSXKD'],
            "TenCoSoSXKD": itemJson['TenCoSoSXKD'],
            "SoGPKD": itemJson['SoGPKD'],
            "CoQuanCapGPKD": itemJson['CoQuanCapGPKD'],
            "SoDienThoai_CS": itemJson['SoDienThoai_CS'],
            "EmailCS": itemJson['EmailCS'],
            "DiaChiCS": itemJson['DiaChiCS'],
            "NguoiDaiDien": itemJson['NguoiDaiDien'],
            "ChucVuID_NguoiDaiDien": itemJson['ChucVuID_NguoiDaiDien'],
            "TrangThai_GCN": itemJson['TrangThai_GCN'],
            "NgayCapGCN": itemJson['NgayCapGCN'],
            "NgayHetHanGCN": itemJson['NgayHetHanGCN'],
            "KinhDo": itemJson['KinhDo'],
            "ViDo": itemJson['ViDo'],
            "DiaBanHCID_Tinh": itemJson['DiaBanHCID_Tinh'],
            "DiaBanHCID_Huyen": itemJson['DiaBanHCID_Huyen'],
            "DiaBanHCID_Xa": itemJson['DiaBanHCID_Xa'],
            "DiaBanHCID_Thon": itemJson['DiaBanHCID_Thon'],
            "LoaiHinhCoSoID": itemJson['LoaiHinhCoSoID'],
            "TrangThai": itemJson['TrangThai'],
            "NhanVienID_XuLy_PA": itemJson['NhanVienID_XuLy_PA'],
            "TenNhanVien_XuLy_PA": itemJson['TenNhanVien_XuLy_PA'],
            "NgayXuLy_PA": itemJson['NgayXuLy_PA'],
            "ChucVuID_XuLy_PA": itemJson['ChucVuID_XuLy_PA'],
            "TenChucVu_XuLy_PA": itemJson['TenChucVu_XuLy_PA'],
            "NoiDung_XuLy_PA": itemJson['NoiDung_XuLy_PA'],
            "UserID_PA": itemJson['UserID_PA'],
            "NgayCapNhat_PA": itemJson['NgayCapNhat_PA'],
            "DonViID": itemJson['DonViID'],
            "DiaChi": itemJson['DiaChi'],
            "PAATTPPhanHoiID": itemJson['PAATTPPhanHoiID'],
            "NhanVienID_XuLy_PH": itemJson['NhanVienID_XuLy_PH'],
            "TenNhanVien_XuLy_PH": itemJson['TenNhanVien_XuLy_PH'],
            "ChucVuID_XuLy_PH": itemJson['ChucVuID_XuLy_PH'],
            "TenChucVu_XuLy_PH": itemJson['TenChucVu_XuLy_PH'],
            "NgayXuLy_PH": itemJson['NgayXuLy_PH'],
            "NoiDung_PH": itemJson['NoiDung_PH'],
            "TenHinhThucXuLy": itemJson['TenHinhThucXuLy'],
            "DinhKem_PH": itemJson['DinhKem_PH'],
            "TrangThai_XuLy_PH": itemJson['TrangThai_XuLy_PH']
          };
        }).toList(),
      );
      // log(phanAnhItemData.toString(), name: 'phananh');
    } catch (e) {
      print("ERROR OCCURRED: $e");
      log('$e', name: 'ERROR OCCURRED');
    } finally {
      isLoading.value = false;
    }
  }

  // Hàm gán giá trị cho biến
  void filterByCoSoSXKDID(String id) {
    var result = cssxkdList.firstWhere(
      (item) => item["CoSoSXKDID"] == id,
      orElse: () => null,
    );
    cssxkdID.value = id;
    tenCssxkd.value = result["TenCoSoSXKD"];
    provinceID.value = result["DiaBanHCID_Tinh"];
    districtID.value = result["DiaBanHCID_Huyen"];
    communeID.value = result["DiaBanHCID_Xa"];
    villageID.value = result["DiaBanHCID_Thon"];
    nguoiDaiDien.value = result["HoVaTen"];
    diaChi.value = result["DiaChiCS"];
  }

  Future<void> fetchByKeyword(String keyword) async {
    if (_debounce?.isActive ?? false) {
      _debounce?.cancel();
    }

    // Đặt Timer mới
    _debounce = Timer(const Duration(milliseconds: 500), () {
      fetchAllCSSXKDByKeyword(keyword);
    });
  }

  Future<void> fetchAllCSSXKDByKeyword(String keyword) async {
    isLoading.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "TuKhoa", "type": "string", "value": keyword}
    ];
    try {
      List<dynamic> cssxkdResponse = await _procService.callProc(
          "Proc_Mobile_GetCombo_CoSoSXKD_byTuKhoa", body);
      List<Map<String, dynamic>> mappedCssxkdResponse =
          cssxkdResponse.cast<Map<String, dynamic>>();
      cssxkdList.assignAll(
        mappedCssxkdResponse.map((Map<String, dynamic> cssxkds) {
          return {
            "CoSoSXKDID": cssxkds['CoSoSXKDID'],
            "TenCoSoSXKD": cssxkds['TenCoSoSXKD'],
            "DiaBanHCID_Tinh": cssxkds['DiaBanHCID_Tinh'],
            "DiaBanHCID_Huyen": cssxkds['DiaBanHCID_Huyen'],
            "DiaBanHCID_Xa": cssxkds['DiaBanHCID_Xa'],
            "DiaBanHCID_Thon": cssxkds['DiaBanHCID_Thon'],
            "HoVaTen": cssxkds['HoVaTen'],
            "DiaChiCS": cssxkds['DiaChiCS'],
          };
        }).toList(),
      );
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> fetchAllProvinces() async {
    isLoadingProvinces.value = true;
    final List<Map<String, dynamic>> body = [];
    try {
      List<dynamic> provincesResponse =
          await _procService.callProc("Proc_Mobile_GetAll_DiaBanHCTinh", body);
      List<Map<String, dynamic>> mappedTinhResponse =
          provincesResponse.cast<Map<String, dynamic>>();
      provinces.assignAll(
        mappedTinhResponse.map((Map<String, dynamic> tinhs) {
          return {
            "value": tinhs['DiaBanHCID'],
            "display": tinhs['TenDiaBan'],
          };
        }).toList(),
      );
      print('Tinh $provinces');
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isLoadingProvinces.value = false;
    }
  }

  Future<void> fetchAllDistricts({required String tinhID}) async {
    isLoadingDistricts.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "TinhID", "type": "guid", "value": tinhID},
    ];
    try {
      List<dynamic> districtsResponse =
          await _procService.callProc("Proc_Mobile_GetAll_DiaBanHCHuyen", body);
      List<Map<String, dynamic>> mappedHuyenResponse =
          districtsResponse.cast<Map<String, dynamic>>();

      districts.assignAll(
        mappedHuyenResponse.map((Map<String, dynamic> huyens) {
          return {
            "value": huyens['DiaBanHCID'],
            "display": huyens['TenDiaBan'],
          };
        }).toList(),
      );
      print('Huyen $districts');
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isLoadingDistricts.value = false;
    }
  }

  Future<void> fetchAllCommunes({required String huyenID}) async {
    isLoadingCommunes.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "HuyenID", "type": "guid", "value": huyenID},
    ];
    try {
      List<dynamic> communesResponse =
          await _procService.callProc("Proc_Mobile_GetAll_DiaBanHCXa", body);
      List<Map<String, dynamic>> mappedXaResponse =
          communesResponse.cast<Map<String, dynamic>>();

      communes.assignAll(
        mappedXaResponse.map((Map<String, dynamic> xas) {
          return {
            "value": xas['DiaBanHCID'],
            "display": xas['TenDiaBan'],
          };
        }).toList(),
      );
      print('Xa $communes');
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isLoadingCommunes.value = false;
    }
  }

  Future<void> fetchAllVillages({required String xaID}) async {
    isLoadingVillages.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "xaID", "type": "guid", "value": xaID},
    ];
    try {
      List<dynamic> villagesResponse =
          await _procService.callProc("Proc_Mobile_GetAll_DiaBanHCThon", body);
      List<Map<String, dynamic>> mappedThonResponse =
          villagesResponse.cast<Map<String, dynamic>>();

      villages.assignAll(
        mappedThonResponse.map((Map<String, dynamic> thons) {
          return {
            "value": thons['DiaBanHCID'],
            "display": thons['TenDiaBan'],
          };
        }).toList(),
      );
      print('Thon $villages');
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isLoadingVillages.value = false;
    }
  }

  // Future<void> HandleSubmit(BuildContext context) async {
  //   if (selectedFiles.isNotEmpty) await luuTepTinPAATTP();
  //   await luuThongTinPAATTP(context);
  // }

  Future<void> HandleSubmit(BuildContext context) async {
    try {
      isLoading.value = true;

      if (selectedFiles.isNotEmpty) {
        bool isUploadSuccess = await luuTepTinPAATTP();
        if (!isUploadSuccess) {
          isLoading.value = false;
          return;
        }
      }
      await luuThongTinPAATTP(context);
    } catch (e) {
      print("Lỗi trong HandleSubmit: $e");
      isLoading.value = false;
    }
  }

  // Future<void> luuTepTinPAATTP() async {
  //   isLoading.value = true;
  //   final res = await UploadService().uploadMutiMediaFiles(
  //       selectedFiles, "HA", "PAATTP", 'https://attphg.nhattamsoft.vn');
  //   selectedValueString =
  //       res!.data['data'].map((path) => path).join('*').toString();
  //   isLoading.value = false;
  // }
  Future<bool> luuTepTinPAATTP() async {
    try {
      final res = await UploadService().uploadMutiMediaFiles(
          selectedFiles, "HA", "PAATTP", "${userAccessModel?.siteURL}");

      if (res == null) {
        Get.snackbar("Lỗi upload", "Tải file thất bại hoặc dung lượng quá lớn");
        return false;
      }
      selectedValueString = res.data['data'].map((path) => path).join('*~/');
      selectedValueString = '*~/$selectedValueString';
      print(selectedValueString);
      return true;
    } catch (e) {
      print("Lỗi khi lưu tệp tin: $e");
      Get.snackbar("Lỗi", "Có lỗi xảy ra khi tải lên tệp tin.");
      return false;
    }
  }

  Future<void> luuThongTinPAATTP(BuildContext context) async {
    isLoading.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "TieuDe", "type": "String", "value": tieuDe.value},
      {"name": "NoiDung", "type": "String", "value": noiDung.value},
      {"name": "HoVaTen", "type": "String", "value": hoVaTen.value},
      {"name": "SoDienThoai", "type": "String", "value": soDienThoai.value},
      {"name": "Email", "type": "String", "value": email.value},
      {"name": "AnDanh", "type": "Boolean", "value": anDanh.value},
      {"name": "DinhKem", "type": "String", "value": selectedValueString},
      {"name": "CoSoSXKDID", "type": "Guid", "value": cssxkdID.value},
      {"name": "TenCoSoSXKD", "type": "String", "value": tenCssxkd.value},
      {"name": "NguoiDaiDien", "type": "String", "value": nguoiDaiDien.value},
      {"name": "KinhDo", "type": "String", "value": kinhDo.value},
      {"name": "ViDo", "type": "String", "value": viDo.value},
      {"name": "DiaBanHCID_Tinh", "type": "Guid", "value": provinceID.value},
      {"name": "DiaBanHCID_Huyen", "type": "Guid", "value": districtID.value},
      {"name": "DiaBanHCID_Xa", "type": "Guid", "value": communeID.value},
      {"name": "DiaBanHCID_Thon", "type": "Guid", "value": villageID.value},
      {"name": "DonViID", "type": "Guid", "value": donViId},
      {"name": "UserID", "type": "Guid", "value": userId},
      {"name": "DiaChi", "type": "String", "value": diaChi.value},
      {"name": "IP_NguoiGui", "type": "String", "value": ipv4.value}
    ];
    try {
      final response =
          await _procService.callProc("Proc_Mobile_LuuThongTin_PAATTP", body);
      if (response.isNotEmpty) {
        // log(response.toString(), name: 'akr');
        clear();
        Get.delete<PhanAnhController>();
        Get.offNamed(Routes.home);
        SnackbarUtil.showSuccess("Gửi phản ánh thành công!",
            alignment: "bottom");
      } else {
        Get.delete<PhanAnhController>();
        Get.offNamed(Routes.home);
        SnackbarUtil.showError("Có lỗi xảy ra khi gửi dữ liệu!",
            alignment: "bottom");
      }
    } catch (e) {
      print("Error logging in: $e");
      Get.delete<PhanAnhController>();
      Get.offNamed(Routes.home);
      SnackbarUtil.showError(
          "Có lỗi xảy ra khi gửi dữ liệu, vui lòng thử vào lúc khác!",
          alignment: "bottom");
    } finally {
      isLoading.value = false;
    }
  }

  void clear() {
    hoVaTen.value = '';
    soDienThoai.value = '';
    email.value = '';
    cssxkdID.value = '';
    tenCssxkd.value = '';
    provinceID.value = '';
    districtID.value = '';
    communeID.value = '';
    villageID.value = '';
    nguoiDaiDien.value = '';
    diaChi.value = '';
    tieuDe.value = '';
    noiDung.value = '';
    anDanh.value = false;
    kinhDo.value = '';
    viDo.value = '';
    errName.value = '';
    errPhoneNumber.value = '';
    errEmail.value = '';
    errCssxkd.value = '';
    errTitle.value = '';
    errNoiDung.value = '';
    // Reset checked về true khi clear
    checked.value = true;
  }
}
