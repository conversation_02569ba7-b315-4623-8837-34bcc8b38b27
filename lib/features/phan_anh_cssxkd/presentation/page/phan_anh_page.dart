import 'dart:developer';
import 'dart:io';
import 'package:attp_2024/core/routes/routes.dart';
import 'package:attp_2024/features/phan_anh_cssxkd/presentation/widgets/combo_fetchdata.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:attp_2024/core/ui/widgets/button/button_widget.dart';
import 'package:attp_2024/core/ui/widgets/custom_textfield/widgets/custom_textarea.dart';
import 'package:attp_2024/core/ui/widgets/custom_textfield/widgets/custom_textfield.dart';
import 'package:attp_2024/core/ui/widgets/load/loading.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/core/utils/validator.dart';
import 'package:attp_2024/features/phan_anh_cssxkd/presentation/controller/phan_anh_controller.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:video_compress/video_compress.dart';

class PhanAnhPage extends GetView<PhanAnhController> {
  const PhanAnhPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarWidget(
        title: "Phản ánh cơ sở sản xuất kinh doanh ",
        callbackLeading: () {
          controller.clear();
          Get.delete<PhanAnhController>();
          Get.back();
        },
      ),
      body: Loading.LoadingFullScreen(
        isLoading: controller.isLoading,
        body: _buildBody(context),
      ),
    );
  }

  Future<String?> compressVideo(String videoPath) async {
    final compressedVideo = await VideoCompress.compressVideo(
      videoPath,
      quality: VideoQuality.MediumQuality, // hoặc LowQuality để giảm nhiều hơn
      deleteOrigin: false, // Giữ lại file gốc
      includeAudio: true,
    );

    if (compressedVideo != null && compressedVideo.path != null) {
      return compressedVideo.path;
    } else {
      return null;
    }
  }

  Future<void> pickAssets(
      BuildContext context, Function(List<String>) onFilesPicked) async {
    const maxFileSizeInMB = 50; // Dung lượng tối đa mỗi file (MB)
    const maxTotalSizeInMB = 30; // Tổng dung lượng tối đa (MB)
    const maxFileSizeInBytes = maxFileSizeInMB * 1024 * 1024;
    const maxTotalSizeInBytes = maxTotalSizeInMB * 1024 * 1024;

    final List<AssetEntity>? result = await AssetPicker.pickAssets(
      context,
      pickerConfig: const AssetPickerConfig(
        requestType: RequestType.common, // Chọn ảnh và video
        maxAssets: 3,
      ),
    );

    if (result != null && result.isNotEmpty) {
      controller.isLoadingVideo.value = true;
      List<String> filePaths = [];
      int totalSize = 0;

      for (var asset in result) {
        final File? file = await asset.file;
        if (file == null) continue;

        final String extension = file.path.split('.').last.toLowerCase();
        File finalFile = file;

        // Nếu là video, tiến hành nén trước
        if (extension == 'mp4' || extension == 'mov') {
          final compressedPath = await compressVideo(file.path);
          if (compressedPath == null) {
            Get.snackbar(
              'Upload video thất bại',
              'Không thể xử lý video này, vui lòng chọn video khác.',
              snackPosition: SnackPosition.BOTTOM,
            );
            continue;
          }
          finalFile = File(compressedPath);
        }

        final fileSize = await finalFile.length();

        // Kiểm tra dung lượng từng file
        if (fileSize > maxFileSizeInBytes) {
          Get.snackbar(
            'File quá lớn',
            'Dung lượng file ${finalFile.path.split('/').last} vượt quá $maxFileSizeInMB MB.',
            snackPosition: SnackPosition.BOTTOM,
          );
          continue;
        }

        // Kiểm tra tổng dung lượng
        if ((totalSize + fileSize) > maxTotalSizeInBytes) {
          Get.snackbar(
            'Tổng dung lượng quá lớn',
            'Tổng dung lượng đã chọn vượt quá $maxTotalSizeInMB MB.',
            snackPosition: SnackPosition.BOTTOM,
          );
          break; // Nếu tổng dung lượng vượt quá, thoát khỏi vòng lặp luôn
        }

        // Thêm file hợp lệ vào danh sách
        totalSize += fileSize;
        filePaths.add(finalFile.path);
      }

      controller.isLoadingVideo.value = false;

      if (filePaths.isNotEmpty) {
        onFilesPicked(filePaths);
      } else {
        Get.snackbar(
          'Không có file hợp lệ',
          'Tất cả file đều không hợp lệ hoặc vượt dung lượng cho phép.',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    }
  }

  Widget _buildBody(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Obx(
          () => Column(
            children: [
              TextFieldWidget(
                title: 'Họ và tên',
                isRequired: true,
                placeholder: 'Nhập họ và tên',
                setValue: controller.hoVaTen.value,
                initialValue: controller.hoVaTen.value,
                errorWidget: controller.errName.value,
                onChange: (e) {
                  controller.hoVaTen.value = e;
                  controller.hoVaTen.value.isEmpty
                      ? controller.errName.value =
                          'Họ tên không được để trống !'
                      : controller.errName.value = '';
                },
              ),
              TextFieldWidget(
                title: 'Số điện thoại',
                isRequired: true,
                placeholder: 'Nhập số điện thoại',
                setValue: controller.soDienThoai.value,
                initialValue: controller.soDienThoai.value,
                errorWidget: controller.errPhoneNumber.value,
                onChange: (e) {
                  controller.soDienThoai.value = e;
                  controller.soDienThoai.value.isEmpty
                      ? controller.errPhoneNumber.value =
                          'Số điện thoại không được để trống !'
                      : !Validators.validPhone(controller.soDienThoai.value)
                          ? controller.errPhoneNumber.value =
                              'Số điện thoại không hợp lệ !'
                          : controller.errPhoneNumber.value = '';
                },
              ),
              TextFieldWidget(
                title: 'Email',
                isRequired: true,
                placeholder: 'Nhập địa chỉ email',
                setValue: controller.email.value,
                initialValue: controller.email.value,
                errorWidget: controller.errEmail.value,
                onChange: (e) {
                  controller.email.value = e;
                  controller.email.value.isEmpty
                      ? controller.errEmail.value =
                          'Email không được để trống !'
                      : !Validators.validateEmail(controller.email.value)
                          ? controller.errEmail.value = 'Email không hợp lệ !'
                          : controller.errEmail.value = '';
                },
              ),
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                Expanded(
                    child: CustomComboBoxFetchData(
                  title: 'Tên cơ sở sản xuất, kinh doanh',
                  defaultSelectedItem: controller.cssxkdID.value.isNotEmpty
                      ? DropdownModel(
                          display: controller.tenCssxkd.value,
                          id: controller.cssxkdID.value)
                      : null,
                  weight: MediaQuery.of(context).size.width * 0.65,
                  dropDownList: controller.cssxkdList,
                  onChange: (selectedCssxkd) {
                    if (selectedCssxkd != null) {
                      controller
                          .filterByCoSoSXKDID(selectedCssxkd.id.toString());
                    }
                    controller.cssxkdID.value.isEmpty
                        ? controller.errCssxkd.value =
                            'Tên cở sở không được để trống !'
                        : controller.errCssxkd.value = '';
                  },
                  delete: () {
                    controller.nguoiDaiDien.value = '';
                    controller.diaChi.value = '';
                    controller.provinceID.value = '';
                    controller.districtID.value = '';
                    controller.communeID.value = '';
                    controller.villageID.value = '';
                    controller.cssxkdID.value = '';
                  },
                  errorText: controller.errCssxkd.value,
                  onChangeSearch: (keyword) {
                    controller.fetchByKeyword(keyword);
                  },
                  isLoading: controller.isLoading.value,
                  isEnabled: controller.checked.value,
                )),
                Padding(
                  padding: EdgeInsets.only(top: 0.9.h),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width * 0.25,
                    height: 45,
                    child: ButtonWidget(
                      ontap: () async {
                        final result = await Get.toNamed(
                            Routes.traCuuCSSXKDDuDKATTPBanDoSo);
                        if (result != null) {
                          controller.updateArguments(result);
                        }
                      },
                      text: "Chọn",
                      leadingIcon: const Icon(
                        Icons.location_on_sharp,
                        color: Colors.white,
                      ),
                      backgroundColor: !controller.isLoading.value
                          ? AppColors.submitButtonColor
                          : AppColors.borderColors,
                      borderRadius: 7,
                      enabled: controller.checked.value,
                    ),
                  ),
                )
              ]),
              TextFieldWidget(
                title: 'Người đại diện',
                isRequired: true,
                placeholder: 'Họ và tên người đại diện',
                setValue: controller.nguoiDaiDien.isNotEmpty
                    ? controller.nguoiDaiDien.value
                    : '',
                initialValue: controller.nguoiDaiDien.isNotEmpty
                    ? controller.nguoiDaiDien.value
                    : '',
                onChange: (e) {
                  controller.nguoiDaiDien.value = e;
                },
                isDisabled: true,
                showClearButton: false,
              ),
              TextFieldWidget(
                title: 'Địa chỉ CSSXKD',
                isRequired: true,
                placeholder: 'Địa chỉ',
                setValue:
                    controller.diaChi.isNotEmpty ? controller.diaChi.value : '',
                initialValue:
                    controller.diaChi.isNotEmpty ? controller.diaChi.value : '',
                onChange: (e) {
                  controller.diaChi.value = e;
                },
                isDisabled: true,
                showClearButton: false,
              ),
              TextFieldWidget(
                title: 'Tiêu đề',
                isRequired: true,
                placeholder: 'Nhập tiêu đề',
                setValue: controller.tieuDe.value,
                initialValue: controller.tieuDe.value,
                errorWidget: controller.errTitle.value,
                onChange: (e) {
                  controller.tieuDe.value = e;
                  controller.tieuDe.value.isEmpty
                      ? controller.errTitle.value =
                          'Tiêu đề không được để trống !'
                      : controller.errTitle.value = '';
                },
              ),
              TextAreaWidget(
                title: 'Nội dung',
                isRequired: true,
                placeholder: 'Nhập nội dung',
                initialValue: controller.noiDung.value,
                errorWidget: controller.errNoiDung.value,
                onChange: (e) {
                  controller.noiDung.value = e;
                  controller.noiDung.value.isEmpty
                      ? controller.errNoiDung.value =
                          'Nội dung không được để trống !'
                      : controller.errNoiDung.value = '';
                },
              ),
              const TextWidget(
                text:
                    'Lưu ý: Nội dung này sau khi cơ quan quản lý có thẩm quyền giải quyết sẽ được công khai hiến thị trên cổng thông tin.',
                color: AppColors.error,
                size: AppDimens.textSize13,
                fontStyle: FontStyle.italic,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      const TextWidget(text: 'Ẩn danh'),
                      Switch(
                          value: controller.anDanh.value,
                          onChanged: (e) {
                            controller.anDanh.value = e;
                          }),
                    ],
                  ),
                  GestureDetector(
                    onTap: () {
                      if (controller.isLoadingVideo.value) {
                        return;
                      }
                      if (controller.selectedFiles.length >= 3) {
                        return;
                      }
                      pickAssets(context, (files) {
                        controller.addFiles(files);
                      });
                    },
                    child: Row(
                      children: [
                        const Icon(Icons.add_photo_alternate_outlined,
                            color: Colors.blue),
                        Text(
                          'Đính kèm tệp (${controller.selectedFiles.length}/3)',
                          style: const TextStyle(
                              fontStyle: FontStyle.italic, color: Colors.blue),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(
                height: 10,
              ),
              Obx(() {
                if (controller.isLoadingVideo.value) {
                  return Column(
                    children: [
                      const CircularProgressIndicator(),
                      TextWidget(
                        text: 'Đang tải tệp tin của bạn...',
                      ),
                    ],
                  );
                }
                if (controller.selectedFiles.isEmpty) {
                  return const SizedBox(); // Không hiển thị gì nếu danh sách rỗng
                }
                return SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: controller.selectedFiles.map((file) {
                      final fileName = file.split('/').last; // Lấy tên file
                      final isImage = fileName.endsWith('.jpg') ||
                          fileName.endsWith('.png');
                      final isVideo = fileName.endsWith('.mp4') ||
                          fileName.endsWith('.MOV');
                      return Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 5.0),
                        child: Column(
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                border: Border.all(color: AppColors.primary),
                                borderRadius: BorderRadius.circular(5),
                              ),
                              child: isImage
                                  ? Image.file(
                                      File(file),
                                      width: 50,
                                      height: 50,
                                    )
                                  : isVideo
                                      ? Icon(
                                          Icons.videocam,
                                          size: 50,
                                          color: AppColors.primary,
                                        )
                                      : const SizedBox.shrink(),
                            ),

                            // Text(
                            //   fileName,
                            //   style: const TextStyle(fontSize: 12),
                            //   overflow: TextOverflow.ellipsis,
                            // ),
                            IconButton(
                              icon: const Icon(Icons.close,
                                  size: 16, color: Colors.red),
                              onPressed: () {
                                controller.removeFile(file);
                              },
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                );
              }),
              const SizedBox(height: 10),
              Obx(() => SizedBox(
                    height: 45,
                    child: ButtonWidget(
                      ontap: () {
                        if (controller.isLoadingVideo.value) {
                          Get.snackbar("Đợi tải video",
                              "Vui lòng chờ video được tải hoàn tất");
                          return;
                        }
                        if (controller.hoVaTen.value.isEmpty ||
                            !Validators.validPhone(
                                controller.soDienThoai.value) ||
                            !Validators.validateEmail(controller.email.value) ||
                            controller.cssxkdID.value.isEmpty ||
                            controller.diaChi.value.isEmpty ||
                            controller.tieuDe.value.isEmpty ||
                            controller.noiDung.value.isEmpty) {
                          controller.errName.value =
                              'Họ và tên không được để trống !';
                          controller.errPhoneNumber.value =
                              'Số điện thoại không được để trống !';
                          controller.errEmail.value =
                              'Email không được để trống !';
                          controller.errTitle.value =
                              'Tiêu đề không được để trống !';
                          controller.errNoiDung.value =
                              'Nội dung không được để trống !';
                          controller.errCssxkd.value =
                              'Tên cở sở không được để trống !';
                          return;
                        }
                        !controller.isLoading.value
                            ? controller.HandleSubmit(context)
                            : null;
                      },
                      text: !controller.isLoading.value
                          ? "Gửi phản ánh"
                          : "Đang gửi phản ánh...",
                      backgroundColor: !controller.isLoading.value
                          ? AppColors.submitButtonColor
                          : AppColors.borderColors,
                      borderRadius: 7,
                    ),
                  ))
            ],
          ),
        ),
      ),
    );
  }
}
