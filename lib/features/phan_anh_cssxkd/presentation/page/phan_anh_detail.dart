// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'dart:developer';

import 'package:attp_2024/core/configs/contents/app_content.dart';
import 'package:attp_2024/core/ui/snackbar/snackbar_until.dart';
import 'package:attp_2024/core/ui/widgets/webview/webview_page.dart';
import 'package:attp_2024/features/phan_anh_cssxkd/presentation/controller/phan_anh_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:attp_2024/core/ui/widgets/carouser_slider/media_carouser.dart';
import 'package:attp_2024/core/ui/widgets/load/loading.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/core/utils/convert_text.dart';
import 'package:attp_2024/features/phan_hoi_cssxkd/widgets/row_text.dart';

class PhanAnhDetail extends GetView<PhanAnhController> {
  final Map<String, dynamic> itemPhanAnhData;

  const PhanAnhDetail({
    super.key,
    required this.itemPhanAnhData,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppBarWidget(
        title: "Chi tiết phản ánh",
      ),
      body: Loading.LoadingFullScreen(
        isLoading: controller.isLoading,
        body: _buildBody(context),
      ),
      // ,
    );
  }

  Widget _buildBody(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          Column(
            children: [
              itemPhanAnhData["DinhKem_PA"] != null &&
                      itemPhanAnhData['DinhKem_PA'].toString().isNotEmpty
                  ? Container(
                      margin: const EdgeInsets.all(10),
                      child: Stack(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                                border: Border.all(
                                    color: AppColors.ConHieuLucTren6Thang),
                                borderRadius:
                                    const BorderRadius.all(Radius.circular(5))),
                            margin: const EdgeInsets.only(top: 15),
                            child: Container(
                                padding: const EdgeInsets.all(20),
                                child: MediaCarousel(
                                  mediaUrls:
                                      convertAttachItemStringToListWithoutSwungDash(
                                          "${controller.userAccessModel?.siteURL}",
                                          itemPhanAnhData['DinhKem_PA']
                                              .toString()),
                                  isNavigator: false,
                                  styleBottom: 1,
                                )),
                          ),
                          Container(
                            margin: const EdgeInsets.only(left: 10),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: Colors.green,
                              borderRadius: BorderRadius.circular(5),
                            ),
                            child: Text(
                              "Hình ảnh",
                              style: TextStyle(color: Colors.white),
                            ),
                          ),
                        ],
                      ),
                    )
                  : const SizedBox.shrink(),
              Container(
                margin: const EdgeInsets.all(10),
                child: Stack(
                  children: [
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                          border:
                              Border.all(color: AppColors.ConHieuLucTren6Thang),
                          borderRadius:
                              const BorderRadius.all(Radius.circular(5))),
                      margin: const EdgeInsets.only(top: 15),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Text(itemPhanAnhData.toString()),
                            const SizedBox(height: 10),
                            rowText(
                                "Mã số:",
                                itemPhanAnhData['MaPAATTP'] ??
                                    'Không có thông tin'),
                            rowText(
                                "Số điện thoại:",
                                itemPhanAnhData['SoDienThoai'] ??
                                    'Không có thông tin'),
                            rowText(
                                "Tên cơ sở sản xuất, kinh doanh:",
                                itemPhanAnhData['TenCoSoSXKD'] ??
                                    'Không có thông tin'),
                            rowText(
                                "Chủ cơ sở:",
                                itemPhanAnhData['NguoiDaiDien'] ??
                                    'Không có thông tin'),
                            rowText(
                                "Địa chỉ:",
                                itemPhanAnhData['DiaChi']
                                    .toString()), // Corrected Address format issue
                            rowText(
                                "Tiêu đề:",
                                itemPhanAnhData['TieuDe'] ??
                                    'Không có thông tin'),
                            rowText(
                                "Nội dung khiếu nại, tố cáo:",
                                itemPhanAnhData['NoiDung'] ??
                                    'Không có thông tin'),
                          ],
                        ),
                      ),
                    ),
                    Container(
                      margin: const EdgeInsets.only(left: 10),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.green,
                        borderRadius: BorderRadius.circular(5),
                      ),
                      child: const Text(
                        "Thông tin khiếu nại, tố cáo từ người dân",
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                margin: const EdgeInsets.all(10),
                child: Stack(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                          border:
                              Border.all(color: AppColors.ConHieuLucTren6Thang),
                          borderRadius:
                              const BorderRadius.all(Radius.circular(5))),
                      margin: const EdgeInsets.only(top: 15),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 10),
                            const Row(
                              spacing: 10,
                              children: [
                                TextWidget(
                                  text: 'Thông tin cơ sở sản xuất, kinh doanh',
                                  size: AppDimens.textSize14,
                                  color: AppColors.green1,
                                  fontWeight: FontWeight.w600,
                                ),
                                Expanded(
                                    child: Divider(
                                  thickness: 0.5,
                                  color: AppColors.green1,
                                )),
                              ],
                            ),
                            const SizedBox(height: 10),
                            rowText(
                                "Số GPKD:",
                                itemPhanAnhData['SoGPKD'] ??
                                    controller.defaultValue.value),
                            rowText(
                                "Cơ quan cấp:",
                                itemPhanAnhData['CoQuanCapGPKD'] ??
                                    controller.defaultValue.value),
                            rowText(
                                "Số điện thoại:",
                                itemPhanAnhData['SoDienThoai_CS'] ??
                                    controller.defaultValue.value),
                            rowText(
                                "Địa chỉ:",
                                itemPhanAnhData['DiaChiCS'] ??
                                    controller.defaultValue.value),
                            rowText(
                                "Người đại diện:",
                                itemPhanAnhData['NguoiDaiDien'] ??
                                    controller.defaultValue.value),
                            rowText(
                                "Ngày cấp:",
                                itemPhanAnhData['NgayCapGCN'] ??
                                    controller.defaultValue.value),
                            rowText(
                                "Ngày hết hiệu lực:",
                                itemPhanAnhData['NgayHetHanGCN'] ??
                                    controller.defaultValue.value),
                            const SizedBox(height: 10),
                            const Row(
                              spacing: 20,
                              children: [
                                TextWidget(
                                  text: 'Kết quả xử lý',
                                  size: AppDimens.textSize14,
                                  color: AppColors.green1,
                                  fontWeight: FontWeight.w600,
                                ),
                                Expanded(
                                    child: Divider(
                                  thickness: 0.5,
                                  color: AppColors.green1,
                                )),
                              ],
                            ),
                            const SizedBox(height: 10),
                            rowText(
                                "Người xử lý:",
                                itemPhanAnhData["TenNhanVien_XuLy_PH"] !=
                                            null &&
                                        itemPhanAnhData["TenNhanVien_XuLy_PH"]
                                            .toString()
                                            .isNotEmpty
                                    ? itemPhanAnhData["TenNhanVien_XuLy_PH"]
                                    : itemPhanAnhData["TenNhanVien_XuLy_PA"] ??
                                        controller.defaultValue.value),
                            rowText(
                                "Chức vụ:",
                                itemPhanAnhData["TenChucVu_XuLy_PH"] != null &&
                                        itemPhanAnhData["TenChucVu_XuLy_PH"]
                                            .toString()
                                            .isNotEmpty
                                    ? itemPhanAnhData["TenChucVu_XuLy_PH"]
                                    : itemPhanAnhData["TenChucVu_XuLy_PA"] ??
                                        controller.defaultValue.value),
                            rowText(
                                "Ngày xử lý:",
                                itemPhanAnhData["NgayXuLy_PH"] != null &&
                                        itemPhanAnhData["NgayXuLy_PH"]
                                            .toString()
                                            .isNotEmpty
                                    ? itemPhanAnhData["NgayXuLy_PH"]
                                    : itemPhanAnhData["NgayXuLy_PA"] ??
                                        controller.defaultValue.value),
                            const TextWidget(
                              text: "Nội dung vi phạm: ",
                              color: Colors.black,
                              size: AppDimens.textSize14,
                            ),
                            itemPhanAnhData["NoiDung_XuLy"]
                                    .toString()
                                    .isNotEmpty
                                ? Padding(
                                    padding: const EdgeInsets.only(left: 10),
                                    child: HtmlWidget(itemPhanAnhData[
                                                    "NoiDung_PH"] !=
                                                null &&
                                            itemPhanAnhData["NoiDung_PH"]
                                                .toString()
                                                .isNotEmpty
                                        ? itemPhanAnhData["NoiDung_PH"]
                                        : itemPhanAnhData["NoiDung_XuLy_PA"] ??
                                            '''<h4>${controller.defaultValue.value}</h4>'''),
                                  )
                                : const SizedBox.shrink(),
                            rowText(
                                "Hình thức xử lý:",
                                itemPhanAnhData["TenHinhThucXuLy"] ??
                                    controller.defaultValue.value),
                            Row(
                              children: [
                                const TextWidget(
                                  text: "Đính kèm: ",
                                  size: AppDimens.textSize15,
                                  fontWeight: FontWeight.w400,
                                  color: Colors.black,
                                ),
                                itemPhanAnhData["NgayXuLy_PH"] != null &&
                                        itemPhanAnhData["NgayXuLy_PH"]
                                            .toString()
                                            .isNotEmpty
                                    ? GestureDetector(
                                        onTap: () {
                                          log('${itemPhanAnhData["DinhKem_PH"]}',
                                              name: "akr");
                                          if (itemPhanAnhData["DinhKem_PH"] ==
                                                  null ||
                                              itemPhanAnhData["DinhKem_PH"]!
                                                  .isEmpty) {
                                            SnackbarUtil.showWarning(
                                                "Không có đính kèm !",
                                                alignment: 'top');
                                          } else {
                                            Get.to(() => WebViewPage(
                                                title: AppContent
                                                    .appTitleWebview,
                                                initialUrl:
                                                    convertAttachItemStringToListWithoutSwungDash(
                                                        '${controller.userAccessModel?.siteURL}',
                                                        itemPhanAnhData[
                                                            "DinhKem_PH"]!)[0]));
                                          }
                                        },
                                        child: const TextWidget(
                                          text: 'Xem đính kèm',
                                          color: AppColors.blue,
                                          fontStyle: FontStyle.italic,
                                        ),
                                      )
                                    : const TextWidget(
                                        text: "Không có đính kèm",
                                        size: AppDimens.textSize14,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.black,
                                      )
                              ],
                            )
                          ],
                        ),
                      ),
                    ),
                    Container(
                      margin: const EdgeInsets.only(left: 10),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.green,
                        borderRadius: BorderRadius.circular(5),
                      ),
                      child: const Text(
                        "Kết quả xử lý phản ánh",
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
