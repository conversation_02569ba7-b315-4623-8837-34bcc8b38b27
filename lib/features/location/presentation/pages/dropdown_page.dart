import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';
import '../controller/LocationController.dart';

class LocationDropdowns extends GetView<LocationController> {
  const LocationDropdowns({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          // Province dropdown
          Obx(() {
            return DropdownButton<Map<String, dynamic>>(
              value: controller.selectedProvince.value,
              items: controller.provinces.map((province) {
                return DropdownMenuItem<Map<String, dynamic>>(
                  value: province,
                  child: Text(province['display']),
                );
              }).toList(),
              onChanged: (province) {
                controller.onProvinceSelected(province!);
              },
            );
          }),

          // District dropdown
          Obx(() {
            return DropdownButton<Map<String, dynamic>>(
              value: controller.selectedDistrict.value,
              items: controller.districts.map((district) {
                return DropdownMenuItem<Map<String, dynamic>>(
                  value: district,
                  child: Text(district['display']),
                );
              }).toList(),
              onChanged: (district) {
                controller.onDistrictSelected(district!);
              },
            );
          }),

          // Ward dropdown
          Obx(() {
            return DropdownButton<Map<String, dynamic>>(
              value: controller.selectedWard.value,
              items: controller.wards.map((ward) {
                return DropdownMenuItem<Map<String, dynamic>>(
                  value: ward,
                  child: Text(ward['display']),
                );
              }).toList(),
              onChanged: (ward) {
                controller.onWardSelected(ward!);
              },
            );
          }),
        ],
      ),
    );
  }
}
