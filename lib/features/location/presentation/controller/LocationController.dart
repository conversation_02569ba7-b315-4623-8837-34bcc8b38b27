import 'package:get/get.dart';
import 'package:get_it/get_it.dart';
import '../../../../core/data/api/services/proc/proc_service.dart';

class LocationController extends GetxController {
  var isLoading = false.obs;
  var provinces = <dynamic>[].obs;
  var districts = <dynamic>[].obs;
  var wards = <dynamic>[].obs;

  // Selected items
  var selectedProvince = Rxn<Map<String, dynamic>>();
  var selectedDistrict = Rxn<Map<String, dynamic>>();
  var selectedWard = Rxn<Map<String, dynamic>>();

  final _procService = GetIt.I<ProcService>();

  // Fetch provinces
  Future<void> fetchAllProvinces() async {
    isLoading.value = true;
    final body = [];
    try {
      List<dynamic> response =
          await _procService.callProc("Proc_Mobile_GetAllTinh", body);
      provinces.assignAll(response);
    } catch (e) {
      Get.snackbar("Error", "Failed to fetch provinces: $e");
    } finally {
      isLoading.value = false;
    }
  }

  // Fetch districts based on selected province
  Future<void> fetchDistrictsByProvince(String provinceId) async {
    isLoading.value = true;
    final body = [
      {"name": "TinhID", "type": "guid", "value": provinceId}
    ];
    try {
      List<dynamic> response =
          await _procService.callProc("Proc_Mobile_GetAllHuyen", body);
      districts.assignAll(response);
    } catch (e) {
      Get.snackbar("Error", "Failed to fetch districts: $e");
    } finally {
      isLoading.value = false;
    }
  }

  // Fetch wards based on selected district
  Future<void> fetchWardsByDistrict(String districtId) async {
    isLoading.value = true;
    final body = [
      {"name": "HuyenID", "type": "guid", "value": districtId}
    ];
    try {
      List<dynamic> response =
          await _procService.callProc("Proc_Mobile_GetAllXa", body);
      wards.assignAll(response);
    } catch (e) {
      Get.snackbar("Error", "Failed to fetch wards: $e");
    } finally {
      isLoading.value = false;
    }
  }

  // Update selected province and fetch districts
  void onProvinceSelected(Map<String, dynamic> province) {
    selectedProvince.value = province;
    selectedDistrict.value = null; // Reset district when province changes
    selectedWard.value = null; // Reset ward when province changes
    fetchDistrictsByProvince(province['id']);
  }

  // Update selected district and fetch wards
  void onDistrictSelected(Map<String, dynamic> district) {
    selectedDistrict.value = district;
    selectedWard.value = null; // Reset ward when district changes
    fetchWardsByDistrict(district['id']);
  }

  // Update selected ward
  void onWardSelected(Map<String, dynamic> ward) {
    selectedWard.value = ward;
  }
}
