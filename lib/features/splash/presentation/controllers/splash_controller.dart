import 'dart:async';

import 'package:get/get.dart';
import 'package:attp_2024/core/routes/routes.dart';
import 'package:attp_2024/core/services/user_access_use_case.dart';
import 'package:attp_2024/core/services/user_use_case.dart';

class SplashController extends GetxController {
  RxDouble loadingProgress = 0.0.obs;
  bool keepLogin = false;
  bool hasLoggerAccount = false;
  @override
  void onInit() async {
    super.onInit();
    keepLogin = await UserUseCase.getKeepLogin();
    var loggerAccounts = await UserAccessUseCase.getUserAccess();
    if (loggerAccounts.isNotEmpty) {
      hasLoggerAccount = true;
    }
    simulateLoading();
  }

  void simulateLoading() {
    if (loadingProgress.value < 0.0) {
      loadingProgress.value = 0.0;
    }

    Timer.periodic(const Duration(milliseconds: 10), (timer) {
      if (loadingProgress.value >= 1.0) {
        timer.cancel();
        if (keepLogin) {
          Get.offNamed(Routes.main);
        } else {
          if (hasLoggerAccount) {
            Get.offNamed(Routes.usedAccount);
          } else {
            Get.offNamed(Routes.login);
          }
        }
      } else {
        loadingProgress.value = (loadingProgress.value + 0.1).clamp(0.0, 1.0);
      }
    });
  }
}
