import 'package:get/get.dart';
import 'package:attp_2024/core/data/prefs/prefs.dart';
import 'package:attp_2024/features/auth/TaiKhoan<PERSON>gu<PERSON>ung/presentation/controllers/used_accounts_controller.dart';

import '../../../../core/data/api/configs/dio_configs.dart';
import '../../../../core/data/database/device_data.dart';

class UsedAccountBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => Prefs(), fenix: true);
    Get.put(UsedAccountsController());
    Get.lazyPut(() => DeviceData(Get.find<DioService>()));
  }
}
