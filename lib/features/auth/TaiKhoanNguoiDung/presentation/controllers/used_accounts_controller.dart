import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/data/models/user_access_model.dart';
import 'package:attp_2024/core/routes/routes.dart';
import 'package:attp_2024/core/services/user_access_use_case.dart';

class UsedAccountsController extends GetxController {
  RxList<UserAccessModel> loggerAccounts = <UserAccessModel>[].obs;
  RxList<UserAccessModel> visibleAccounts = <UserAccessModel>[].obs;

  RxBool isLoading = false.obs;

  final int initialCount = 3;
  final int incrementCount = 5;

  @override
  void onInit() async {
    super.onInit();
    await initial();
  }

  Future<void> initial() async {
    isLoading.value = true;
    final accounts = await UserAccessUseCase.getUserAccess();
    if (accounts.isNotEmpty) {
      loggerAccounts.value = accounts.reversed.toList();
      visibleAccounts.value = loggerAccounts.take(initialCount).toList();
      isLoading.value = false;
    } else {
      Get.toNamed(Routes.login);
    }
  }

  Future<void> deleteLoggerAccount({required String uid}) async {
    await UserAccessUseCase.removeUserAccessById(uid);

    // Remove the account from both lists
    loggerAccounts.removeWhere((user) => user.userID == uid);
    visibleAccounts.removeWhere((user) => user.userID == uid);

    // Add more accounts to visibleAccounts if needed
    if (visibleAccounts.length < initialCount && loggerAccounts.isNotEmpty) {
      int remainingToShow = initialCount - visibleAccounts.length;
      visibleAccounts.addAll(
        loggerAccounts.take(remainingToShow).where(
              (user) => !visibleAccounts.contains(user),
            ),
      );
    }

    // Check if there are no accounts left and show the dialog
    if (loggerAccounts.isEmpty && visibleAccounts.isEmpty) {
      Get.dialog(
        AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          title: const Text('Không còn tài khoản'),
          content: const Text('Chuyển đến trang đăng nhập để tiếp tục.'),
          actions: [
            TextButton(
              onPressed: () {
                Get.back();
                Get.toNamed(Routes.login);
              },
              child: const Text('Đến trang đăng nhập'),
            ),
          ],
        ),
      );
    }
  }

  void loadMoreAccounts() {
    int nextCount = visibleAccounts.length + incrementCount;
    if (nextCount > loggerAccounts.length) {
      nextCount = loggerAccounts.length;
    }
    visibleAccounts.value = loggerAccounts.take(nextCount).toList();
  }

  bool get canLoadMore => visibleAccounts.length < loggerAccounts.length;
}
