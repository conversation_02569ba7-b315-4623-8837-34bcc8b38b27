import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/data/models/user_access_model.dart';
import 'package:attp_2024/core/routes/routes.dart';
import 'package:attp_2024/core/ui/widgets/load/loading.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/features/auth/TaiKhoanNguoiDung/presentation/controllers/used_accounts_controller.dart';

class UsedAccountsPage extends GetView<UsedAccountsController> {
  const UsedAccountsPage({super.key});
  @override
  Widget build(BuildContext context) {
    return Loading.LoadingFullScreen(
      isLoading: controller.isLoading,
      body: Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(AppImageString.ibgAuthPage),
              fit: BoxFit.cover,
            ),
          ),
          child: Center(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(10, 30, 10, 30),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  SizedBox(height: 8.h),
                  const _Header(),
                  SizedBox(height: 4.h),
                  Obx(() {
                    return Expanded(
                      child: _ListCardAuth(
                        // ignore: invalid_use_of_protected_member
                        loggerAccounts: controller.loggerAccounts.value,
                      ),
                    );
                  }),
                  const _LoginButton(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _ListCardAuth extends GetView<UsedAccountsController> {
  final List<UserAccessModel> loggerAccounts;
  const _ListCardAuth({required this.loggerAccounts});
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return SingleChildScrollView(
        child: Column(
          children: [
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: controller.visibleAccounts.length,
              itemBuilder: (context, i) {
                final account = controller.visibleAccounts[i];
                return _buildCard(loggerAccount: account);
              },
            ),
            if (controller.canLoadMore)
              TextButton(
                onPressed: controller.loadMoreAccounts,
                child: const Text("Xem thêm"),
              ),
          ],
        ),
      );
    });
  }

  Widget _buildCard({required UserAccessModel loggerAccount}) {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(5),
          color: AppColors.white,
          boxShadow: [
            BoxShadow(
                // ignore: deprecated_member_use
                color: AppColors.black.withOpacity(.2),
                blurRadius: 2,
                offset: const Offset(1, 2))
          ]),
      margin: const EdgeInsets.all(8.0),
      child: InkWell(
        onTap: () {
          Get.toNamed(Routes.loginHistory, arguments: loggerAccount);
        },
        child: Padding(
          padding: const EdgeInsets.all(10.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  const CircleAvatar(
                      radius: 20,
                      backgroundImage: AssetImage(AppImageString.avatar)),
                  SizedBox(width: 4.w),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SingleChildScrollView(
                        scrollDirection: Axis.vertical,
                        child: SizedBox(
                          child: Row(
                            children: [
                              TextWidget(
                                text: loggerAccount.tenDangNhap,
                                size: 16,
                                color: AppColors.greenHeavy,
                                fontWeight: FontWeight.w600,
                                wordLimit: 3,
                                maxLines: 1,
                              ),
                              const SizedBox(width: 10),
                              Container(
                                constraints: const BoxConstraints(maxWidth: 50),
                                child: TextWidget(
                                  text: " ${loggerAccount.year}",
                                  maxLines: 1,
                                  size: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(
                        height: .2.h,
                      ),
                      const Text(
                        "******",
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              Padding(
                padding: const EdgeInsets.only(left: 5),
                child: InkWell(
                  onTap: () async {
                    controller.deleteLoggerAccount(uid: loggerAccount.userID);
                  },
                  child: Icon(
                    CupertinoIcons.clear_circled_solid,
                    // ignore: deprecated_member_use
                    color: AppColors.accentColor.withOpacity(.6),
                    size: 20,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _Header extends StatelessWidget {
  const _Header();

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(
                  AppImageString.logoATTP,
                  width: 40,
                  height: 40,
                  fit: BoxFit.fill,
                ),
                Gap(2.w),
                Text(
                  'PHẦN MỀM QUẢN LÝ',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.w900,
                    color: const Color(0xFF6C6A6A),
                    fontSize: 17.sp,
                  ),
                ),
              ],
            ),
            Gap(3.w),
            Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  'CƠ SỞ SẢN XUẤT KINH DOANH',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.w900,
                    color: AppColors.primary,
                    fontSize: 17.sp,
                  ),
                ),
                Text(
                  'THỰC PHẨM NÔNG, LÂM, THUỶ SẢN',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.w900,
                    color: AppColors.primary,
                    fontSize: 17.sp,
                  ),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 8),
        const Text(
          'Tài khoản đã đăng nhập',
          style: TextStyle(
            fontSize: 17,
            fontWeight: FontWeight.w500,
            color: Color.fromRGBO(44, 44, 44, 1),
          ),
        ),
      ],
    );
  }
}

class _LoginButton extends StatelessWidget {
  const _LoginButton();
  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        height: 50,
        width: 80.w,
        child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
            ),
            onPressed: () {
              Get.toNamed(Routes.login);
            },
            child: const TextWidget(
              fontWeight: FontWeight.w500,
              color: AppColors.white,
              size: AppDimens.textSize18,
              text: 'Sử dụng tài khoản khác',
            )),
      ),
    );
  }
}
