import 'package:attp_2024/core/data/database/device_data.dart';
import 'package:get/get.dart';
import 'package:attp_2024/features/auth/LichSuDangNhap/presentation/controllers/login_history_controller.dart';

import '../../../../core/data/api/configs/dio_configs.dart';

class LoginHistoryBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => LoginHistoryController());
    Get.lazyPut(() => DeviceData(Get.find<DioService>()));
  }
}
