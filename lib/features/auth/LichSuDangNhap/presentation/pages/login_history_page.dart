import 'dart:io';

import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/snackbar/snackbar_until.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:local_auth/local_auth.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/ui/widgets/custom_textfield/widgets/custom_passwordField.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/features/auth/LichSuDangNhap/presentation/controllers/login_history_controller.dart';
import 'package:attp_2024/features/auth/LichSuDangNhap/presentation/widgets/custom_checkbox_widget.dart';

class LoginFromHistoryPage extends GetView<LoginHistoryController> {
  const LoginFromHistoryPage({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage(AppImageString.background),
                fit: BoxFit.cover,
              ),
            ),
            child: SingleChildScrollView(
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.all(30.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      const SizedBox(height: 40),
                      const _Header(),
                      const SizedBox(height: 10),
                      _LoginForm(
                        controller: controller,
                      ),
                      SizedBox(height: 1.h),
                      CustomCheckButton(
                        value: controller.isCheck,
                        onChanged: (newValue) {
                          controller.isCheck.value = newValue ?? false;
                        },
                        activeColor: AppColors.activeColors,
                        checkColor: AppColors.checkColors,
                        fillColor: Colors.grey[300]!,
                      ),
                      SizedBox(height: 1.h),
                      _LoginButton(controller),
                    ],
                  ),
                ),
              ),
            ),
          ),
          Positioned(
              left: 1.w,
              top: 4.h,
              child: IconButton(
                onPressed: () {
                  Get.back();
                },
                icon: const Icon(Icons.arrow_back_ios_new_rounded,
                    color: AppColors.greenHeavy),
              ))
        ],
      ),
    );
  }
}

class _Header extends GetView<LoginHistoryController> {
  const _Header();

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(height: 32),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Image.asset(
                      AppImageString.logoATTP,
                      width: 40,
                      height: 40,
                      fit: BoxFit.fill,
                    ),
                    Gap(2.w),
                    Text(
                      'PHẦN MỀM QUẢN LÝ',
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.w900,
                        color: const Color(0xFF6C6A6A),
                        fontSize: 17.sp,
                      ),
                    ),
                  ],
                ),
                Gap(3.w),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      'CƠ SỞ SẢN XUẤT KINH DOANH',
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.w900,
                        color: AppColors.primary,
                        fontSize: 17.sp,
                      ),
                    ),
                    Text(
                      'THỰC PHẨM NÔNG, LÂM, THUỶ SẢN',
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.w900,
                        color: AppColors.primary,
                        fontSize: 17.sp,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 25),
            Image.asset(
              AppImageString.avatar,
              width: 69,
              height: 69,
            ),
            const SizedBox(height: 10),
            Text(
              'Xin chào',
              style: Theme.of(context)
                  .textTheme
                  .headlineMedium
                  ?.copyWith(fontSize: 17.sp, fontWeight: FontWeight.w500),
            ),
            Gap(1.h),
            Text(
              controller.userAccessModel?.tenDangNhap ?? "Tên đăng nhập",
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.w700,
                    color: AppColors.primary,
                    fontSize: 17.sp,
                  ),
            ),
          ],
        ),
      ],
    );
  }
}

class _LoginForm extends StatelessWidget {
  final LoginHistoryController controller;
  const _LoginForm({required this.controller});
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Gap(10),
        SizedBox(
            width: double.infinity,
            child: Obx(() {
              return PasswordFieldWidget(
                title: 'Mật khẩu',
                placeholder: '',
                initialValue: '',
                errorWidget: controller.passwordError.value,
                prefixIcon: Icon(
                  CupertinoIcons.lock_circle_fill,
                  size: 20,
                  color: AppColors.primary,
                ),
                onChange: (item) {
                  controller.password.value = item;
                  if (controller.passwordError.value.isNotEmpty) {
                    controller.passwordError.value = "";
                  }
                },
              );
            })),
      ],
    );
  }
}

class _LoginButton extends StatelessWidget {
  final LoginHistoryController controller;
  final LocalAuthentication auth = LocalAuthentication();

  _LoginButton(this.controller);

  /// 🔥 **Xác thực vân tay hoặc Face ID**
  Future<void> _authenticateWithBiometrics() async {
    bool isAuthenticated = false;
    try {
      isAuthenticated = await auth.authenticate(
        localizedReason: Platform.isIOS
            ? 'Vui lòng xác thực bằng Face ID để đăng nhập'
            : 'Vui lòng xác thực bằng vân tay để đăng nhập',
        options: const AuthenticationOptions(
          sensitiveTransaction: false,
          biometricOnly: true,
          stickyAuth: true,
        ),
      );
    } catch (e) {
      _showAuthErrorDialog();
    }

    if (isAuthenticated) {
      await controller.login(
          passwordAuto: controller.userAccessModel?.password ?? "");
    }
  }

  /// 🔥 **Hiển thị thông báo lỗi xác thực**
  void _showAuthErrorDialog() {
    // Get.dialog(
    //   AlertDialog(
    //     shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
    //     title: Text(Platform.isIOS ? 'Lỗi Face ID' : 'Lỗi vân tay'),
    //     content: TextWidget(
    //       text: Platform.isIOS
    //           ? "Thiết bị không hỗ trợ Face ID hoặc chưa thiết lập!"
    //           : "Thiết bị không hỗ trợ vân tay hoặc chưa thiết lập!",
    //     ),
    //     actions: [
    //       TextButton(
    //         onPressed: () {
    //           Get.back();
    //         },
    //         child: const Text('Đăng nhập với mật khẩu'),
    //       ),
    //     ],
    //   ),
    // );
    SnackbarUtil.showError(
        Platform.isIOS
            ? 'Thiết bị không hỗ trợ Face ID hoặc chưa thiết lập!'
            : 'Thiết bị không hỗ trợ vân tay hoặc chưa thiết lập!',
        alignment: 'bottom');
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: InkWell(
            onTap: () async {
              await controller.login();
            },
            child: Container(
              height: 48,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(7),
                color: AppColors.primary,
              ),
              child: const Center(
                child: Text(
                  'Đăng nhập',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ),
        InkWell(
          onTap: () {
            if (controller.isFingerAuth.value &&
                controller.allowLoginWithFingerprint.value) {
              _authenticateWithBiometrics();
            } else {
              _showAuthErrorDialog();
            }
          },
          child: Container(
            margin: const EdgeInsets.only(left: 10),
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: const Color(0xffF3F3F3),
              borderRadius: BorderRadius.circular(7),
            ),
            child: getBiometricIcon(),
          ),
        ),
      ],
    );
  }
}

Widget getBiometricIcon() {
  if (Platform.isIOS) {
    return SvgPicture.asset(
      "assets/icons/faceid-svgrepo-com.svg",
      width: 10.w,
      height: 10.h,
      colorFilter: ColorFilter.mode(AppColors.primary, BlendMode.srcIn),
    );
  } else {
    return SvgPicture.asset(
      "assets/icons/fingerprint-svgrepo-com.svg",
      width: 10.w,
      height: 10.h,
      colorFilter: ColorFilter.mode(AppColors.primary, BlendMode.srcIn),
    );
  }
}
