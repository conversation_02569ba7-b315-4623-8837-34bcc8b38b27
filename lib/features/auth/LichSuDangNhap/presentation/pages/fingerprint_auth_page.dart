import 'package:flutter/material.dart';
import 'package:local_auth/local_auth.dart';

class FingerprintAuthPage extends StatefulWidget {
  const FingerprintAuthPage({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _FingerprintAuthPageState createState() => _FingerprintAuthPageState();
}

class _FingerprintAuthPageState extends State<FingerprintAuthPage> {
  final LocalAuthentication auth = LocalAuthentication();
  bool _isAuthenticated = false;

  // Kiểm tra thiết bị có hỗ trợ sinh trắc học không
  Future<bool> _checkBiometrics() async {
    try {
      return await auth.canCheckBiometrics;
    } catch (e) {
      print("Error checking biometrics: $e");
      return false;
    }
  }

  // Thực hiện xác thực vân tay
  Future<void> _authenticate() async {
    try {
      bool authenticated = await auth.authenticate(
        localizedReason: 'Please authenticate to access',
        options: const AuthenticationOptions(
          biometricOnly: true,
          useErrorDialogs: true,
          stickyAuth: true,
        ),
      );
      setState(() {
        _isAuthenticated = authenticated;
      });

      if (authenticated) {
        // Điều hướng đến màn hình chính nếu đăng nhập thành công
        Navigator.pushReplacementNamed(context, '/home');
      }
    } catch (e) {
      print("Error authenticating: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Đăng nhập bằng Vân tay'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Text(
              _isAuthenticated
                  ? 'Xác thực thành công!'
                  : 'Hãy xác thực bằng vân tay để đăng nhập',
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () async {
                bool canCheckBiometrics = await _checkBiometrics();
                if (canCheckBiometrics) {
                  _authenticate();
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content:
                          Text('Thiết bị không hỗ trợ xác thực sinh trắc học'),
                    ),
                  );
                }
              },
              child: const Text('Xác thực Vân tay'),
            ),
          ],
        ),
      ),
    );
  }
}
