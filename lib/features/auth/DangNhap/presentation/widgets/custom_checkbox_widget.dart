import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CustomCheckButton extends StatelessWidget {
  final RxBool value;
  final ValueChanged<bool?> onChanged;
  final Color activeColor; // Màu khi checkbox được chọn
  final Color checkColor; // <PERSON>àu của dấu kiểm
  final Color fillColor; // Màu của ô trước khi chọn

  const CustomCheckButton({
    super.key,
    required this.value,
    required this.onChanged,
    this.activeColor = Colors.blue, // Màu mặc định khi được chọn
    this.checkColor = Colors.white, // Màu mặc định của dấu kiểm
    this.fillColor = Colors.grey, // Màu mặc định của ô trước khi chọn
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Obx(() => Checkbox(
              value: value.value,
              onChanged: onChanged,
              activeColor: activeColor, // <PERSON><PERSON><PERSON> khi checkbox được chọn
              checkColor: checkColor, // <PERSON><PERSON>u của dấu kiểm
              fillColor: WidgetStateProperty.resolveWith((states) {
                if (states.contains(WidgetState.selected)) {
                  return activeColor; // Màu khi checkbox được chọn
                }
                return fillColor; // Màu khi checkbox chưa được chọn
              }),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4), // Thêm góc bo tròn
              ),
              side: BorderSide.none, // Loại bỏ viền
            )),
        const Text('Ghi nhớ'),
      ],
    );
  }
}
