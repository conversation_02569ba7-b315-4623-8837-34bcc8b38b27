import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';

class BuildFooter extends StatelessWidget {
  const BuildFooter({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const Text(
          '<PERSON>ên hệ với chúng tôi ',
          style: TextStyle(color: Colors.black54),
        ),
        SizedBox(height: 1.h),
        Wrap(
          spacing: 10.w,
          children: [
            Image.asset(
              AppImageString.iIconContact1,
              fit: BoxFit.cover,
              width: 40, // Chiều rộng
              height: 40, // Chiều cao
            ),
            Image.asset(
              AppImageString.iIconContact2,
              fit: BoxFit.cover,
              width: 40, // Chiều rộng
              height: 40, // Chiều cao
            ),
            Image.asset(
              AppImageString.iIconContact3,
              fit: BoxFit.cover,
              width: 40, // Chiều rộng
              height: 40, // <PERSON><PERSON>u cao
            ),
          ],
        ),
      ],
    );
  }
}
