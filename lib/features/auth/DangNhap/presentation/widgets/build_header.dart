import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class BuildHeader extends StatelessWidget {
  const BuildHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              'PHẦN MỀM QUẢN LÝ',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.w900,
                    color: const Color(0xFF6C6A6A),
                    fontSize: 17.sp,
                  ),
            ),
            Text(
              'CƠ SỞ SẢN XUẤT KINH DOANH',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.w900,
                    color: AppColors.primary,
                    //color: const Color(0xFF6C6A6A),
                    fontSize: 17.sp,
                  ),
            ),
            Text(
              'THỰC PHẨM NÔNG, LÂM, THUỶ SẢN',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.w900,
                color: AppColors.primary,
                //color: const Color(0xFF6C6A6A),
                fontSize: 17.sp,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
