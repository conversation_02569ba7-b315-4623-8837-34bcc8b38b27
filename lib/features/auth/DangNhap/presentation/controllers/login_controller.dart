import 'dart:developer';

import 'package:attp_2024/core/data/api/configs/dio_configs.dart';
import 'package:attp_2024/core/data/api/services/proc/proc_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/configs/enum.dart';
import 'package:attp_2024/core/data/api/services/auth/auth_service.dart';
import 'package:attp_2024/core/data/database/device_data.dart';
import 'package:attp_2024/core/data/dto/request/login_request_model.dart';
import 'package:attp_2024/core/data/dto/request/proc_request_model.dart';
import 'package:attp_2024/core/data/dto/response/device_response.dart';
import 'package:attp_2024/core/data/dto/response/device_response_model.dart';
import 'package:attp_2024/core/data/models/auth/auth_model.dart';
import 'package:attp_2024/core/data/models/database/database_model.dart';
import 'package:attp_2024/core/data/models/user_access_model.dart';
import 'package:attp_2024/core/routes/routes.dart';
import 'package:attp_2024/core/services/user_access_use_case.dart';
import 'package:attp_2024/core/services/user_use_case.dart';
import 'package:attp_2024/core/ui/snackbar/snackbar_until.dart';
import 'package:attp_2024/core/utils/info_device.dart';
import '../../../../../core/services/auth_use_case/auth_use_case.dart';

class LoginController extends GetxController {
  final ProcService _procService = Get.find<ProcService>();
  final AuthService _authService = Get.find<AuthService>();
  final DioService _dioService = Get.find<DioService>();
  var data = {}.obs;
  var isLoading = false.obs;
  var authModel = AuthModel().obs;
  var userName = "";
  var password = "";
  var isCheck = false.obs;
  RxList<DatabaseModel> listDataBase = <DatabaseModel>[].obs;
  var databaseSelected = Rx<DatabaseModel?>(null);
  final deviceSerice = Get.find<DeviceData>();
  DeviceResponse? infoDevice;
  var userNameError = "".obs;
  var passwordError = "".obs;
  var databaseName = dotenv.env['DATABASE_NAME'] ?? "ATTP_mobile";

  @override
  onInit() async {
    super.onInit();
    var response = await _authService.fetchDatabase(databaseName);
    infoDevice = await InfoDevice.getDeviceData();
    if (response.status == Status.success) {
      log(response.data.toString(), name: "databaseInfo");
      listDataBase.value = response.data ?? [];
      databaseSelected.value = response.data?[0];
      if (databaseSelected.value?.siteURL.isNotEmpty ?? false) {
        _dioService.updateBaseUrl(databaseSelected.value!.siteURL);
        log(databaseSelected.value!.siteURL, name: "Change Base to");
      }
    }
  }

  Future<void> initDevice({required String userId}) async {
    deviceSerice.addDevice(
        request: ProcRequestModel(fields: [
      FieldModel(
          name: "ThietBiCode",
          type: "String",
          value: infoDevice?.thietBiCode ?? ""),
      FieldModel(
          name: "TenThietBi",
          type: "String",
          value: infoDevice?.tenThietBi ?? ""),
      FieldModel(
          name: "HangSX", type: "String", value: infoDevice?.hangSX ?? ""),
      FieldModel(
          name: "Platform", type: "String", value: infoDevice?.platform ?? ""),
      FieldModel(
          name: "Version", type: "String", value: infoDevice?.version ?? ""),
      FieldModel(
          name: "LoaiThietBi",
          type: "String",
          value: infoDevice?.loaiThietBi ?? ""),
      FieldModel(name: "UserID", type: "guid", value: userId),
      FieldModel(name: "NgungSD", type: "bool", value: false),
    ]));
  }

  Future<void> login(BuildContext context) async {
    isLoading.value = true;
    try {
      if (userName.trim().isEmpty) {
        userNameError.value = "Không được để trống";
        return;
      }

      if (password.trim().isEmpty) {
        passwordError.value = "Không được để trống";
        return;
      }

      if (databaseSelected.value?.id.isNotEmpty ?? false) {
        final response = await _authService.login(
          loginRequestModel: LoginRequestModel(
            userName: userName.trim(),
            password: password.trim(),
            idDatabase: databaseSelected.value?.id ?? "",
          ),
        );

        if (response.status == Status.success) {
          response.data?.toJson().forEach((key, value) {
            log("$key: $value", name: "infoData:");
          });
          String hoTen = "";
          String TenDonVi = "";
          String anhDaiDien = "";
          final List<Map<String, dynamic>> body = [
            {
              "name": "UserID",
              "type": "Guid",
              "value": response.data?.userId ?? ""
            },
          ];
          try {
            final userInfoResponse = await _procService.callProc(
                "Proc_Mobile_GetUserInfo_byDB", body);
            if (userInfoResponse.isNotEmpty) {
              hoTen = userInfoResponse[0]['HoTen'] ?? '';
              TenDonVi = userInfoResponse[0]['TenDonVi'] ?? '';
              anhDaiDien = userInfoResponse[0]['HinhDaiDien'] ?? '';
            }
          } catch (e) {
            print("Error fetching user info: $e");
          }

          UserAccessModel userSave = UserAccessModel(
            databaseId: databaseSelected.value?.id ?? "",
            tenSite: databaseSelected.value?.tenSite ?? "",
            siteURL: databaseSelected.value?.siteURL ?? "",
            tenTinh: databaseSelected.value?.tenTinh ?? "",
            huyenID: databaseSelected.value?.huyenID ?? "",
            huyenCode: databaseSelected.value?.huyenCode ?? "",
            dbDataName: databaseSelected.value?.dbDataName ?? "",
            dbDUserName: databaseSelected.value?.dbDUserName ?? "",
            checkLicense: databaseSelected.value?.checkLicense ?? false,
            userName: userName,
            password: password,
            year: "2024",
            apiUrl: databaseSelected.value?.apiUrl ?? "",
            apiUpFile: databaseSelected.value?.apiUpFile ?? "",
            apiDeleteFile: databaseSelected.value?.apiDeleteFile ?? "",
            userID: response.data?.userId ?? "",
            tenDangNhap: response.data?.tenDangNhap ?? "",
            donViID: response.data?.donViId ?? "",
            userGroupCode: response.data?.userGroupCode ?? "",
            token: response.data?.token ?? "",
            hoTen: hoTen,
            TenDonVi: TenDonVi,
            anhDaiDien: anhDaiDien,
          );

          if (userSave.token.isNotEmpty) {
            await AuthUseCase.setToken(token: userSave.token);
            UserAccessUseCase.setUserAccess(userAccessModel: userSave);
            UserUseCase.setUser(userAccessModel: userSave);
            final deviceData = Get.find<DeviceData>();
            var result = await deviceData.getListDevice(
                request: ProcRequestModel(fields: [
              FieldModel(
                  name: "UserID",
                  type: "guid",
                  value: response.data?.userId ?? "")
            ]));

            if (result.status == Status.success) {
              List<DeviceResponseModel> data = result.data ?? [];
              bool allowAdd = false;
              if (data.isNotEmpty) {
                allowAdd = !data.any((device) =>
                    device.thietBiCode == infoDevice?.thietBiCode &&
                    device.userID == response.data?.userId);
              } else {
                allowAdd = true;
              }
              if (allowAdd) {
                await initDevice(userId: response.data?.userId ?? "");
              }
            }
            if (isCheck.value) {
              UserUseCase.setKeepLogin();
            }
            Get.offAllNamed(Routes.main);
          } else {
            // ignore: use_build_context_synchronously
            SnackbarUtil.showError("Lỗi thông tin đăng nhập không chính xác!",
                alignment: "bottom");
          }
        } else {
          // ignore: use_build_context_synchronously
          SnackbarUtil.showError("Lỗi thông tin đăng nhập không chính xác!",
              alignment: "bottom");
          return;
        }
      } else {
        SnackbarUtil.showError("Lỗi thông tin đăng nhập không chính xác!",
            alignment: "bottom");
      }
    } catch (e) {
      print("Error logging in: $e");
    } finally {
      isLoading.value = false;
    }
  }

  void changeColor() {
    update(["buildId"]);
  }
}
