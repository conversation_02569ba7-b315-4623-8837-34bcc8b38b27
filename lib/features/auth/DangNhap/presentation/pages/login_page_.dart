import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/features/auth/DangNhap/presentation/widgets/custom_button.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/load/loading.dart';
import 'package:attp_2024/features/auth/DangNhap/presentation/controllers/login_controller.dart';
import 'package:attp_2024/features/auth/DangNhap/presentation/widgets/build_header.dart';
import 'package:attp_2024/features/auth/DangNhap/presentation/widgets/custom_checkbox_widget.dart';
import 'package:attp_2024/features/auth/DangNhap/presentation/widgets/login_form.dart';

class LoginPage extends GetView<LoginController> {
  const LoginPage({super.key});
  @override
  Widget build(BuildContext context) {
    return Loading.LoadingFullScreen(
      isLoading: controller.isLoading,
      body: _BuildBody(context),
    );
  }

  // ignore: non_constant_identifier_names
  Widget _BuildBody(BuildContext context) {
    final isKeyboardVisible = MediaQuery.of(context).viewInsets.bottom > 0;
    return Stack(
      children: [
        SvgPicture.asset(
          AppImageString.AppBgSvg,
          width: double.infinity,
          height: double.infinity,
          fit: BoxFit.fill,
        ),
        if (!isKeyboardVisible)
          Padding(
            padding: EdgeInsets.only(top: 16.h),
            child: Align(
              alignment:
                  Alignment.topCenter,
              child: Image.asset(
                AppImageString.logoATTP,
                width: 100,
                height: 100,
                fit: BoxFit.cover,
              ),
            ),
          ),
        if (!isKeyboardVisible)
          Padding(
            padding: EdgeInsets.only(top: 12.w, left: 5.w),
            child: Align(
              alignment: Alignment.topLeft,
              child: Column(
                children: [
                  Row(
                    children: [
                      Text(
                        'Đăng nhập',
                        textAlign: TextAlign.start,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.white,
                              fontSize: 19.sp,
                            ),
                      ),
                      Gap(1.w),
                      const Icon(
                        CupertinoIcons.person,
                        color: AppColors.white,
                      )
                    ],
                  ),
                  Row(
                    children: [
                      Text(
                        'Vui lòng đăng nhập để sử dụng ứng dụng',
                        textAlign: TextAlign.start,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.white,
                              fontSize: 15.sp,
                            ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        Center(
          child: SingleChildScrollView(
            scrollDirection: Axis.vertical,
            child: Padding(
              padding: EdgeInsets.only(left: 9.w, right: 9.w),
              child: Column(
                children: [
                  const BuildHeader(),
                  const LoginForm(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      CustomCheckButton(
                        value: controller.isCheck,
                        onChanged: (newValue) {
                          controller.isCheck.value = newValue ?? false;
                        },
                        activeColor: AppColors.primary,
                        checkColor: AppColors.checkColors,
                        fillColor: Colors.grey[300]!,
                      ),
                    ],
                  ),
                  // _buildButton(context),
                  CustomButton(controller: controller),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
