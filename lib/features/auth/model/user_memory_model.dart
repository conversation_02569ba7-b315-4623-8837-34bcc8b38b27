class UserMemoryModel {
  final String userName;
  final String year;

  UserMemoryModel({required this.userName, required this.year});

  Map<String, dynamic> toJson() {
    return {
      'userName': userName,
      'year': year,
    };
  }

  factory UserMemoryModel.fromJson(Map<String, dynamic> json) {
    return UserMemoryModel(
      userName: json['userName'],
      year: json['year'],
    );
  }
}
