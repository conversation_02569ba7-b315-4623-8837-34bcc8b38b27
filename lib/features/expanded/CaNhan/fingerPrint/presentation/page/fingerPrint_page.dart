import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/data/dto/response/device_response_model.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:attp_2024/core/ui/widgets/customSwitch/custom_switch.dart';
import 'package:attp_2024/core/ui/widgets/load/loading.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/features/expanded/CaNhan/fingerPrint/presentation/controller/fingerPrint_controller.dart';

class FingerprintPage extends GetView<FingerprintController> {
  const FingerprintPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Loading.LoadingFullScreen(
      isLoading: controller.isLoading,
      body: Scaffold(
        appBar: const AppBarWidget(
          centerTitle: true,
          // title: controller.supportedBiometric.value == "Face ID"
          //     ? "Cài đặt Face ID"
          //     : "Cài đặt Vân tay",
          title: "Cài đặt vân tay/Face ID",
        ),
        body: Stack(
          children: [
            Container(
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage("assets/images/background_profile.png"),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(top: 1.h),
              child: GetBuilder<FingerprintController>(
                  id: "bodyID", builder: (controller) => _buildBody(context)),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _fingerprintBuild(),
          Gap(2.h),
          Padding(
            padding: EdgeInsets.only(left: 3.w, right: 3.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const TextWidget(
                  text: 'Danh sách thiết bị',
                  fontWeight: FontWeight.w700,
                ),
                const Gap(10),
                Stack(
                  children: [
                    Container(
                      constraints:
                          BoxConstraints(maxHeight: 58.h, minHeight: 55.h),
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: controller.listDevice.length,
                        itemBuilder: (context, index) {
                          final device = controller.listDevice[index];
                          return _themeModeSetting(
                            leadingIconUrl: AppImageString.iconPhone,
                            title: device.tenThietBi,
                            device: '${device.platform} ${device.version}',
                            deviceResponse: device,
                            myPhone: device.thietBiCode ==
                                (controller.infoDevice?.thietBiCode ?? ""),
                          );
                        },
                      ),
                    ),
                    Obx(
                      () => !controller.switchActive.value
                          ? Positioned(
                              left: 0,
                              top: 0,
                              right: 0,
                              bottom: 0,
                              child: Container(
                                decoration: BoxDecoration(
                                  color: AppColors.gray1.withOpacity(0),
                                ),
                              ),
                            )
                          : const Positioned(child: SizedBox()),
                    )
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _themeModeSetting(
      {required String leadingIconUrl,
      required String title,
      required String device,
      required DeviceResponseModel deviceResponse,
      bool? myPhone}) {
    RxBool activeButton = (!deviceResponse.ngungSD).obs;
    return Obx(() {
      Color green =
          controller.switchActive.value ? AppColors.primary : AppColors.gray2;
      Color text = controller.switchActive.value
          ? AppColors.titleFingerprint
          : AppColors.gray2;
      Color switchColor = controller.switchActive.value
          ? AppColors.customWitchColors
          : AppColors.gray2;
      return Container(
        margin: EdgeInsets.only(bottom: 1.h),
        decoration: BoxDecoration(
          color: AppColors.containerColors,
          borderRadius: BorderRadius.circular(5),
          border: Border.all(
            color: !(myPhone ?? false)
                ? AppColors.borderInput1.withOpacity(.2)
                : green,
            width: !(myPhone ?? false) ? .5 : 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Image.asset(
                    leadingIconUrl,
                    width: 35,
                    height: 35,
                  ),
                  const SizedBox(
                    width: 15,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            constraints: BoxConstraints(
                              maxWidth: 35.w,
                            ),
                            child: TextWidget(
                              text: title,
                              maxLines: 1,
                              color: text,
                              fontWeight:
                                  (myPhone ?? false) ? FontWeight.w700 : null,
                            ),
                          ),
                          SizedBox(
                            width: 2.w,
                          ),
                          TextWidget(
                            text:
                                (myPhone ?? false) ? "(Thiết bị của tôi)" : "",
                            color: text,
                            size: AppDimens.textSize12,
                          )
                        ],
                      ),
                      const SizedBox(
                        height: 3,
                      ),
                      TextWidget(
                        text: device,
                        size: 13,
                        color: AppColors.deviceFingerprint,
                      ),
                    ],
                  )
                ],
              ),
              SwitchCustom(
                onChange: (value) async {
                  activeButton.value = value;
                  await controller.updateStateDevice(
                      currentSate: value,
                      userId: deviceResponse.userID,
                      thietBiCode: deviceResponse.thietBiCode);
                },
                activeTrackColor: switchColor,
                inactiveTrackColor: AppColors.inactiveTrackColor,
                activeThumbColor: AppColors.activeThumbColor,
                inactiveThumbColor: AppColors.inactiveThumbColor,
                switchWidth: 49,
                switchHeight: 24,
                thumbSize: 20,
                borderColor: Colors.transparent,
                isActive: activeButton, // Bỏ viền bằng cách đặt màu trong suốt
              )
            ],
          ),
        ),
      );
    });
  }

  Container _fingerprintBuild() {
    return Container(
      decoration: const BoxDecoration(
        color: AppColors.fingerprintContainer,
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Image.asset(
                  AppImageString.iconProfile3,
                  width: 25,
                  height: 25,
                ),
                const SizedBox(
                  width: 15,
                ),
                const TextWidget(
                  text: 'Đăng nhập bằng vân tay/Face ID',
                ),
              ],
            ),
            SwitchCustom(
              onChange: (value) async {
                await controller.handleChangeSwich(currentState: value);
                controller.switchActive.value = value;
              },
              activeTrackColor: AppColors.customWitchColors,
              inactiveTrackColor: AppColors.inactiveTrackColor,
              activeThumbColor: AppColors.activeThumbColor,
              inactiveThumbColor: AppColors.inactiveThumbColor,
              switchWidth: 49,
              switchHeight: 24,
              thumbSize: 20,
              borderColor: Colors.transparent,
              isActive: controller.switchActive,
            )
          ],
        ),
      ),
    );
  }
}
