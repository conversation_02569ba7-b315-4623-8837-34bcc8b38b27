import 'package:get/get.dart';
import 'package:attp_2024/features/expanded/Ca<PERSON><PERSON>/changePassword/presentation/controller/changePassword_controller.dart';
import 'package:attp_2024/features/expanded/Ca<PERSON>han/customInterface/presentation/controller/customInterface_controller.dart';
import 'package:attp_2024/features/expanded/CaNhan/editProfile/presentation/controller/editProfile_controller.dart';
import 'package:attp_2024/features/expanded/CaNhan/fingerPrint/presentation/controller/fingerPrint_controller.dart';
import 'package:attp_2024/features/nav/profile/presentation/controllers/profile_controller.dart';

class FingerprintBinding extends Bindings {
  @override
  void dependencies() {
    //cách 1 : khởi tạo trong lần đầu được gọi
    // sử dụng tối ưu hiệu năng không pải load api nhiều lần
    Get.lazyPut(() => FingerprintController());

    // cách 2: tạo đối tượng ngay lập tưc bắt kể là dùng hay hông dùng
    // dùng trong trường hợp cần sử dụng ngay controller
    // Get.put(ProfileController());
  }
}
