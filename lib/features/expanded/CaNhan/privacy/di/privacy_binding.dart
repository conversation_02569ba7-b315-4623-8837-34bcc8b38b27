import 'package:get/get.dart';
import 'package:attp_2024/features/expanded/CaNhan/privacy/presentation/controller/privacy_controller.dart';

class PrivacyBinding extends Bindings {
  @override
  void dependencies() {
    //cách 1 : khởi tạo trong lần đầu đư<PERSON> g<PERSON>
    // sử dụng tối ưu hiệu năng không pải load api nhiều lần
    Get.lazyPut(() => PrivacyController());

    // cách 2: tạo đối tượng ngay lập tưc bắt kể là dùng hay hông dùng
    // dùng trong trường hợp cần sử dụng ngay controller
    // Get.put(ProfileController());
  }
}
