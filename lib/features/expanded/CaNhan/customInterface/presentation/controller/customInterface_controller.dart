import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/configs/theme/app_theme.dart';
import 'package:attp_2024/core/configs/theme/app_theme_manager.dart';
import 'package:attp_2024/core/data/models/theme_model.dart';
import 'package:attp_2024/core/services/theme_use_case.dart';

class CustomInterfaceController extends GetxController {
  AppThemeManager appThemeModel = AppThemeManager.initialize();

  // Make themeModel reactive using Rx<ThemeModel?>
  var themeModel = Rx<ThemeModel?>(null);

  @override
  void onInit() async {
    super.onInit();
    await fetchThemes();
  }

  Future<void> fetchThemes() async {
    try {
      themeModel.value = await ThemeUseCase.getThemes();
      update(["bodyID"]);
    } catch (e) {
      print("Error fetching themes: $e");
    }
  }

  Future<void> changeTheme({required ThemeModel newTheme}) async {
    try {
      await ThemeUseCase.setThemes(themes: newTheme);
      themeModel.value = await ThemeUseCase.getThemes();
      AppColors.setTheme(themeModel.value ?? appThemeModel.defaultTheme);
      Fluttertoast.showToast(msg: 'Thoát vào lại để cập nhật theme');
      update(["bodyID"]);
    } catch (e) {
      print("Error updating theme: $e");
    }
  }

  void toggleTheme(bool isDarkMode) {
    Get.changeTheme(isDarkMode ? AppTheme.dark : AppTheme.light);
  }

  Future<void> applyTheme(ThemeModel newTheme) async {
    try {
      await changeTheme(newTheme: newTheme);
    } catch (e) {
      Get.snackbar('Error', 'Failed to apply theme');
    }
  }
}
