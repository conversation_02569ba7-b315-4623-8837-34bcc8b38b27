import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/data/models/theme_model.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/features/expanded/CaNhan/customInterface/presentation/controller/customInterface_controller.dart';

class CustomInterfacePage extends GetView<CustomInterfaceController> {
  const CustomInterfacePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppBarWidget(
        title: "Tùy chỉnh giao diện",
      ),
      body: GetBuilder<CustomInterfaceController>(
        id: "bodyID",
        builder: (_) => Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(
                    Theme.of(context).brightness == Brightness.dark
                        ? "assets/images/bg_dark.jpg"
                        : "assets/images/background_profile.png",
                  ),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            _buildBody(context),
          ],
        ),
      ),
    );
  }

  Widget _buildBody(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.only(left: 3.w, right: 3.w, top: 4.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            for (var i = 0;
                i < controller.appThemeModel.customThemes.length;
                i++)
              _themeModeSetting(
                title: i == 0 ? 'Màu mặc định' : "Chủ đề $i",
                themeModel: controller.appThemeModel.customThemes[i],
                onChange: () {
                  controller.changeTheme(
                      newTheme: controller.appThemeModel.customThemes[i]);
                },
              ),
            SizedBox(height: 1.5.h),
          ],
        ),
      ),
    );
  }

  Container _themeModeSetting({
    String? leadingIconUrl,
    required ThemeModel themeModel,
    required Function onChange,
    required String title,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 2.h),
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(5),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withOpacity(.08),
            blurRadius: 2,
            offset: const Offset(1, 1.5),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              if (leadingIconUrl != null)
                Container(
                  margin: EdgeInsets.only(right: 3.w),
                  child: Image.asset(
                    leadingIconUrl,
                    width: 7.w,
                    height: 7.w,
                  ),
                ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextWidget(
                    text: title,
                    size: 16,
                    fontWeight: FontWeight.w700,
                  ),
                  SizedBox(height: 1.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _colorBox(themeModel.primaryColor),
                      _colorBox(themeModel.accentColor),
                      _colorBox(themeModel.backgroundColor),
                      _colorBox(themeModel.textColor),
                      _colorBox(themeModel.buttonColor),
                      _colorBox(themeModel.inputFieldColor),
                    ],
                  ),
                ],
              ),
            ],
          ),
          InkWell(
            onTap: () async {
              await onChange();
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: .8.h),
              decoration: BoxDecoration(
                color: controller.themeModel.value?.primaryColor.value ==
                        themeModel.primaryColor.value
                    ? themeModel.primaryColor
                    : AppColors.white,
                borderRadius: BorderRadius.circular(5),
                border: Border.all(
                    width: .5,
                    color: controller.themeModel.value?.primaryColor.value ==
                            themeModel.primaryColor.value
                        ? AppColors.white
                        : AppColors.greenHeavy),
              ),
              child: TextWidget(
                text: "Áp dụng",
                size: 12,
                color: controller.themeModel.value?.primaryColor.value ==
                        themeModel.primaryColor.value
                    ? AppColors.white
                    : AppColors.black,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _colorBox(Color color) {
    return Container(
      width: 7.w,
      height: 7.w,
      margin: EdgeInsets.only(right: 2.w),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(10000),
        boxShadow: [
          BoxShadow(color: AppColors.black.withOpacity(.1), blurRadius: 1)
        ],
      ),
    );
  }
}
