import 'package:get/get.dart';
import 'package:attp_2024/features/expanded/Ca<PERSON>han/editProfile/presentation/controller/editProfile_controller.dart';

import '../../../../../core/data/api/configs/dio_configs.dart';
import '../../../../../core/data/api/services/proc/proc_service.dart';

class EditProfileBinding extends Bindings {
  @override
  void dependencies() {
    //cách 1 : khởi tạo trong lần đầu được gọi
    // sử dụng tối ưu hiệu năng không pải load api nhiều lần
    Get.lazyPut(() => EditProfileController());
    Get.lazyPut(() => ProcService(Get.find<DioService>()));
    // cách 2: tạo đối tượng ngay lập tưc bắt kể là dùng hay hông dùng
    // dùng trong trường hợp cần sử dụng ngay controller
    // Get.put(ProfileController());
  }
}
