import 'dart:developer';
import 'dart:typed_data';
import 'package:attp_2024/core/data/models/user_access_model.dart';
import 'package:attp_2024/core/ui/snackbar/snackbar_until.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:attp_2024/core/data/api/services/proc/proc_service.dart';
import 'package:attp_2024/core/data/dto/response/profile_user_model.dart';
import 'package:attp_2024/core/services/uploads/upload_service.dart';
import 'package:attp_2024/core/services/user_use_case.dart';
import 'package:attp_2024/core/ui/widgets/custom_combo/combo.dart';

class EditProfileController extends GetxController {
  RxString avatarPathTemp = ''.obs;
  RxString avatarPath = ''.obs;
  RxString avatarPathOld = ''.obs;
  Rx<XFile?> avatarImage = Rx<XFile?>(null);
  RxString name = ''.obs;
  RxString phone = ''.obs;
  RxString email = ''.obs;
  Rx<DateTime> birthday = DateTime(1970, 01, 01).obs;
  RxString location = ''.obs;
  final userID = ''.obs;
  var defGender = ''.obs;
  Rx<DropdownModel> gender = DropdownModel(id: '', display: '').obs;
  RxList<DropdownModel> listGenders = <DropdownModel>[].obs;
  final ProcService _procService = Get.find<ProcService>();
  var isLoading = false.obs;
  var isLoadingGenders = false.obs;
  RxInt numGender = 0.obs;
  var isLoadingUpdate = false.obs;
  UserAccessModel? userAccessModel;

  @override
  Future<void> onInit() async {
    super.onInit();
    userID.value = await getUserID();
    await loadInfoProfile();
    await fetchUserProfile(userID: userID.value);
    await fetchAllGenders();
    numGender.value = int.parse(gender.value.id.toString());
  }

  DropdownModel getDefaultDropDownGender(String display) {
    return listGenders.firstWhere(
      (e) => e.display == display,
      orElse: () => DropdownModel(id: '', display: ''),
    );
  }

  Future<void> loadInfoProfile() async {
    userAccessModel = await UserUseCase.getUser();
    update(["bodyID"]);
  }

  Future<String> getUserID() async {
    final userId = await UserUseCase.getUser();
    return userId!.userID;
  }

  Future<void> fetchAllGenders() async {
    isLoading.value = true;
    final List<Map<String, dynamic>> body = [];
    try {
      if (true) {
        final response =
            await _procService.callProc("Proc_Mobile_GetAllGioiTinh", body);
        if (response.isNotEmpty) {
          final List<DropdownModel> genders = response
              .map((json) => DropdownModel(
                    id: json['GioiTinhID'] ?? '',
                    display: json['TenGioiTinh'] ?? '',
                  ))
              .toList();
          listGenders.addAll(genders);
          // gender.value = getDefaultDropDownGender(defGender.value);
          gender.value.id = getDefaultDropDownGender(defGender.value).id;

          gender.value.display =
              getDefaultDropDownGender(defGender.value).display;
          print(gender.value.display);
          print(gender.value.id);
        }
      }
    } catch (e) {
      print("Error logging in: $e");
    } finally {
      isLoading.value = false;
    }
  }

  // Lấy dữ liệu về
  Future<void> fetchUserProfile({required String userID}) async {
    isLoadingUpdate.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "UserID", "type": "Guid", "value": userID},
    ];
    try {
      if (true) {
        final response =
            await _procService.callProc("Proc_Mobile_GetUserInfo_byDB", body);
        if (response.isNotEmpty) {
          final userProfile = response
              .map((json) => ProfileUserModel(
                    diaChi: json['DiaChi'] ?? '',
                    tenDangNhap: json['TenDangNhap'] ?? '',
                    hoTen: json['HoTen'] ?? '',
                    gioiTinh: json['GioiTinh'] ?? '',
                    diDong: json['DiDong'] ?? '',
                    email: json['Email'] ?? '',
                    ngaySinh: json['NgaySinh'] ?? '',
                    hinhDaiDien: json['HinhDaiDien'] ?? '',
                    donViCode: json['DonViCode'] ?? '',
                    tenDonVi: json['TenDonVi'] ?? '',
                    donViID: json['DonViID'] ?? '',
                    tinhID: json['TinhID'] ?? '',
                    huyenID: json['HuyenID'] ?? '',
                    xaID: json['XaID'] ?? '',
                    thonID: json['ThonID'] ?? '',
                    tenTinh: json['TenTinh'] ?? '',
                    tenHuyen: json['TenHuyen'] ?? '',
                    tenXa: json['TenXa'] ?? '',
                    tenThon: json['TenThon'] ?? '',
                    userGroupID: json['UserGroupID'] ?? '',
                    userGroupCode: json['UserGroupCode'] ?? '',
                  ))
              .first;
          name.value = userProfile.hoTen;
          defGender.value = userProfile.gioiTinh;
          phone.value = userProfile.diDong;
          email.value = userProfile.email;
          birthday.value = DateFormat("dd/MM/yyyy").parse(userProfile.ngaySinh);
          avatarPathOld.value = userProfile.hinhDaiDien;
          location.value = userProfile.diaChi;
          // avatarPath.value = "${userAccessModel?.siteURL}/${userProfile.hinhDaiDien.substring(1)}";
          avatarPath.value =
              "${userAccessModel?.siteURL}${userProfile.hinhDaiDien.replaceFirst(RegExp(r'^/+'), '')}";
        }
      }
    } catch (e) {
      print("Error logging in: $e");
    } finally {
      isLoadingUpdate.value = false;
      print("avatarPathOld: ${avatarPathOld.value}");
      print("avatarPath: ${avatarPath.value}");
    }
  }

  //Lưu thông tin người dùng
  Future<List<dynamic>> updateUserProfile({required String userID}) async {
    isLoading.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "HoTen", "type": "String", "value": name.value.toString()},
      {
        "name": "GioiTinh",
        "type": "String",
        "value": gender.value.display.toString()
      },
      {"name": "Email", "type": "String", "value": email.value.toString()},
      {"name": "DiDong", "type": "String", "value": phone.value.toString()},
      {
        "name": "NgaySinh",
        "type": "String",
        "value": birthday.value.toString()
      },
      {"name": "DiaChi", "type": "String", "value": location.value.toString()},
      {"name": "UserID", "type": "guid", "value": userID},
    ];
    try {
      final response =
          await _procService.callProc("Proc_Mobile_UpdateUserInfo", body);

      if (response is List<dynamic>) {
        return response;
      } else {
        return [];
      }
    } catch (e) {
      print("Error logging in: $e");
      return [];
    } finally {
      isLoading.value = false;
    }
  }

  String cutUrl(String url) {
    Uri uri = Uri.parse(url);
    String path = uri
        .path; // Lấy phần /Uploads/Images/e200d681-a516-458e-ac99-37cc7f62ff81.png
    return path.startsWith('/')
        ? path.substring(1)
        : path; // Xóa dấu '/' ở đầu nếu có
  }

  Future<List<dynamic>> updateUserAvatar(XFile? image) async {
    isLoading.value = true;
    Uint8List imageBytes = await image!.readAsBytes();
    final res = await UploadService().uploadImage(imageBytes, "ANHDAIDIEN");
    if (res?.statusCode != 200) {
      log('Lỗi khi up ảnh: ${res?.statusCode}');
      return [];
    }

    avatarPath.value = cutUrl(res?.data['data'][0]);
    log(res?.data['data'][0], name: "uploadAvatar");

    if (avatarPathOld.value.toString().isNotEmpty) {
      final resDel = await UploadService().deleteFiles([(avatarPathOld.value)]);
      if (resDel?.statusCode != 200) {
        log('Lỗi khi xóa ảnh cũ: ${resDel?.statusCode}');
      } else {
        log('Xóa ảnh cũ thành công: ${resDel?.statusCode} + path: $avatarPathOld');
      }
    }
    await updateAvatarPath(userID: userID.value);
    isLoading.value = false;

    return res?.data is List<dynamic> ? res?.data : [];
  }

  Future<void> updateAvatarPath({required String userID}) async {
    if (userID.isEmpty) {
      print('Error: NULL UserID');
      return;
    }

    if (avatarPath.value.toString().isEmpty) {
      print('Error: NULL avatarPath');
      return;
    }
    String trimmedAvatarPath =
    avatarPath.value.toString().replaceFirst("~", "");
    if (!trimmedAvatarPath.startsWith("/")) {
      trimmedAvatarPath = "/$trimmedAvatarPath";
    }
    log("trimmedAvatarPath" + trimmedAvatarPath);
    final List<Map<String, dynamic>> body = [
      {"name": "UserID", "type": "guid", "value": userID},
      {"name": "Anh", "type": "String", "value": trimmedAvatarPath}
    ];

    try {
      final response =
          await _procService.callProc("Proc_Mobile_UpdateAnhDaiDien", body);
      if (response.isNotEmpty) {
        print('Tải ảnh đại diện lên thành công');
      }
    } catch (e) {
      print("Error logging in: $e");
    }
  }

  Future<void> updateUserInformation() async {
    isLoadingUpdate.value = true;
    try {
      final List<dynamic> updateProfileResponse =
          await updateUserProfile(userID: userID.value);

      final List<dynamic>? updateAvatarResponse = avatarImage.value != null
          ? await updateUserAvatar(avatarImage.value)
          : null;

      if ((updateProfileResponse.isNotEmpty) ||
          (updateAvatarResponse?.isNotEmpty ?? false)) {
        SnackbarUtil.showSuccess(
            "Thông tin người dùng đã được cập nhật thành công!",
            alignment: 'bottom');
        Fluttertoast.showToast(
            msg:
                'Vui lòng thoát ra vào lại để hiển thị thông tin sau khi cập nhật!');
      } else {
        // Get.snackbar("Lỗi", "Cập nhật thông tin không thành công.");
        SnackbarUtil.showError(
            "Có lỗi xảy ra. Cập nhật thông tin không thành công!",
            alignment: 'bottom');
      }
    } catch (e) {
      print("Error updating user information: $e");
      Get.snackbar("Lỗi", "Đã xảy ra lỗi trong quá trình cập nhật.");
    } finally {
      isLoadingUpdate.value = false;
    }
  }
}
