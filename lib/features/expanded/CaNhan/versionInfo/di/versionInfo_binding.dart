
import 'package:get/get.dart';
import 'package:attp_2024/features/expanded/CaNhan/versionInfo/presentation/controller/versionInfo_controller.dart';

class VersionInfoBinding extends Bindings{
  @override
  void dependencies() {

    //cách 1 : khởi tạo trong lần đầu đư<PERSON> gọi
    // sử dụng tối ưu hiệu năng không pải load api nhiều lần
    Get.lazyPut(()=> VersionInfoController());

    // cách 2: tạo đối tượng ngay lập tưc bắt kể là dùng hay hông dùng
    // dùng trong trường hợp cần sử dụng ngay controller
    // Get.put(ProfileController());
  }
}