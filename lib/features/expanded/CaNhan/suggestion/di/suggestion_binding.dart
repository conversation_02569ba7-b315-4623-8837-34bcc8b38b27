import 'package:get/get.dart';
import 'package:attp_2024/features/expanded/CaNhan/suggestion/presentation/controller/suggestion_controller.dart';

class SuggestionBinding extends Bindings {
  @override
  void dependencies() {
    //cách 1 : khởi tạo trong lần đầu đư<PERSON> g<PERSON>
    // sử dụng tối ưu hiệu năng không pải load api nhiều lần
    Get.lazyPut(() => SuggestionController());

    // cách 2: tạo đối tượng ngay lập tưc bắt kể là dùng hay hông dùng
    // dùng trong trường hợp cần sử dụng ngay controller
    // Get.put(ProfileController());
  }
}
