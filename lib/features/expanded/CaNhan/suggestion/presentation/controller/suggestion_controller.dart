import 'dart:developer';

import 'package:attp_2024/core/configs/contanst/proc_constants.dart';
import 'package:attp_2024/core/services/user_use_case.dart';
import 'package:attp_2024/core/ui/snackbar/snackbar_until.dart';
import 'package:attp_2024/core/utils/validator.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/data/api/services/proc/proc_service.dart';

class SuggestionController extends GetxController {
  var rating = 4.obs;
  var isLoading = false.obs;
  final ProcService _procService = Get.find<ProcService>();
  var userId = ''.obs;
  var name = ''.obs;
  var email = ''.obs;
  var noiDung = ''.obs;
  var errGopY = ''.obs;
  var errName = ''.obs;
  var errEmail = ''.obs;

  bool validateGopY() {
    bool isValid = true;

    if (name.value.trim().isEmpty) {
      errName.value = 'Họ tên không được bỏ trống';
      isValid = false;
    } else {
      errName.value = '';
    }

    if (email.value.trim().isEmpty) {
      errEmail.value = 'Email không được bỏ trống';
      isValid = false;
    } else {
      errEmail.value = '';
    }

    if (noiDung.value.trim().isEmpty) {
      errGopY.value = 'Nội dung không được bỏ trống';
      isValid = false;
    } else {
      errGopY.value = '';
    }

    if (!Validators.validateEmail(email.value.trim())) {
      errEmail.value = 'Email không hợp lệ';
      isValid = false;
    } else {
      errEmail.value = '';
    }
    return isValid;
  }

  void updateRating(int value) {
    rating.value = value;
  }

  Future<void> onInit() async {
    userId.value = await _getUserID();
  }

  Future<String> _getUserID() async {
    final userId = await UserUseCase.getUser();
    return userId!.userID;
  }

  Future<void> guiGopY(BuildContext context) async {
    isLoading.value = true;

    final List<Map<String, dynamic>> body = [
      {"name": "UserID", "type": "Guid", "value": userId.value},
      {"name": "SoLuongSao", "type": "Int", "value": rating.value},
      {"name": "HoVaTen", "type": "String", "value": name.value},
      {"name": "Email", "type": "String", "value": email.value},
      {"name": "NoiDung", "type": "String", "value": noiDung.value},
      {"name": "Loai", "type": "Int", "value": 1},
    ];
    try {
      final response = await _procService.callProc(ProcConstants.guiGopY, body);
      if (response.isNotEmpty) {
        SnackbarUtil.showSuccess( "Lưu thông tin góp ý thành công",
            alignment: "bottom");
        Get.back();
      } else {
        SnackbarUtil.showError("Có lỗi xảy ra khi gửi dữ liệu!",
            alignment: "bottom");
        Get.back();
      }
    } catch (e) {
      print("Error logging in: $e");
    } finally {
      isLoading.value = false;
    }
  }
}
