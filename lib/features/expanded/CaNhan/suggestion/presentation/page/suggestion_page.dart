import 'package:attp_2024/core/ui/widgets/custom_textfield/widgets/custom_textfield.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/button/button_widget.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/features/expanded/CaNhan/suggestion/presentation/controller/suggestion_controller.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class SuggestionPage extends GetView<SuggestionController> {
  final FocusNode _focusNode = FocusNode();

  SuggestionPage({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppBarWidget(
        title: "<PERSON>ó<PERSON> ý về ứng dụng",
        titleSize: 18,
      ),
      body: Stack(
        children: [
          Container(
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage("assets/images/background_profile.png"),
                fit: BoxFit.cover,
              ),
            ),
          ),
          Obx(() => _buildBody(context))
        ],
      ),
    );
  }

  Widget _buildBody(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          spacing: 3.h,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _infoSuggestion(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const TextWidget(
                  text: 'Chạm để xếp hạng:',
                  fontWeight: FontWeight.w500,
                ),
                Row(
                  spacing: 2.0,
                  children: List.generate(5, (index) {
                    return GestureDetector(
                      child: Icon(
                        size: 25,
                        index < controller.rating.value
                            ? Icons.star
                            : Icons.star_border,
                        color: Colors.amber,
                      ),
                      onTap: () {
                        controller.updateRating(index + 1);
                      },
                    );
                  }),
                ),
              ],
            ),
            Column(
              children: [
                TextFieldWidget(
                  title: 'Họ và tên',
                  isRequired: true,
                  placeholder: 'Nhập họ và tên',
                  setValue: controller.name.value,
                  initialValue: '',
                  errorWidget: controller.errName.value,
                  onChange: (e) {
                    controller.name.value = e;
                  },
                ),
                TextFieldWidget(
                  title: 'Email',
                  isRequired: true,
                  placeholder: 'Nhập email',
                  setValue: controller.email.value,
                  initialValue: '',
                  errorWidget: controller.errEmail.value,
                  onChange: (e) {
                    controller.email.value = e;
                  },
                ),
                const Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const TextWidget(
                      text: 'Góp ý của bạn',
                      fontWeight: FontWeight.w500,
                    ),
                    const TextWidget(
                      text: '255 ký tự',
                      size: 13,
                    )
                  ],
                ),
                _sugesstionText(),
                SizedBox(
                  height: 45,
                  child: ButtonWidget(
                    ontap: () {
                      controller.validateGopY()
                          ? controller.guiGopY(context)
                          : null;
                    },
                    text: "Gửi góp ý",
                    backgroundColor: AppColors.primary,
                    borderRadius: 7,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  GestureDetector _sugesstionText() {
    return GestureDetector(
      onTap: () {
        _focusNode.requestFocus();
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 1,
        children: [
          Container(
            height: 205,
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(5),
              border: Border.all(
                  color: controller.errGopY.value.isNotEmpty
                      ? Colors.red
                      : AppColors.borderSuggestion,
                  width: 0.5),
            ),
            child: Column(
              children: [
                TextField(
                  focusNode: _focusNode, // Gán focusNode cho TextField
                  maxLines: null,
                  minLines: 1,
                  decoration: const InputDecoration(
                    contentPadding: EdgeInsets.symmetric(horizontal: 20),
                    border: InputBorder.none,
                  ),
                  onChanged: (value) {
                    controller.noiDung.value = value;
                  },
                ),
                // Thêm TextField vào đây nếu cần
              ],
            ),
          ),
          TextWidget(
            text: controller.errGopY.value,
            color: Colors.red,
            size: 14,
          )
        ],
      ),
    );
  }

  Container _infoSuggestion() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: AppColors.white,
        border: Border.all(color: AppColors.borderSuggestion, width: 0.5),
      ),
      child: Padding(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              AppImageString.logoATTP,
              width: 116,
              height: 116,
            ),
            const SizedBox(height: 15),
            const TextWidget(
              text:
                  'Nhằm cải tiến về dịch vụ cũng như ứng dụng, chúng tôi muốn lắng nghe ý kiến của bạn để chúng tôi hiểu rõ hơn về nhu cầu nhằm cải tiến phần mềm mang lại trải nghiệm tốt hơn. Hãy cho chúng tôi biết có thể cải tiến điều gì để giúp sản phẩm ngày càng tốt hơn nhé',
              size: 15,
              fontWeight: FontWeight.w500,
              color: AppColors.titleSuggestion,
            ),
          ],
        ),
      ),
    );
  }
}
