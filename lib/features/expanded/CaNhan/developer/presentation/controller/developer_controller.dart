import 'package:get/get.dart';
import 'package:attp_2024/core/data/api/services/proc/proc_service.dart';
import 'package:attp_2024/core/data/dto/response/ThongTinHeThongModel.dart';

class DeveloperController extends GetxController {
  var isLoading = true.obs;
  final ProcService _procService = Get.find<ProcService>();
  var infoSys = <ThongTinHeThongModel>[].obs;

  @override
  Future<void> onInit() async {
    super.onInit();
    await fetchThongTin(2);
  }

  Future<void> fetchThongTin(int viTri) async {
    isLoading.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "ViTri", "type": "Int", "value": viTri}
    ];
    try {
      final response = await _procService.callProc(
          "Proc_Mobile_GetHeThongMobile_ByViTri", body);
      if (response.isNotEmpty) {
        final infoSystem = response
            .map((json) => ThongTinHeThongModel(
                  HeThong_MobileID: json['HeThong_MobileID'] ?? '',
                  TenHeThong_Mobile: json['TenHeThong_Mobile'] ?? '',
                  ViTri: json['ViTri'] ?? 1,
                  MoTa: json['MoTa'] ?? '',
                  NoiDung: json['NoiDung'] ?? '',
                  DinhDanh: json['DinhDanh'] ?? '',
                  PhienBan: json['PhienBan'] ?? '',
                  TrangThai: json['TrangThai'] ?? false,
                  Icon: json['Icon'] ?? '',
                ))
            .toList();
        infoSys.assignAll(infoSystem);
      }
    } catch (e) {
      print("Error logging in: $e");
    } finally {
      isLoading.value = false;
    }
  }
}
