import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/custom_textfield/widgets/custom_passwordField.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/core/utils/validator.dart';
import 'package:attp_2024/features/expanded/CaNhan/changePassword/presentation/controller/changePassword_controller.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../../../../../../core/ui/widgets/appbar/app_bar_widget.dart';
import '../../../../../../core/ui/widgets/button/button_widget.dart';

class ChangePasswordPage extends GetView<ChangePasswordController> {
  const ChangePasswordPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppBarWidget(
        title: "Đổi mật khẩu",
        centerTitle: true,
      ),
      body: Stack(
        children: [
          Container(
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage("assets/images/background_profile.png"),
                fit: BoxFit.cover,
              ),
            ),
          ),
          Obx(() => _buildBody(context))
        ],
      ),
    );
  }

  // ignore: non_constant_identifier_names
  Widget _buildBody(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Gap(2.h),
            PasswordFieldWidget(
              title: 'Mật khẩu hiện tại',
              placeholder: '',
              initialValue: '',
              prefixIcon: Icon(
                CupertinoIcons.lock_circle_fill,
                size: 20,
                color: AppColors.primary,
              ),
              onChange: (value) {
                controller.password.value = value;
              },
              errorWidget: controller.errPassword.value,
            ),
          
            PasswordFieldWidget(
              title: 'Mật khẩu mới',
              placeholder: '',
              initialValue: '',
              prefixIcon: Icon(
                CupertinoIcons.lock_circle_fill,
                size: 20,
                color: AppColors.primary,
              ),
              onChange: (value) {
                controller.newPassword.value = value;
              },
              errorWidget: controller.errNewPassword.value,
            ),
           
            PasswordFieldWidget(
              title: 'Nhập lại mật khẩu',
              placeholder: '',
              initialValue: '',
              prefixIcon: Icon(
                CupertinoIcons.lock_circle_fill,
                size: 20,
                color: AppColors.primary,
              ),
              onChange: (value) {
                controller.rePassword.value = value;
              },
              errorWidget: controller.errRePassword.value,
            ),
           
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const TextWidget(
                  text: '*Mật khẩu mới bao gồm',
                  size: 15,
                  color: AppColors.roleColors,
                ),
                const TextWidget(
                  text: 'Từ 7-20 ký tự',
                  size: 13,
                  color: AppColors.roleText,
                ),
                const TextWidget(
                  text: 'Ký tự thường (a-z)',
                  size: 13,
                  color: AppColors.roleText,
                ),
                const TextWidget(
                  text: 'Ký tự in hoa (A-Z)',
                  size: 13,
                  color: AppColors.roleText,
                ),
                const TextWidget(
                  text: 'Chữ số (1-9)',
                  size: 13,
                  color: AppColors.roleText,
                ),
                const SizedBox(
                  height: 30,
                ),
                SizedBox(
                  height: 45,
                  child: ButtonWidget(
                    ontap: () {
                      if (controller.checkPassword()) {
                        if (Validators.validPassword(
                            controller.newPassword.value)) {
                          controller.errRePassword.value = '';
                          controller.doiMatKhau(context);
                        } else {
                          controller.errRePassword.value =
                              'Mật khẩu không đúng định dạng !';
                          return;
                        }
                      }
                    },
                    text: "Lưu mật khẩu",
                    backgroundColor: AppColors.primary,
                    borderRadius: 7,
                  ),
                )
              ],
            ),
          ],
        ),
      ),
    );
  }
}
