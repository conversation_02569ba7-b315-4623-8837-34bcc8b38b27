// ignore: file_names
import 'package:get/get.dart';
import 'package:attp_2024/features/expanded/appInfo/presentation/controller/appInfo_controller.dart';

import '../../../../core/data/api/configs/dio_configs.dart';
import '../../../../core/data/api/services/proc/proc_service.dart';

class AppinfoBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => AppinfoController());
    Get.lazyPut(() => ProcService(Get.find<DioService>()));
  }
}
