import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/features/expanded/appInfo/presentation/controller/appInfo_controller.dart';

class AppinfoPage extends GetView<AppinfoController> {
  const AppinfoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppBarWidget(
        title: "Thông tin ứng dụng",
        titleSize: 18,
      ),
      body: Stack(
        children: [
          Container(
              decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage("assets/images/background_profile.png"),
              fit: BoxFit.cover,
            ),
          )),
          Obx(() => controller.infoSys.isNotEmpty
              ? _buildBody(context)
              : const Center(
                  child: TextWidget(
                    text: "Loading ...",
                    size: AppDimens.textSize12,
                    color: AppColors.gray2,
                  ),
                ))
        ],
      ),
    );
  }

  // ignore: non_constant_identifier_names
  Widget _buildBody(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.all(3.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            HtmlWidget(controller.infoSys.first.NoiDung),
          ],
        ),
      ),
    );
  }
}
