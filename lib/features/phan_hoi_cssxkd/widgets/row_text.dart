import 'dart:ffi';

import 'package:flutter/material.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';

Widget rowText(String label, String value,
    [double size = AppDimens.textSize14]) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 4.0),
    child: RichText(
      text: TextSpan(
        style: TextStyle(color: AppColors.black, fontSize: size),
        children: [
          TextSpan(
            text: "$label ",
          ),
          TextSpan(
            text: value,
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: size),
          ),
        ],
      ),
    ),
  );
}

