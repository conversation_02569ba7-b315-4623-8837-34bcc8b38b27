import 'package:get/get.dart';
import 'package:attp_2024/features/phan_hoi_cssxkd/presentation/controller/phan_hoi_cssxkd_controller.dart';

class PhanHoiCssxkdBinding extends Bindings {
  @override
  void dependencies() {
    //cách 1 : khởi tạo trong lần đầu đ<PERSON> g<PERSON>
    // sử dụng tối ưu hiệu năng không pải load api nhiều lần
    Get.lazyPut(() => PhanHoiCssxkdController());
    // cách 2: tạo đối tượng ngay lập tưc bắt kể là dùng hay hông dùng
    // dùng trong trường hợp cần sử dụng ngay controller
    // Get.put(ProfileController());
  }
}
