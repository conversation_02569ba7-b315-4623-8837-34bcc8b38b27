import 'dart:developer';
import 'package:attp_2024/core/data/models/user_access_model.dart';
import 'package:attp_2024/core/ui/snackbar/snackbar_until.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/data/api/services/proc/proc_service.dart';
import 'package:attp_2024/core/services/uploads/upload_service.dart';
import 'package:attp_2024/core/services/user_use_case.dart';

class PhanHoiCssxkdController extends GetxController {
  var isLoading = false.obs;
  var isLoadingCSSXKDSelectedData = false.obs;
  final ProcService _procService = Get.find<ProcService>();
  var phanAnhItemData = [].obs;
  var nhanVienListData = [].obs;
  var chucVuListData = [].obs;
  var viPhamListData = [].obs;
  var phanHoiSelectedData = [].obs;
  var donViId = '';
  var userId = '';

  var defaultValue = 'Không có thông tin'.obs;

  // Tiep nhan
  var viPhamATTPID = ''.obs;
  var nhanVienID = ''.obs;
  var chucVuID = ''.obs;
  var tenChucVu = ''.obs;
  var noiDung = ''.obs;
  final RxBool tuChoiTiepNhanIsChecked = false.obs;
  final RxBool congKhaiKetQuaIsChecked = true.obs;
  Rx<DateTime> ngayXuLy = DateTime.now().obs;

  UserAccessModel? userAccessModel;

  // check
  var viPhamErr = ''.obs;
  var nhanVienErr = ''.obs;
  var chucVuErr = ''.obs;
  var noiDungErr = ''.obs;

  var choTiepNhan = '80'.obs;
  var dangXuLy = '81'.obs;
  var daXuLy = '82'.obs;
  var tuChoi = '83'.obs;

  @override
  Future<void> onInit() async {
    super.onInit();
    donViId = await _getDonViID();
    userId = await _getUserID();
    // fetchAllPAATTPByTrangThai(); //Chưa duyệt
    fetchAllPAATTPByTrangThai(choTiepNhan.value);
    fetchAllNhanVien();
    fetchAllChucVu();
    fetchAllViPhamVSATTP();
    await loadInfoProfile();
  }

  Future<void> loadInfoProfile() async {
    userAccessModel = await UserUseCase.getUser();
  }

  Future<String> _getDonViID() async {
    final userId = await UserUseCase.getUser();
    return userId!.donViID;
  }

  Future<String> _getUserID() async {
    final userId = await UserUseCase.getUser();
    return userId!.userID;
  }

  void toggleTrangThaiTiepNhanCheckbox(bool value) {
    tuChoiTiepNhanIsChecked.value = value;
  }

  var selectedFiles = <String>[].obs;
  var selectedValueString = '';

  void addFiles(List<String> files) {
    selectedFiles.addAll(files);
  }

  void removeFile(String file) {
    selectedFiles.remove(file);
  }

  void clearFiles() {
    selectedFiles.clear();
  }

  //------------------------------------------------------------------------------ FETCH DATA

  Future<void> fetchATTPPhanHoibyPAATTPID(String id) async {
    isLoading.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "PAATTPID", "type": "Guid", "value": id}
    ];
    try {
      List<dynamic> resItem = await _procService.callProc(
          "Proc_Mobile_GetPAATTPPhanHoi_ByPAATTPID", body);
      List<Map<String, dynamic>> mappedCssxkdResponse =
          resItem.cast<Map<String, dynamic>>();
      phanHoiSelectedData.assignAll(
        mappedCssxkdResponse.map((Map<String, dynamic> itemJson) {
          return {
            "PAATTPPhanHoiID": itemJson['PAATTPPhanHoiID'],
            "TuChoi": itemJson['TuChoi'],
            "CongKhai": itemJson['CongKhai'],
            "SoGPKD": itemJson['SoGPKD'],
            "PAATTPID": itemJson['PAATTPID'],
            "CoSoSXKDID": itemJson['CoSoSXKDID'],
            "MaCoSoSXKD": itemJson['MaCoSoSXKD'],
            "TenCoSoSXKD": itemJson['TenCoSoSXKD'],
            "NgayCapGPKD": itemJson['NgayCapGPKD'],
            "NgayCapGCN": itemJson['NgayCapGCN'],
            "NgayHetHanGCN": itemJson['NgayHetHanGCN'],
            "DiaBanHCID_Tinh": itemJson['DiaBanHCID_Tinh'],
            "TinhName": itemJson['TinhName'],
            "DiaBanHCID_Huyen": itemJson['DiaBanHCID_Huyen'],
            "HuyenName": itemJson['HuyenName'],
            "DiaBanHCID_Xa": itemJson['DiaBanHCID_Xa'],
            "XaName": itemJson['XaName'],
            "DiaBanHCID_Thon": itemJson['DiaBanHCID_Thon'],
            "ThonName": itemJson['ThonName'],
            "DiaChiCoSo": itemJson['DiaChiCoSo'],
            "LoaiHinhCoSoID": itemJson['LoaiHinhCoSoID'],
            "HoVaTen": itemJson['HoVaTen'],
            "ChucVuID": itemJson['ChucVuID'],
            "ChucVuNguoiDaiDien": itemJson['ChucVuNguoiDaiDien'],
            "SoDienThoai": itemJson['SoDienThoai'],
            "EmailCS": itemJson['EmailCS'],
            "SoGCN": itemJson['SoGCN'],
            "TrangThai_GCN": itemJson['TrangThai_GCN'],
            "NoiDung": itemJson['NoiDung'],
            "TrangThai_XuLy": itemJson['TrangThai_XuLy'],
            "DinhKem": itemJson['DinhKem'],
            "UserID": itemJson['UserID'],
            "DonViID": itemJson['DonViID'],
            "NgayTao": itemJson['NgayTao'],
            "NgayCapNhat": itemJson['NgayCapNhat'],
            "NgayPhanHoi": itemJson['NgayPhanHoi'],
            "NhanVienID": itemJson['NhanVienID'],
            "TenNhanVien": itemJson['TenNhanVien'],
            "ChucVuID_NhanVien": itemJson['ChucVuID_NhanVien'],
            "ChucVuNhanVien": itemJson['ChucVuNhanVien'],
            "ViPhamATTPID": itemJson['ViPhamATTPID'],
            "NoiDungXuLy": itemJson['NoiDungXuLy'],
            "HinhThucXuLy": itemJson['HinhThucXuLy']
          };
        }).toList(),
      );
      log(phanHoiSelectedData.toString(), name: 'phanhoi');
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> fetchAllPAATTPByTrangThai(String trangThai) async {
    log('Đang fetch api bởi trạng thái: $trangThai, userid: $userId');
    isLoading.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "TrangThai", "type": "String", "value": trangThai},
      {"name": "DonViID", "type": "Guid", "value": donViId},
      {"name": "CongKhai", "type": "Boolean", "value": null}
    ];
    try {
      List<dynamic> resItem = await _procService.callProc(
          "Proc_Mobile_GetAll_PAATTP_ByTrangThai", body);
      List<Map<String, dynamic>> mappedResponse =
          resItem.cast<Map<String, dynamic>>();
      phanAnhItemData.assignAll(
        mappedResponse.map((Map<String, dynamic> itemJson) {
          return {
            "PAATTPID": itemJson['PAATTPID'],
            "MaPAATTP": itemJson['MaPAATTP'],
            "TieuDe": itemJson['TieuDe'],
            "NoiDung": itemJson['NoiDung'],
            "HoVaTen": itemJson['HoVaTen'],
            "SoDienThoai": itemJson['SoDienThoai'],
            "Email": itemJson['Email'],
            "AnDanh": itemJson['AnDanh'],
            "NgayGui": itemJson['NgayGui'],
            "DinhKem_PA": itemJson['DinhKem_PA'],
            "CoSoSXKDID": itemJson['CoSoSXKDID'],
            "MaCoSoSXKD": itemJson['MaCoSoSXKD'],
            "TenCoSoSXKD": itemJson['TenCoSoSXKD'],
            "SoGPKD": itemJson['SoGPKD'],
            "CoQuanCapGPKD": itemJson['CoQuanCapGPKD'],
            "SoDienThoai_CS": itemJson['SoDienThoai_CS'],
            "EmailCS": itemJson['EmailCS'],
            "NguoiDaiDien": itemJson['NguoiDaiDien'],
            "ChucVuID_NguoiDaiDien": itemJson['ChucVuID_NguoiDaiDien'],
            "TrangThai_GCN": itemJson['TrangThai_GCN'],
            "NgayCapGCN": itemJson['NgayCapGCN'],
            "NgayHetHanGCN": itemJson['NgayHetHanGCN'],
            "KinhDo": itemJson['KinhDo'],
            "ViDo": itemJson['ViDo'],
            "DiaBanHCID_Tinh": itemJson['DiaBanHCID_Tinh'],
            "DiaBanHCID_Huyen": itemJson['DiaBanHCID_Huyen'],
            "DiaBanHCID_Xa": itemJson['DiaBanHCID_Xa'],
            "DiaBanHCID_Thon": itemJson['DiaBanHCID_Thon'],
            "LoaiHinhCoSoID": itemJson['LoaiHinhCoSoID'],
            "TrangThai": itemJson['TrangThai'],
            "NhanVienID_XuLy_PA": itemJson['NhanVienID_XuLy_PA'],
            "TenNhanVien_XuLy_PA": itemJson['TenNhanVien_XuLy_PA'],
            "NgayXuLy_PA": itemJson['NgayXuLy_PA'],
            "ChucVuID_XuLy_PA": itemJson['ChucVuID_XuLy_PA'],
            "TenChucVu_XuLy_PA": itemJson['TenChucVu_XuLy_PA'],
            "NoiDung_XuLy_PA": itemJson['NoiDung_XuLy_PA'],
            "UserID_PA": itemJson['UserID_PA'],
            "NgayCapNhat_PA": itemJson['NgayCapNhat_PA'],
            "DonViID": itemJson['DonViID'],
            "DiaChi": itemJson['DiaChi'],
            "PAATTPPhanHoiID": itemJson['PAATTPPhanHoiID'],
            "NhanVienID_XuLy_PH": itemJson['NhanVienID_XuLy_PH'],
            "TenNhanVien_XuLy_PH": itemJson['TenNhanVien_XuLy_PH'],
            "ChucVuID_XuLy_PH": itemJson['ChucVuID_XuLy_PH'],
            "TenChucVu_XuLy_PH": itemJson['TenChucVu_XuLy_PH'],
            "NgayXuLy_PH": itemJson['NgayXuLy_PH'],
            "NoiDung_PH": itemJson['NoiDung_PH'],
            "TenHinhThucXuLy": itemJson['TenHinhThucXuLy'],
            "DinhKem_PH": itemJson['DinhKem_PH'],
            "TrangThai_XuLy_PH": itemJson['TrangThai_XuLy_PH']
          };
        }).toList(),
      );
      log(phanAnhItemData.toString(), name: 'phananh');
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> fetchAllNhanVien() async {
    isLoading.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "DonViID", "type": "Guid", "value": donViId}
    ];
    try {
      List<dynamic> resItem =
          await _procService.callProc("Proc_Mobile_GetComBo_NhanVien", body);
      List<Map<String, dynamic>> mappedCssxkdResponse =
          resItem.cast<Map<String, dynamic>>();
      nhanVienListData.assignAll(
        mappedCssxkdResponse.map((Map<String, dynamic> itemJson) {
          return {
            "NhanVienID": itemJson['NhanVienID'],
            "MaNhanVien": itemJson['MaNhanVien'],
            "TenNhanVien": itemJson['TenNhanVien'],
          };
        }).toList(),
      );
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> fetchAllChucVu() async {
    isLoading.value = true;
    final List<Map<String, dynamic>> body = [];
    try {
      List<dynamic> resItem =
          await _procService.callProc("Proc_Mobile_GetComBo_ChucVu", body);
      List<Map<String, dynamic>> mappedCssxkdResponse =
          resItem.cast<Map<String, dynamic>>();
      chucVuListData.assignAll(
        mappedCssxkdResponse.map((Map<String, dynamic> itemJson) {
          return {
            "ChucVuID": itemJson['ChucVuID'],
            "MaChucVu": itemJson['MaChucVu'],
            "TenChucVu": itemJson['TenChucVu'],
          };
        }).toList(),
      );
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> fetchAllChucVuByID(String id) async {
    isLoading.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "ChucVuID", "type": "Guid", "value": id}
    ];
    try {
      List<dynamic> resItem =
          await _procService.callProc("Proc_Mobile_GetChucVuTheoID", body);
      List<Map<String, dynamic>> mappedCssxkdResponse =
          resItem.cast<Map<String, dynamic>>();
      chucVuListData.assignAll(
        mappedCssxkdResponse.map((Map<String, dynamic> itemJson) {
          return {
            "ChucVuID": itemJson['ChucVuID'],
            "MaChucVu": itemJson['MaChucVu'],
            "TenChucVu": itemJson['TenChucVu'],
          };
        }).toList(),
      );
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> fetchAllViPhamVSATTP() async {
    isLoading.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "DonViID", "type": "Guid", "value": donViId}
    ];
    try {
      List<dynamic> resItem =
          await _procService.callProc("Proc_Mobile_GetViPhamATTP_Combo", body);
      List<Map<String, dynamic>> mappedCssxkdResponse =
          resItem.cast<Map<String, dynamic>>();
      viPhamListData.assignAll(
        mappedCssxkdResponse.map((Map<String, dynamic> itemJson) {
          return {
            "ViPhamATTPID": itemJson['ViPhamATTPID'],
            "SoVanBan": itemJson['SoVanBan'],
            "NoiDung": itemJson['NoiDung'],
          };
        }).toList(),
      );
      log(viPhamListData.toString(), name: "akr");
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isLoading.value = false;
    }
  }

  //------------------------------------------------------------------------------ POST DATA

  Future<void> capNhatThongTinPAATTP(BuildContext context, String pAATTPID,
      {bool isNhapKetQua = false}) async {
    isLoading.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "PAATTPID", "type": "Guid", "value": pAATTPID},
      {"name": "NhanVienID_XuLy", "type": "Guid", "value": nhanVienID.value},
      {
        "name": "NgayXuLy",
        "type": "String",
        "value": ngayXuLy.value.toString()
      },
      {"name": "ChucVuID_XuLy", "type": "Guid", "value": chucVuID.value},
      {"name": "NoiDung_XuLy", "type": "String", "value": noiDung.value},
      {"name": "UserID", "type": "Guid", "value": userId},
      {
        "name": "TrangThai",
        "type": "String",
        "value": isNhapKetQua
            ? daXuLy.value
            : tuChoiTiepNhanIsChecked.value
                ? tuChoi.value
                : dangXuLy.value
      },
    ];
    try {
      final response =
          await _procService.callProc("Proc_Mobile_Update_PAATTP", body);
      if (response.isNotEmpty) {
        log(response.toString(), name: 'akr');
        Get.back();
        SnackbarUtil.showSuccess("Cập nhật thành công !", alignment: "bottom");
      } else {
        Get.back();
        SnackbarUtil.showSuccess("Có lỗi xảy ra khi gửi dữ liệu !",
            alignment: "bottom");
      }
    } catch (e) {
      print("Error logging in: $e");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> luuThongTinPAATTPPhanHoi(
      BuildContext context, final Map<String, dynamic> itemPhanAnhData,
      {bool isNhapKetQua = false}) async {
    isLoading.value = true;
    final List<Map<String, dynamic>> body;
    if (itemPhanAnhData['PAATTPPhanHoiID'] == null ||
        itemPhanAnhData['PAATTPPhanHoiID'] == '') {
      body = [
        {"name": "Loai", "type": "String", "value": "them"},
        {"name": "PAATTPPhanHoiID", "type": "Guid", "value": ""},
        {
          "name": "TuChoi",
          "type": "Boolean",
          "value": tuChoiTiepNhanIsChecked.value
        },
        {
          "name": "CongKhai",
          "type": "Boolean",
          "value": congKhaiKetQuaIsChecked.value
        },
        {
          "name": "PAATTPID",
          "type": "Guid",
          "value": itemPhanAnhData['PAATTPID']
        },
        {
          "name": "CoSoSXKDID",
          "type": "Guid",
          "value": itemPhanAnhData['CoSoSXKDID']
        },
        {
          "name": "MaCoSoSXKD",
          "type": "String",
          "value": itemPhanAnhData['MaCoSoSXKD']
        },
        {
          "name": "TenCoSoSXKD",
          "type": "String",
          "value": itemPhanAnhData['TenCoSoSXKD']
        },
        {
          "name": "DiaBanHCID_Tinh",
          "type": "Guid",
          "value": itemPhanAnhData['DiaBanHCID_Tinh']
        },
        {
          "name": "DiaBanHCID_Huyen",
          "type": "Guid",
          "value": itemPhanAnhData['DiaBanHCID_Huyen']
        },
        {
          "name": "DiaBanHCID_Xa",
          "type": "Guid",
          "value": itemPhanAnhData['DiaBanHCID_Xa']
        },
        {
          "name": "DiaBanHCID_Thon",
          "type": "Guid",
          "value": itemPhanAnhData['DiaBanHCID_Thon']
        },
        {
          "name": "DiaChiCoSo",
          "type": "String",
          "value": itemPhanAnhData['DiaChi']
        },
        {
          "name": "LoaiHinhCoSoID",
          "type": "Guid",
          "value": itemPhanAnhData['LoaiHinhCoSoID']
        },
        {
          "name": "HoVaTen",
          "type": "String",
          "value": itemPhanAnhData['NguoiDaiDien']
        },
        {
          "name": "ChucVuID",
          "type": "Guid",
          "value": itemPhanAnhData['ChucVuID_NguoiDaiDien']
        },
        {"name": "NhanVienId", "type": "Guid", "value": nhanVienID.value},
        {"name": "ChucVuID_NhanVien", "type": "Guid", "value": chucVuID.value},
        {
          "name": "SoDienThoai",
          "type": "String",
          "value": itemPhanAnhData['SoDienThoai_CS']
        },
        {
          "name": "EmailCS",
          "type": "String",
          "value": itemPhanAnhData['EmailCS']
        },
        {"name": "SoGCN", "type": "String", "value": itemPhanAnhData['SoGPKD']},
        {
          "name": "TrangThai_GCN",
          "type": "String",
          "value": itemPhanAnhData['TrangThai_GCN']
        },
        {"name": "NoiDung", "type": "String", "value": noiDung.value},
        {"name": "TrangThai_XuLy", "type": "String", "value": "82"},
        {"name": "DinhKem", "type": "String", "value": selectedValueString},
        {"name": "UserID", "type": "Guid", "value": userId},
        {"name": "DonViID", "type": "Guid", "value": donViId},
        {
          "name": "NgayTao",
          "type": "DateTime",
          "value": DateTime.now().toString()
        },
        {"name": "ViPhamATTPID", "type": "Guid", "value": viPhamATTPID.value},
        {
          "name": "NgayPhanHoi",
          "type": "DateTime",
          "value": ngayXuLy.value.toString()
        },
      ];
    } else {
      body = [
        {"name": "Loai", "type": "String", "value": "sua"},
        {
          "name": "PAATTPPhanHoiID",
          "type": "Guid",
          "value": itemPhanAnhData['PAATTPPhanHoiID']
        },
        {
          "name": "TuChoi",
          "type": "Boolean",
          "value": tuChoiTiepNhanIsChecked.value
        },
        {
          "name": "CongKhai",
          "type": "Boolean",
          "value": congKhaiKetQuaIsChecked.value
        },
        {
          "name": "PAATTPID",
          "type": "Guid",
          "value": itemPhanAnhData['PAATTPID']
        },
        {
          "name": "CoSoSXKDID",
          "type": "Guid",
          "value": itemPhanAnhData['CoSoSXKDID']
        },
        {
          "name": "MaCoSoSXKD",
          "type": "String",
          "value": itemPhanAnhData['MaCoSoSXKD']
        },
        {
          "name": "TenCoSoSXKD",
          "type": "String",
          "value": itemPhanAnhData['TenCoSoSXKD']
        },
        {
          "name": "DiaBanHCID_Tinh",
          "type": "Guid",
          "value": itemPhanAnhData['DiaBanHCID_Tinh']
        },
        {
          "name": "DiaBanHCID_Huyen",
          "type": "Guid",
          "value": itemPhanAnhData['DiaBanHCID_Huyen']
        },
        {
          "name": "DiaBanHCID_Xa",
          "type": "Guid",
          "value": itemPhanAnhData['DiaBanHCID_Xa']
        },
        {
          "name": "DiaBanHCID_Thon",
          "type": "Guid",
          "value": itemPhanAnhData['DiaBanHCID_Thon']
        },
        {
          "name": "DiaChiCoSo",
          "type": "String",
          "value": itemPhanAnhData['DiaChi']
        },
        {
          "name": "LoaiHinhCoSoID",
          "type": "Guid",
          "value": itemPhanAnhData['LoaiHinhCoSoID']
        },
        {
          "name": "HoVaTen",
          "type": "String",
          "value": itemPhanAnhData['NguoiDaiDien']
        },
        {
          "name": "ChucVuID",
          "type": "Guid",
          "value": itemPhanAnhData['ChucVuID_NguoiDaiDien']
        },
        {"name": "NhanVienId", "type": "Guid", "value": nhanVienID.value},
        {"name": "ChucVuID_NhanVien", "type": "Guid", "value": chucVuID.value},
        {
          "name": "SoDienThoai",
          "type": "String",
          "value": itemPhanAnhData['SoDienThoai_CS']
        },
        {
          "name": "EmailCS",
          "type": "String",
          "value": itemPhanAnhData['EmailCS']
        },
        {"name": "SoGCN", "type": "String", "value": itemPhanAnhData['SoGPKD']},
        {
          "name": "TrangThai_GCN",
          "type": "String",
          "value": itemPhanAnhData['TrangThai_GCN']
        },
        {"name": "NoiDung", "type": "String", "value": noiDung.value},
        {"name": "TrangThai_XuLy", "type": "String", "value": "82"},
        {"name": "DinhKem", "type": "String", "value": selectedValueString},
        {"name": "UserID", "type": "Guid", "value": userId},
        {"name": "DonViID", "type": "Guid", "value": donViId},
        {
          "name": "NgayTao",
          "type": "DateTime",
          "value": DateTime.now().toString()
        },
        {"name": "ViPhamATTPID", "type": "Guid", "value": viPhamATTPID.value},
        {
          "name": "NgayPhanHoi",
          "type": "DateTime",
          "value": ngayXuLy.value.toString()
        },
      ];
    }

    try {
      final response = await _procService.callProc(
          "Proc_Mobile_LuuThongTin_PAATTPPhanHoi", body);
      if (response.isNotEmpty) {
        log(response.toString(), name: 'akr');
        Get.back();

        SnackbarUtil.showSuccess("Cập nhật thành công !", alignment: "bottom");
      } else {
        Get.back();
        SnackbarUtil.showError("Có lỗi xảy ra khi gửi dữ liệu!",
            alignment: "bottom");
      }
    } catch (e) {
      log('Error: $e', name: 'err');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> luuTepTinPAATTPPhanHoi() async {
    isLoading.value = true;
    final res = await UploadService().uploadPdfFiles(
        selectedFiles, "VB", "PAATTPPhanHoi", '${userAccessModel?.siteURL}');
    selectedValueString = res!.data['data']
        .map((path) => path.replaceFirst('~', ''))
        .join('*')
        .toString();
    isLoading.value = false;
  }

  //------------------------------------------------------------------------------ HANDLE CHOOSE

  Future<void> handleCapNhatThongTinPAATTP(
      BuildContext context, String pAATTPID) async {
    await capNhatThongTinPAATTP(context, pAATTPID);
    Get.back();
    phanAnhItemData.clear();
    fetchAllPAATTPByTrangThai('80');
  }

  Future<void> handleCapNhatThongTinPAATTPPhanHoi(
    BuildContext context,
    final Map<String, dynamic> itemPhanAnhData,
  ) async {
    if (selectedFiles.isNotEmpty) await luuTepTinPAATTPPhanHoi();
    await capNhatThongTinPAATTP(context, itemPhanAnhData['PAATTPID'],
        isNhapKetQua: true);
    await luuThongTinPAATTPPhanHoi(context, itemPhanAnhData);
  }
}
