// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:attp_2024/core/ui/widgets/input/custom_check_box.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/features/phan_hoi_cssxkd/presentation/controller/phan_hoi_cssxkd_controller.dart';

import '../../../../core/configs/theme/app_colors.dart';
import '../../../../core/ui/widgets/CustomDatePicker/CustomDatePicker.dart';
import '../../../../core/ui/widgets/button/button_widget.dart';
import '../../../../core/ui/widgets/custom_combo/combo.dart';
import '../../../../core/ui/widgets/custom_textfield/widgets/custom_textarea.dart';

class PhanHoiCssxkdTiepNhan extends GetView<PhanHoiCssxkdController> {
  final Map<String, dynamic> itemPhanAnhData;

  const PhanHoiCssxkdTiepNhan({
    super.key,
    required this.itemPhanAnhData,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppBarWidget(
        title: "Tiếp nhận phản ánh ATTP",
      ),
      body: Obx(() => _buildBody(context)),
    );
  }

  Widget _buildBody(BuildContext context) {
    return SafeArea(
        child: SingleChildScrollView(
      padding: EdgeInsets.all(4.w),
      child: Column(
        children: [
          // Text(itemPhanAnhData.toString()),
          Container(
            padding: const EdgeInsets.all(10.0),
            decoration: BoxDecoration(
                border: Border.all(width: 0.5),
                color: AppColors.white,
                borderRadius: const BorderRadius.all(
                  Radius.circular(5.0),
                )),
            child: TextWidget(
              text:
                  "Bạn đang thực hiện tiếp nhận phản ánh của người dân, tổ chức về an toàn thực phẩm, người gửi: ${itemPhanAnhData['HoVaTen']} với nội dung: ${itemPhanAnhData['NoiDung']}. Vui lòng điền đầy đủ các thông tin bên dưới và nhấn vào nút “Tiếp nhận/từ chối” để thực hiện thao tác.",
              color: AppColors.customColor1,
              size: AppDimens.textSize13,
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          CustomDatePicker(
            // isMaximumDate: true,
            isRequired: false,
            title: 'Ngày tiếp nhận',
            placeholder: 'dd-MM-yyyy',
            date: controller.ngayXuLy.value,
            // Ngày mặc định
            setTime: controller.ngayXuLy.value,
            onChange: (DateTime? newDate) {
              print('Ngày đã chọn: $newDate');
              controller.ngayXuLy.value = newDate!;
            },
            showClearButton: false,
          ),
          const SizedBox(
            height: 10,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Obx(
                  () => CustomCombobox(
                    title: 'Người xử lý',
                    weight: 45.w,
                    dropDownList: controller.nhanVienListData
                        .map((e) => DropdownModel(
                              id: e['NhanVienID'],
                              display: e['TenNhanVien'],
                            ))
                        .toList(),
                    defaultSelectedItem: controller.nhanVienID.value.isNotEmpty
                        ? controller.nhanVienListData
                            .where((e) =>
                                e['NhanVienID'].toString() ==
                                controller.nhanVienID.value)
                            .map((e) => DropdownModel(
                                  id: e['NhanVienID'],
                                  display: e['TenNhanVien'],
                                ))
                            .firstOrNull
                        : null,
                    onChange: (selectedItem) {
                      if (selectedItem != null) {
                        controller.nhanVienID.value =
                            selectedItem.id.toString();
                        controller.nhanVienErr.value = '';
                      }
                      log(controller.nhanVienID.value,
                          name: 'Đã chọn nhân viên:');
                    },
                    delete: () {
                      controller.nhanVienID.value = '';
                      log(controller.nhanVienID.value,
                          name: 'Đã chọn nhân viên:');
                    },
                    errorText: controller.nhanVienErr.value,
                  ),
                ),
              ),
              Expanded(
                child: CustomCombobox(
                  title: 'Chức vụ',
                  weight: 45.w,
                  dropDownList: controller.chucVuListData
                      .map((e) => DropdownModel(
                            id: e['ChucVuID'],
                            display: e['TenChucVu'],
                          ))
                      .toList(),
                  defaultSelectedItem: controller.chucVuID.value.isNotEmpty
                      ? controller.chucVuListData
                          .where((e) =>
                              e['ChucVuID'].toString() ==
                              controller.chucVuID.value)
                          .map((e) => DropdownModel(
                                id: e['ChucVuID'],
                                display: e['TenChucVu'],
                              ))
                          .firstOrNull
                      : null,
                  onChange: (selectedItem) {
                    if (selectedItem != null) {
                      controller.chucVuID.value = selectedItem.id.toString();
                      controller.chucVuErr.value = '';
                    }
                    log(controller.chucVuID.value, name: 'Đã chọn chức vụ:');
                  },
                  delete: () {
                    controller.chucVuID.value = '';
                  },
                  errorText: controller.chucVuErr.value,
                ),
              ),
            ],
          ),

          const SizedBox(height: 10),

          TextAreaWidget(
            title: 'Nội dung tiếp nhận',
            placeholder: 'Nhập nội dung',
            initialValue: controller.noiDung.value,
            // errorWidget: controller.errNoiDung.value,
            onChange: (e) {
              controller.noiDung.value = e;
              // controller.noiDung.value.isEmpty
              //     ? controller.errNoiDung.value =
              //         'Nội dung không được để trống !'
              //     : controller.errNoiDung.value = '';
            },
          ),
          Row(spacing: 2.w, children: [
            CustomCheckBox(
              checkValue: controller.tuChoiTiepNhanIsChecked,
            ),
            const Expanded(
              child: TextWidget(
                text:
                    "Từ chối tiếp nhận phản ánh (Trường hợp thông tin phản ánh không chuẩn xác, chưa đủ cơ sở, thông tin phản ánh chưa cụ thể,...)",
                color: AppColors.error,
                size: AppDimens.textSize13,
              ),
            )
          ]),
          const SizedBox(height: 10),
          SizedBox(
            height: 45,
            child: ButtonWidget(
              ontap: () {
                if (controller.nhanVienID.value.isEmpty) {
                  controller.nhanVienErr.value = "Vui lòng chọn người xử lý !";
                  return;
                }
                if (controller.chucVuID.value.isEmpty) {
                  controller.chucVuErr.value = 'Vui lòng chọn chức vụ !';
                  return;
                }
                controller.handleCapNhatThongTinPAATTP(
                    context, itemPhanAnhData['PAATTPID']);
                controller.nhanVienID.value = '';
                controller.chucVuID.value = '';
              },
              text: "Tiếp nhận",
              backgroundColor: AppColors.submitButtonColor,
              borderRadius: 7,
            ),
          )
        ],
      ),
    ));
  }
}
