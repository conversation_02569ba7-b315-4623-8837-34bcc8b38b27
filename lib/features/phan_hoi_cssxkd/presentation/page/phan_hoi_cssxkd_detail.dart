// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:developer';
import 'dart:io';

import 'package:attp_2024/core/configs/contents/app_content.dart';
import 'package:attp_2024/core/ui/widgets/webview/webview_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:attp_2024/core/ui/widgets/carouser_slider/media_carouser.dart';
import 'package:attp_2024/core/ui/widgets/load/loading.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/core/utils/convert_text.dart';
import 'package:attp_2024/features/phan_hoi_cssxkd/presentation/controller/phan_hoi_cssxkd_controller.dart';
import 'package:attp_2024/features/phan_hoi_cssxkd/presentation/page/phan_hoi_cssxkd_nhap_ket_qua.dart';
import 'package:attp_2024/features/phan_hoi_cssxkd/presentation/page/phan_hoi_cssxkd_tiep_nhan.dart';
import 'package:attp_2024/features/phan_hoi_cssxkd/widgets/row_text.dart';

class PhanHoiCssxkdDetail extends GetView<PhanHoiCssxkdController> {
  final Map<String, dynamic> itemPhanAnhData;

  const PhanHoiCssxkdDetail({
    super.key,
    required this.itemPhanAnhData,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarWidget(
        title: "Chi tiết phản ánh",
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(
              Icons.more_vert,
              color: AppColors.white,
            ),
            onSelected: (String value) {
              switch (value) {
                case 'item_1':
                  Get.to(() => PhanHoiCssxkdTiepNhan(
                        itemPhanAnhData: itemPhanAnhData,
                      ));
                  controller.noiDung.value =
                      'Tiếp nhận phản ánh của người dân, tổ chức về an toàn thực phẩm tại số PAKN: 1, ngày gửi: ${itemPhanAnhData['NgayGui']}, người gửi: ${itemPhanAnhData['HoVaTen']} về nội dung: "${itemPhanAnhData['NoiDung']}".';
                  break;
                case 'item_2':
                  Get.to(() => PhanHoiCssxkdNhapKetQua(
                        itemPhanAnhData: itemPhanAnhData,
                      ));
                  controller.noiDung.value =
                      'Sau khi cơ quan quản lý có thẩm quyền kiểm tra, thẩm định tại phản ánh của người dân, tổ chức về an toàn thực phẩm tại số PAKN: , ngày gửi: ${itemPhanAnhData['NgayGui']}, người gửi: ${itemPhanAnhData['HoVaTen']} về nội dung: "${itemPhanAnhData['NoiDung']}" thì cơ sở vi phạm về An toàn thực phẩm và bị xử lý theo quyết định số:....., ngày: ${controller.ngayXuLy.string}';
                  break;
              }
            },
            itemBuilder: (BuildContext context) {
              return [
                if (itemPhanAnhData['TrangThai'] ==
                    controller.choTiepNhan.value)
                  const PopupMenuItem<String>(
                    value: 'item_1',
                    child: Row(
                      children: [
                        Icon(
                          Icons.edit_square,
                          color: AppColors.orange,
                        ),
                        TextWidget(
                          text: 'Tiếp nhận phản ánh',
                          size: AppDimens.textSize14,
                          fontWeight: FontWeight.w500,
                        ),
                      ],
                    ),
                  ),
                if (itemPhanAnhData['TrangThai'] == controller.dangXuLy.value)
                  const PopupMenuItem<String>(
                    value: 'item_2',
                    child: Row(
                      children: [
                        Icon(
                          Icons.check,
                          color: AppColors.green1,
                        ),
                        TextWidget(
                          text: 'Nhập kết quả xử lý phản ánh',
                          size: AppDimens.textSize14,
                          fontWeight: FontWeight.w500,
                        ),
                      ],
                    ),
                  ),
              ];
            },
          ),
        ],
      ),
      body: Loading.LoadingFullScreen(
        isLoading: controller.isLoading,
        body: Obx(() => _buildBody(context)),
      ),
      // ,
    );
  }

  Widget _buildBody(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          // ElevatedButton(
          //     onPressed: () => {
          //           log(
          //               convertImageStringToList(
          //                       ${controller.userAccessModel?.siteURL},
          //                       itemPhanAnhData['DinhKem'].toString())
          //                   .toString(),
          //               name: 'akr')
          //         },
          //     child: Text('data')),
          Column(
            children: [
              itemPhanAnhData["DinhKem_PA"] != null &&
                      itemPhanAnhData['DinhKem_PA'].toString().isNotEmpty
                  ? Container(
                      margin: const EdgeInsets.all(10),
                      child: Stack(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                                border: Border.all(
                                    color: AppColors.ConHieuLucTren6Thang),
                                borderRadius:
                                    const BorderRadius.all(Radius.circular(5))),
                            margin: const EdgeInsets.only(top: 15),
                            child: Container(
                                padding: const EdgeInsets.all(20),
                                child: MediaCarousel(
                                  mediaUrls:
                                      convertAttachItemStringToListWithoutSwungDash(
                                          '${controller.userAccessModel?.siteURL}',
                                          itemPhanAnhData['DinhKem_PA']
                                              .toString()),
                                  isNavigator: false,
                                  styleBottom: 1,
                                )),
                          ),
                          Container(
                            margin: const EdgeInsets.only(left: 10),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: Colors.green,
                              borderRadius: BorderRadius.circular(5),
                            ),
                            child: const Text(
                              "Hình ảnh khiếu nại, tố cáo",
                              style: TextStyle(color: Colors.white),
                            ),
                          ),
                        ],
                      ),
                    )
                  : const SizedBox.shrink(),
              Container(
                margin: const EdgeInsets.all(10),
                child: Stack(
                  children: [
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                          border:
                              Border.all(color: AppColors.ConHieuLucTren6Thang),
                          borderRadius:
                              const BorderRadius.all(Radius.circular(5))),
                      margin: const EdgeInsets.only(top: 15),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Text(itemPhanAnhData.toString()),
                            const SizedBox(height: 10),
                            rowText(
                                "Mã số:",
                                itemPhanAnhData['MaPAATTP'] ??
                                    'Không có thông tin'),
                            rowText(
                                "Số điện thoại:",
                                itemPhanAnhData['SoDienThoai'] ??
                                    'Không có thông tin'),
                            rowText(
                                "Tên cơ sở sản xuất, kinh doanh:",
                                itemPhanAnhData['TenCoSoSXKD'] ??
                                    'Không có thông tin'),
                            rowText(
                                "Chủ cơ sở:",
                                itemPhanAnhData['NguoiDaiDien'] ??
                                    'Không có thông tin'),
                            rowText("Địa chỉ:",
                                itemPhanAnhData['DiaChi'].toString()),
                            rowText(
                                "Tiêu đề:",
                                itemPhanAnhData['TieuDe'] ??
                                    'Không có thông tin'),
                            rowText(
                                "Nội dung khiếu nại, tố cáo:",
                                itemPhanAnhData['NoiDung'] ??
                                    'Không có thông tin'),
                          ],
                        ),
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(left: 10),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.green,
                        borderRadius: BorderRadius.circular(5),
                      ),
                      child: const Text(
                        "Thông tin khiếu nại, tố cáo từ người dân",
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                margin: const EdgeInsets.all(10),
                child: Stack(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                          border:
                              Border.all(color: AppColors.ConHieuLucTren6Thang),
                          borderRadius:
                              const BorderRadius.all(Radius.circular(5))),
                      margin: const EdgeInsets.only(top: 15),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 10),
                            const Row(
                              spacing: 10,
                              children: [
                                TextWidget(
                                  text: 'Thông tin cơ sở sản xuất, kinh doanh',
                                  size: AppDimens.textSize14,
                                  color: AppColors.green1,
                                  fontWeight: FontWeight.w600,
                                ),
                                Expanded(
                                    child: Divider(
                                  thickness: 0.5,
                                  color: AppColors.green1,
                                )),
                              ],
                            ),
                            const SizedBox(height: 10),
                            rowText(
                                "Cơ sở sản xuất, kinh doanh: ",
                                itemPhanAnhData.isNotEmpty
                                    ? " ${itemPhanAnhData['MaCoSoSXKD'] ?? 'Không có thông tin'} - ${itemPhanAnhData['TenCoSoSXKD'] ?? 'Không có thông tin'}"
                                    : controller.defaultValue.value),
                            rowText(
                                "Số GPKD:",
                                itemPhanAnhData['SoGPKD'] ??
                                    controller.defaultValue.value),
                            rowText(
                                "Cơ quan cấp:",
                                itemPhanAnhData['CoQuanCapGPKD'] ??
                                    controller.defaultValue.value),
                            rowText(
                                "Số điện thoại:",
                                itemPhanAnhData['SoDienThoai_CS'] ??
                                    controller.defaultValue.value),
                            rowText(
                                "Địa chỉ:",
                                itemPhanAnhData['DiaChiCS'] ??
                                    controller.defaultValue.value),
                            rowText(
                                "Người đại diện:",
                                itemPhanAnhData['NguoiDaiDien'] ??
                                    controller.defaultValue.value),
                            rowText(
                                "Ngày cấp:",
                                itemPhanAnhData['NgayCapGCN'] ??
                                    controller.defaultValue.value),
                            rowText(
                                "Ngày hết hiệu lực:",
                                itemPhanAnhData['NgayHetHanGCN'] ??
                                    controller.defaultValue.value),
                            const SizedBox(height: 10),
                            const Row(
                              spacing: 20,
                              children: [
                                TextWidget(
                                  text: 'Kết quả xử lý',
                                  size: AppDimens.textSize14,
                                  color: AppColors.green1,
                                  fontWeight: FontWeight.w600,
                                ),
                                Expanded(
                                    child: Divider(
                                  thickness: 0.5,
                                  color: AppColors.green1,
                                )),
                              ],
                            ),
                            const SizedBox(height: 10),
                            rowText(
                                "Người xử lý:",
                                itemPhanAnhData["TenNhanVien_XuLy_PH"] !=
                                            null &&
                                        itemPhanAnhData["TenNhanVien_XuLy_PH"]
                                            .toString()
                                            .isNotEmpty
                                    ? itemPhanAnhData["TenNhanVien_XuLy_PH"]
                                    : itemPhanAnhData["TenNhanVien_XuLy_PA"] ??
                                        controller.defaultValue.value),
                            rowText(
                                "Chức vụ:",
                                itemPhanAnhData["TenChucVu_XuLy_PH"] != null &&
                                        itemPhanAnhData["TenChucVu_XuLy_PH"]
                                            .toString()
                                            .isNotEmpty
                                    ? itemPhanAnhData["TenChucVu_XuLy_PH"]
                                    : itemPhanAnhData["TenChucVu_XuLy_PA"] ??
                                        controller.defaultValue.value),
                            rowText(
                                "Ngày xử lý:",
                                itemPhanAnhData["NgayXuLy_PH"] != null &&
                                        itemPhanAnhData["NgayXuLy_PH"]
                                            .toString()
                                            .isNotEmpty
                                    ? itemPhanAnhData["NgayXuLy_PH"]
                                    : itemPhanAnhData["NgayXuLy_PA"] ??
                                        controller.defaultValue.value),
                            const TextWidget(
                              text: "Nội dung vi phạm: ",
                              color: Colors.black,
                              size: AppDimens.textSize14,
                            ),
                            itemPhanAnhData["NoiDung_XuLy"]
                                    .toString()
                                    .isNotEmpty
                                ? Padding(
                                    padding: const EdgeInsets.only(left: 10),
                                    child: HtmlWidget(itemPhanAnhData[
                                                    "NoiDung_PH"] !=
                                                null &&
                                            itemPhanAnhData["NoiDung_PH"]
                                                .toString()
                                                .isNotEmpty
                                        ? itemPhanAnhData["NoiDung_PH"]
                                        : itemPhanAnhData["NoiDung_XuLy_PA"] ??
                                            '''<h4>${controller.defaultValue.value}</h4>'''),
                                  )
                                : const SizedBox.shrink(),
                            rowText(
                                "Hình thức xử lý:",
                                itemPhanAnhData["TenHinhThucXuLy"] ??
                                    controller.defaultValue.value),
                            Row(
                              children: [
                                const TextWidget(
                                  text: "Đính kèm: ",
                                  size: AppDimens.textSize15,
                                  fontWeight: FontWeight.w400,
                                  color: Colors.black,
                                ),
                                itemPhanAnhData["NgayXuLy_PH"] != null &&
                                        itemPhanAnhData["NgayXuLy_PH"]
                                            .toString()
                                            .isNotEmpty
                                    ? GestureDetector(
                                        onTap: () {
                                          log(convertAttachItemStringToListWithoutSwungDash(
                                                  '${controller.userAccessModel?.siteURL}',
                                                  itemPhanAnhData[
                                                      "DinhKem_PH"]!)
                                              .first);
                                          Get.to(() => WebViewPage(
                                              title: AppContent.appTitleWebview,
                                              initialUrl:
                                                  convertAttachItemStringToListWithoutSwungDash(
                                                          '${controller.userAccessModel?.siteURL}',
                                                          itemPhanAnhData[
                                                              "DinhKem_PH"]!)
                                                      .first));
                                        },
                                        child: TextWidget(
                                          text: 'Xem đính kèm ',
                                          color: AppColors.blue,
                                          fontStyle: FontStyle.italic,
                                        ),
                                      )
                                    : const TextWidget(
                                        text: "Không có đính kèm",
                                        size: AppDimens.textSize14,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.black,
                                      )
                              ],
                            )
                          ],
                        ),
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(left: 10),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.green,
                        borderRadius: BorderRadius.circular(5),
                      ),
                      child: const Text(
                        "Kết quả xử lý phản ánh",
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
