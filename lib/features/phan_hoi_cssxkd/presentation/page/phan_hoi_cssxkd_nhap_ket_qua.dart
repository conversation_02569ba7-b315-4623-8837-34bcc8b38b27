// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:attp_2024/core/ui/widgets/input/custom_check_box.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/core/utils/file_picker.dart';
import 'package:attp_2024/features/phan_hoi_cssxkd/presentation/controller/phan_hoi_cssxkd_controller.dart';
import '../../../../core/configs/theme/app_colors.dart';
import '../../../../core/ui/widgets/CustomDatePicker/CustomDatePicker.dart';
import '../../../../core/ui/widgets/button/button_widget.dart';
import '../../../../core/ui/widgets/custom_combo/combo.dart';
import '../../../../core/ui/widgets/custom_textfield/widgets/custom_textarea.dart';

class PhanHoiCssxkdNhapKetQua extends GetView<PhanHoiCssxkdController> {
  final Map<String, dynamic> itemPhanAnhData;

  const PhanHoiCssxkdNhapKetQua({
    super.key,
    required this.itemPhanAnhData,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppBarWidget(
        title: "Nhập kết quả xử lý phản ánh ATTP",
      ),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return SafeArea(
        child: SingleChildScrollView(
      padding: EdgeInsets.all(4.w),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(10.0),
            decoration: BoxDecoration(
                border: Border.all(width: 0.5),
                color: AppColors.white,
                borderRadius: const BorderRadius.all(
                  Radius.circular(5.0),
                )),
            child: TextWidget(
              text:
                  "Bạn đang thực hiện nhập kết quả phản ánh của người dân, tổ chức về an toàn thực phẩm, người gửi: ${itemPhanAnhData['HoVaTen']} với nội dung: ${itemPhanAnhData['NoiDung']}. Vui lòng điền đầy đủ các thông tin bên dưới và nhấn vào nút “Nhập kết quả” để thực hiện thao tác.",
              color: AppColors.customColor1,
              size: AppDimens.textSize13,
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          CustomDatePicker(
            // isMaximumDate: true,
            isRequired: true,
            title: 'Ngày nhập kết quả',
            placeholder: 'dd-MM-yyyy',
            date: controller.ngayXuLy.value,
            // Ngày mặc định
            setTime: controller.ngayXuLy.value,
            onChange: (DateTime? newDate) {
              print('Ngày đã chọn: $newDate');
              controller.ngayXuLy.value = newDate!;
            },
            showClearButton: false,
          ),
          const SizedBox(
            height: 20,
          ),
          Obx(
            () => CustomCombobox(
              title: 'Quyết định xử lý vi phạm ATTP',
              weight: MediaQuery.of(context).size.width,
              defaultSelectedItem: controller.viPhamListData
                  .map((e) => DropdownModel(
                        id: e['ViPhamATTPID'],
                        display: e['NoiDung'],
                      ))
                  .firstWhere(
                    (item) => item.id == controller.viPhamATTPID.value,
                    orElse: () => DropdownModel(id: null, display: ''),
                  ),
              dropDownList: controller.viPhamListData
                  .map((e) => DropdownModel(
                        id: e['ViPhamATTPID'],
                        display: e['NoiDung'],
                      ))
                  .toList(),
              onChange: (selectedItem) {
                if (selectedItem != null) {
                  controller.viPhamATTPID.value = selectedItem.id.toString();
                  controller.viPhamErr.value = '';
                }
                log(controller.viPhamATTPID.value, name: 'Đã chọn quyết định:');
              },
              delete: () {
                controller.viPhamATTPID.value = '';
                log(controller.viPhamATTPID.value, name: 'Đã chọn quyết định:');
              },
              errorText: controller.viPhamErr.value,
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Obx(
                  () => CustomCombobox(
                    title: 'Người xử lý',
                    weight: 45.w,
                    defaultSelectedItem: controller.nhanVienListData
                        .map((e) => DropdownModel(
                              id: e['NhanVienID'],
                              display: e['TenNhanVien'],
                            ))
                        .firstWhere(
                          (item) => item.id == controller.nhanVienID.value,
                          orElse: () => DropdownModel(id: null, display: ''),
                        ),
                    dropDownList: controller.nhanVienListData
                        .map((e) => DropdownModel(
                              id: e['NhanVienID'],
                              display: e['TenNhanVien'],
                            ))
                        .toList(),
                    onChange: (selectedItem) {
                      if (selectedItem != null) {
                        controller.nhanVienID.value =
                            selectedItem.id.toString();
                      }
                      controller.nhanVienErr.value = '';
                      // controller.cssxkdID.value.isEmpty
                      //     ? controller.errCssxkd.value =
                      //         'Tên cở sở không được để trống !'
                      //     : controller.errCssxkd.value = '';
                    },
                    delete: () {
                      controller.nhanVienID.value = '';
                    },
                    errorText: controller.nhanVienErr.value,
                  ),
                ),
              ),
              Expanded(
                child: Obx(
                  () => CustomCombobox(
                    title: 'Chức vụ',
                    weight: 45.w,
                    defaultSelectedItem: controller.chucVuListData
                        .map((e) => DropdownModel(
                              id: e['ChucVuID'],
                              display: e['TenChucVu'],
                            ))
                        .firstWhere(
                          (item) => item.id == controller.chucVuID.value,
                          orElse: () => DropdownModel(id: null, display: ''),
                        ),
                    dropDownList: controller.chucVuListData
                        .map((e) => DropdownModel(
                              id: e['ChucVuID'],
                              display: e['TenChucVu'],
                            ))
                        .toList(),
                    onChange: (selectedItem) {
                      if (selectedItem != null) {
                        controller.chucVuID.value = selectedItem.id.toString();
                      }
                      controller.chucVuErr.value = '';
                    },
                    delete: () {
                      controller.chucVuID.value = '';
                    },
                    errorText: controller.chucVuErr.value,
                  ),
                ),
              ),
            ],
          ),
          Obx(
            () => TextAreaWidget(
              title: 'Kết quả xử lý',
              placeholder: 'Nhập nội dung',
              initialValue: controller
                  .noiDung.value, // errorWidget: controller.errNoiDung.value,
              onChange: (e) {
                controller.noiDung.value = e;
                controller.noiDung.value.isEmpty
                    ? controller.noiDungErr.value =
                        'Nội dung không được để trống !'
                    : controller.noiDungErr.value = '';
              },
              errorWidget: controller.noiDungErr.value,
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(spacing: 2.w, children: [
                CustomCheckBox(
                  checkValue: controller.congKhaiKetQuaIsChecked,
                ),
                const TextWidget(
                  text: "Công khai kết quả ra cổng thông tin.",
                  color: AppColors.error,
                  size: AppDimens.textSize13,
                ),
              ]),
              GestureDetector(
                onTap: () {
                  if (controller.selectedFiles.isNotEmpty) {
                    return;
                  }
                  pickImagesAndPdf(
                      maxFiles: 1,
                      onFilesPicked: (files) {
                        controller.addFiles(files);
                      });
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    const Icon(
                      Icons.edit_document,
                      color: AppColors.blue,
                    ),
                    Obx(
                      () => TextWidget(
                        text: 'Đính kèm (${controller.selectedFiles.length}/1)',
                        color: AppColors.blue,
                        fontStyle: FontStyle.italic,
                        size: AppDimens.textSize15,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          Obx(() {
            if (controller.selectedFiles.isEmpty) {
              return const SizedBox(); // Không hiển thị gì nếu danh sách rỗng
            }
            return SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: controller.selectedFiles.map((file) {
                  final fileName = file.split('/').last; // Lấy tên file
                  final isImage =
                      fileName.endsWith('.jpg') || fileName.endsWith('.png');
                  final isVideo = fileName.endsWith('.mp4');
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 5.0),
                    child: Column(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.primary),
                            borderRadius: BorderRadius.circular(5),
                          ),
                          child: Icon(
                            isImage
                                ? Icons.image
                                : isVideo
                                    ? Icons.videocam
                                    : Icons.insert_drive_file,
                            size: 40,
                            color: Colors.grey,
                          ),
                        ),
                        // Text(
                        //   fileName,
                        //   style: const TextStyle(fontSize: 12),
                        //   overflow: TextOverflow.ellipsis,
                        // ),
                        IconButton(
                          icon: const Icon(Icons.close,
                              size: 16, color: Colors.red),
                          onPressed: () {
                            controller.removeFile(file);
                          },
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
            );
          }),
          SizedBox(
            height: 45,
            child: ButtonWidget(
              ontap: () {
                if (controller.viPhamATTPID.value.isEmpty) {
                  controller.viPhamErr.value =
                      'Vui lòng chọn Quyết định xử lý vi phạm !';
                  return;
                }
                if (controller.nhanVienID.value.isEmpty) {
                  controller.nhanVienErr.value = "Vui lòng chọn người xử lý !";
                  return;
                }
                if (controller.chucVuID.value.isEmpty) {
                  controller.chucVuErr.value = 'Vui lòng chọn chức vụ !';
                  return;
                }
                if (controller.noiDung.value.isEmpty) {
                  controller.noiDungErr.value =
                      'Vui lòng nhập nội dung xử lý !';
                  return;
                }
                controller.handleCapNhatThongTinPAATTPPhanHoi(
                    context, itemPhanAnhData);
              },
              text: "Nhập kết quả",
              backgroundColor: AppColors.primary,
              borderRadius: 7,
            ),
          )
        ],
      ),
    ));
  }
}
