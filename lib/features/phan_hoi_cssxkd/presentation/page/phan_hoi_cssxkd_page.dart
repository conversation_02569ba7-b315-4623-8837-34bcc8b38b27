import 'dart:developer';

import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/ui/widgets/tabbar/tabbar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:attp_2024/core/ui/widgets/load/loading.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/features/phan_hoi_cssxkd/presentation/controller/phan_hoi_cssxkd_controller.dart';
import 'package:attp_2024/features/phan_hoi_cssxkd/presentation/page/phan_hoi_cssxkd_detail.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class PhanHoiCssxkdPage extends GetView<PhanHoiCssxkdController> {
  const PhanHoiCssxkdPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppBarWidget(
        title: "Phản hồi phản ánh ATTP",
      ),
      body: _appbar(context),
    );
  }

  Widget _appbar(BuildContext context) {
    return TabBarWidget(
      tabs: const ['Chờ tiếp nhận', 'Đang xử lý', 'Đã xử lý', 'Từ chối'],
      tabMinWidth: 90.0, // Optional: minimum width for each tab
      tabPadding: 16.0, // Optional: horizontal padding for each tab
      fontSize: AppDimens.defaultText,
      onChangeTab: (index) {
        controller.phanAnhItemData.clear();
        controller.phanAnhItemData.refresh();
        switch (index) {
          case 0:
            controller.fetchAllPAATTPByTrangThai(controller.choTiepNhan.value);
            break;
          case 1:
            controller.fetchAllPAATTPByTrangThai(controller.dangXuLy.value);
            break;
          case 2:
            controller.fetchAllPAATTPByTrangThai(controller.daXuLy.value);
            break;
          case 3:
            controller.fetchAllPAATTPByTrangThai(controller.tuChoi.value);
            break;
        }
      },
      children: [
        _buildTabView(context),
        _buildTabView(context),
        _buildTabView(context),
        _buildTabView(context),
      ],
    );
  }

  _buildTabView(BuildContext context) {
    return Stack(
      children: [
        Container(
            decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage("assets/images/background_profile.png"),
            fit: BoxFit.cover,
          ),
        )),
        Obx(() => _buildBody(context)),
      ],
    );
  }

  // ignore: non_constant_identifier_names
  Widget _buildBody(BuildContext context) {
    return Loading.LoadingFullScreen(
        isLoading: controller.isLoading,
        body: ListView.builder(
          itemCount: controller.phanAnhItemData.length,
          itemBuilder: (context, index) {
            return itemPhanAnh(controller.phanAnhItemData[index]);
          },
        ));
  }

  Widget itemPhanAnh(Map<String, dynamic> itemData) {
    return GestureDetector(
      onTap: () async {
        Get.to(PhanHoiCssxkdDetail(itemPhanAnhData: itemData));
      },
      child: Container(
        margin: const EdgeInsets.fromLTRB(8, 8, 8, 0),
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: const Color.fromARGB(155, 255, 255, 255),
          border: Border.all(color: AppColors.ConHieuLucTren6Thang),
          borderRadius: const BorderRadius.all(Radius.circular(5)),
        ),
        child: IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    height: 10.h,
                    child: Image.asset(
                      AppImageString.phanAnhIcon,
                      fit: BoxFit.cover,
                      width: 10.h, // Chiều rộng
                    ),
                  ),
                  const SizedBox(height: 8), // Added spacing
                  Builder(
                    builder: (context) {
                      final trangThai = itemData['TrangThai_XuLy_PH'] ??
                          itemData['TrangThai'];
                      String text;
                      Color backgroundColor;

                      switch (trangThai) {
                        case '80':
                          text = 'Đã gửi, chờ tiếp nhận';
                          backgroundColor = AppColors.customColor1;
                          break;
                        case '81':
                          text = 'Đã tiếp nhận, đang xử lý';
                          backgroundColor = AppColors.blue;
                          break;
                        case '82':
                          text = 'Đã tiếp nhận, đã xử lý';
                          backgroundColor = AppColors.activeColors;
                          break;
                        case '83':
                          text = 'Từ chối tiếp nhận';
                          backgroundColor = AppColors.grey;
                          break;
                        default:
                          text = 'Trạng thái không xác định';
                          backgroundColor = Colors.grey;
                      }

                      return Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 7.0, vertical: 4.0),
                        decoration: BoxDecoration(
                          color: backgroundColor,
                          borderRadius:
                              const BorderRadius.all(Radius.circular(4.0)),
                        ),
                        child: TextWidget(
                          text: text,
                          color: AppColors.white,
                          fontWeight: FontWeight.w600,
                          size: AppDimens.textSize11,
                        ),
                      );
                    },
                  ),
                ],
              ),
              const SizedBox(width: 12), // Added consistent spacing
              // Right column with details - wrapped in Expanded
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildInfoRow(
                        'Người gửi:', itemData["HoVaTen"] ?? "Không rõ"),
                    _buildInfoRow(
                        'Ngày gửi:', itemData["NgayGui"] ?? "Không rõ"),
                    _buildInfoRow(
                        'CSSXKD:', itemData["TenCoSoSXKD"] ?? "Không rõ"),
                    _buildInfoRow('Địa chỉ:', itemData["DiaChi"] ?? "Không rõ"),
                    _buildInfoRow('Người xử lý:',
                        itemData["TenNhanVien_XuLy_PH"] ?? "Chưa xử lý"),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Container(
      width: double.infinity, // Ensure full width
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.grey,
            width: 1.0,
          ),
        ),
      ),
      padding: const EdgeInsets.symmetric(
          vertical: 4), // Added consistent vertical spacing
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start, // Align text to top
        children: [
          TextWidget(
            text: label,
            color: AppColors.black,
            size: AppDimens.textSize12,
          ),
          Expanded(
            // Wrap value in Expanded to handle long text
            child: TextWidget(
              text: value,
              color: AppColors.black,
              fontWeight: FontWeight.w600,
              size: AppDimens.textSize12,
              maxLines: 2, // Allow up to 2 lines for long text
            ),
          ),
        ],
      ),
    );
  }
}
