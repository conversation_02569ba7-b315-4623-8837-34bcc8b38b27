import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class BeautifulBarChart extends StatelessWidget {
  final List<String> categories = [
    "Phường 1",
    "Phường 2",
    "Phường 3",
    "Ph<PERSON>ờng 5",
    "Ph<PERSON>ờng 7",
    "<PERSON><PERSON> Long Đức"
  ];

  final List<double> jobData = [58, 80, 60, 85, 42, 54];
  final List<double> unemployedData = [25, 35, 31, 15, 54, 17];
  final List<double> notParticipatingData = [80, 96, 77, 94, 70, 68];

  @override
  Widget build(BuildContext context) {
    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: 100,
        barTouchData: BarTouchData(enabled: false),
        titlesData: FlTitlesData(
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              interval: 20,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 12,
                  ),
                );
              },
            ),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (double value, TitleMeta meta) {
                return SideTitleWidget(
                  axisSide: meta.axisSide,
                  child: Text(
                    categories[value.toInt()],
                    style: TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                );
              },
            ),
          ),
        ),
        gridData: FlGridData(show: true),
        borderData: FlBorderData(show: true),
        barGroups: _buildBarGroups(),
      ),
    );
  }

  List<BarChartGroupData> _buildBarGroups() {
    return List.generate(categories.length, (index) {
      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            borderRadius: BorderRadius.zero,
            toY: jobData[index],
            color: Colors.purple,
            width: 10,
          ),
          BarChartRodData(
            borderRadius: BorderRadius.zero,
            toY: unemployedData[index],
            color: Colors.redAccent,
            width: 10,
          ),
          BarChartRodData(
            borderRadius: BorderRadius.zero,
            toY: notParticipatingData[index],
            color: Colors.lightBlue,
            width: 10,
          ),
        ],
        barsSpace: 4,
      );
    });
  }
}
