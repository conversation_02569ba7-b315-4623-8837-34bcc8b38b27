import 'package:flutter/material.dart';

class CustomDropdown extends StatefulWidget {
  final String? headerTitle;
  final List<String> options;
  final String initialSelectedOption;

  // Customization parameters
  final Color? headerBackgroundColor;
  final Color? dropdownBackgroundColor;
  final Color? borderColor;
  final double? borderWidth;
  final TextStyle? headerTextStyle;
  final TextStyle? selectedOptionTextStyle;
  final TextStyle? optionTextStyle;
  final double elevation;
  final BorderRadius? borderRadius;
  final double? paddingHorizontal;
  final double? paddingVertical;
  final IconData? dropdownIcon;
  final Color? dropdownIconColor;
  final double? dropdownMaxHeight;
  final Curve dropdownAnimationCurve;

  const CustomDropdown({
    Key? key,
    this.headerTitle,
    required this.options,
    required this.initialSelectedOption,
    this.headerBackgroundColor,
    this.dropdownBackgroundColor,
    this.borderColor,
    this.borderWidth,
    this.headerTextStyle,
    this.selectedOptionTextStyle,
    this.optionTextStyle,
    this.elevation = 3,
    this.borderRadius,
    this.paddingHorizontal,
    this.paddingVertical,
    this.dropdownIcon,
    this.dropdownIconColor,
    this.dropdownMaxHeight,
    this.dropdownAnimationCurve = Curves.easeInOut,
  }) : super(key: key);

  @override
  _CustomDropdownState createState() => _CustomDropdownState();
}

class _CustomDropdownState extends State<CustomDropdown> {
  late String _selectedOption;
  bool _isExpanded = false;
  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();
    _selectedOption = widget.initialSelectedOption;
  }

  @override
  void dispose() {
    _removeOverlay();
    super.dispose();
  }

  void _toggleDropdown() {
    if (_isExpanded) {
      _removeOverlay();
    } else {
      _showOverlay();
    }
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  void _showOverlay() {
    final overlay = Overlay.of(context);
    _overlayEntry = _createOverlayEntry();
    overlay?.insert(_overlayEntry!);
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  OverlayEntry _createOverlayEntry() {
    final renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);

    return OverlayEntry(
      builder: (context) => Positioned(
        left: offset.dx,
        top: offset.dy + size.height,
        width: size.width,
        child: Material(
          color: Colors.transparent,
          child: Card(
            margin: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
            color: widget.dropdownBackgroundColor ?? Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
              side: widget.borderColor != null && widget.borderWidth != null
                  ? BorderSide(
                      color: widget.borderColor!,
                      width: widget.borderWidth!,
                    )
                  : BorderSide.none,
            ),
            elevation: widget.elevation,
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxHeight: widget.dropdownMaxHeight ?? 300,
              ),
              child: SingleChildScrollView(
                child: Column(
                  children: widget.options.map((option) {
                    return InkWell(
                      onTap: () {
                        _selectOption(option);
                        _toggleDropdown();
                      },
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                          vertical: widget.paddingVertical ?? 8.0,
                          horizontal: widget.paddingHorizontal ?? 12.0,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              option,
                              style: option == _selectedOption
                                  ? widget.selectedOptionTextStyle ??
                                      TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.blue,
                                      )
                                  : widget.optionTextStyle ??
                                      TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.normal,
                                        color: Colors.black,
                                      ),
                            ),
                            if (option == _selectedOption)
                              Icon(Icons.check, color: Colors.blue),
                          ],
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _selectOption(String option) {
    setState(() {
      _selectedOption = option;
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _toggleDropdown,
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
        color: widget.headerBackgroundColor ?? Colors.white.withOpacity(0.5),
        shape: RoundedRectangleBorder(
          borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
          side: widget.borderColor != null && widget.borderWidth != null
              ? BorderSide(
                  color: widget.borderColor!,
                  width: widget.borderWidth!,
                )
              : BorderSide.none,
        ),
        elevation: widget.elevation,
        child: Padding(
          padding: EdgeInsets.all(widget.paddingHorizontal ?? 12.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (widget.headerTitle?.isNotEmpty ?? false)
                    Text(
                      widget.headerTitle!,
                      style: widget.headerTextStyle ??
                          TextStyle(
                            fontWeight: FontWeight.normal,
                            color: Colors.white,
                          ),
                    ),
                  SizedBox(height: 5),
                  Text(
                    _selectedOption,
                    style: widget.selectedOptionTextStyle ??
                        TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                  ),
                ],
              ),
              Icon(
                widget.dropdownIcon ?? Icons.arrow_drop_down_circle_rounded,
                color: widget.dropdownIconColor ?? Colors.white,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CustomDropdown2 extends StatefulWidget {
  final String? headerTitle;
  final List<String> options;
  final String initialSelectedOption;

  // Customization parameters
  final Color? headerBackgroundColor;
  final Color? dropdownBackgroundColor;
  final Color? borderColor;
  final double? borderWidth;
  final TextStyle? headerTextStyle;
  final TextStyle? selectedOptionTextStyle;
  final TextStyle? optionTextStyle;
  final double elevation;
  final BorderRadius? borderRadius;
  final double? paddingHorizontal;
  final double? paddingVertical;
  final IconData? dropdownIcon;
  final Color? dropdownIconColor;
  final double? dropdownMaxHeight;
  final Curve dropdownAnimationCurve;

  final double? height;
  final double? witdh;

  const CustomDropdown2({
    Key? key,
    this.headerTitle,
    this.height,
    this.witdh,
    required this.options,
    required this.initialSelectedOption,
    this.headerBackgroundColor,
    this.dropdownBackgroundColor,
    this.borderColor,
    this.borderWidth,
    this.headerTextStyle,
    this.selectedOptionTextStyle,
    this.optionTextStyle,
    this.elevation = 3,
    this.borderRadius,
    this.paddingHorizontal,
    this.paddingVertical,
    this.dropdownIcon,
    this.dropdownIconColor,
    this.dropdownMaxHeight,
    this.dropdownAnimationCurve = Curves.easeInOut,
  }) : super(key: key);

  @override
  _CustomDropdownState2 createState() => _CustomDropdownState2();
}

class _CustomDropdownState2 extends State<CustomDropdown2> {
  late String _selectedOption;
  bool _isExpanded = false;
  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();
    _selectedOption = widget.initialSelectedOption;
  }

  @override
  void dispose() {
    _removeOverlay();
    super.dispose();
  }

  void _toggleDropdown() {
    if (_isExpanded) {
      _removeOverlay();
    } else {
      _showOverlay();
    }
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  void _showOverlay() {
    final overlay = Overlay.of(context);
    _overlayEntry = _createOverlayEntry();
    overlay?.insert(_overlayEntry!);
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  OverlayEntry _createOverlayEntry() {
    final renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);

    return OverlayEntry(
      builder: (context) => Positioned(
        left: offset.dx,
        top: offset.dy + size.height,
        width: widget.witdh,
        height: widget.height,
        child: Material(
          color: Colors.transparent,
          child: Card(
            margin: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
            color: widget.dropdownBackgroundColor ?? Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
              side: widget.borderColor != null && widget.borderWidth != null
                  ? BorderSide(
                      color: widget.borderColor!,
                      width: widget.borderWidth!,
                    )
                  : BorderSide.none,
            ),
            elevation: widget.elevation,
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxHeight: widget.dropdownMaxHeight ?? 300,
              ),
              child: SingleChildScrollView(
                child: Column(
                  children: widget.options.map((option) {
                    return InkWell(
                      onTap: () {
                        _selectOption(option);
                        _toggleDropdown();
                      },
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                          vertical: widget.paddingVertical ?? 8.0,
                          horizontal: widget.paddingHorizontal ?? 12.0,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              option,
                              style: option == _selectedOption
                                  ? widget.selectedOptionTextStyle ??
                                      TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.blue,
                                      )
                                  : widget.optionTextStyle ??
                                      TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.normal,
                                        color: Colors.black,
                                      ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            if (option == _selectedOption)
                              Icon(Icons.check, color: Colors.blue),
                          ],
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _selectOption(String option) {
    setState(() {
      _selectedOption = option;
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _toggleDropdown,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.headerTitle?.isNotEmpty ?? false)
                Text(
                  widget.headerTitle!,
                  style: widget.headerTextStyle ??
                      TextStyle(
                        fontWeight: FontWeight.normal,
                        color: Colors.white,
                      ),
                ),
              SizedBox(height: 5),
              Text(
                _selectedOption,
                style: widget.selectedOptionTextStyle ??
                    TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
              ),
            ],
          ),
          Icon(
            widget.dropdownIcon ?? Icons.arrow_drop_down_circle_rounded,
            color: widget.dropdownIconColor ?? Colors.black,
          ),
        ],
      ),
    );
  }
}
