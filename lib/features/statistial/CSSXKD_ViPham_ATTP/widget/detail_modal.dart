import 'package:attp_2024/core/configs/contents/app_content.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../../core/configs/theme/app_colors.dart';
import '../presentation/controller/CSSXKD_ViPham_controller.dart';

class DetailModal extends GetView<CSSXKDViPhamController> {
  final String label;
  final Map<String, List<dynamic>> groupedData;

  const DetailModal({
    super.key,
    required this.label,
    required this.groupedData,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.87,
      padding: const EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Divider(
            color: Colors.grey.withOpacity(0.3),
            thickness: 3.5,
            indent: 150,
            endIndent: 150,
          ),
          Row(
            children: [
              const Spacer(),
              const Spacer(),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Địa bàn: $label',
                    style: TextStyle(
                      fontSize: AppDimens.mediumText,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryFocus,
                    ),
                  ),
                ],
              ),
              const Spacer(),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  IconButton(
                    icon: const Icon(
                      Icons.clear,
                      color: Colors.red,
                    ),
                    onPressed: () => Get.back(),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: DefaultTabController(
              length: 5,
              child: Column(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: TabBar(
                      tabs: const [
                        Tab(text: 'Nhắc nhở, cảnh báo'),
                        Tab(text: 'Thu hồi giấy cam kết'),
                        Tab(text: 'Xử phạt hành chính'),
                        Tab(text: 'Thu hồi giấy chứng nhận'),
                        Tab(text: 'Hình thức khác'),
                      ],
                      labelColor: AppColors.primary,
                      unselectedLabelColor: Colors.grey,
                      indicatorColor: AppColors.primary,
                      indicatorSize: TabBarIndicatorSize.tab,
                      isScrollable: true,
                      labelStyle: TextStyle(fontSize: AppDimens.defaultText),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: TabBarView(
                      children: [
                        _buildTabContent(
                            groupedData['04'] ?? []), // Nhắc nhở, cảnh báo
                        _buildTabContent(
                            groupedData['02'] ?? []), // Thu hồi giấy cam kết
                        _buildTabContent(
                            groupedData['03'] ?? []), // Xử phạt hành chính
                        _buildTabContent(
                            groupedData['01'] ?? []), // Thu hồi giấy chứng nhận
                        _buildTabContent(
                            groupedData['05'] ?? []), // Hình thức khác
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabContent(List<dynamic> items) {
    if (items.isEmpty) {
      return const Center(
        child: Text(
          'Không có dữ liệu',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(8.0, 8.0, 8.0, 8.0),
          child: Text(
            'Tổng số cơ sở: ${items.length}',
            style: TextStyle(
              fontSize: AppDimens.defaultText,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(vertical: 8),
            itemCount: items.length,
            itemBuilder: (context, index) {
              final item = items[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item['TenCoSoSXKD'] ?? AppContent.textDefault,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: AppDimens.defaultText,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Địa chỉ: ${item['DiaChiCS'] ?? AppContent.textDefault}',
                        style: TextStyle(fontSize: AppDimens.subText),
                      ),
                      Text(
                        'Hình thức xử lý: ${item['TenHinhThucXuLy'] ?? AppContent.textDefault}',
                        style: TextStyle(fontSize: AppDimens.subText),
                      )
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  String _formatDate(dynamic date) {
    if (date == null) return 'Không có';
    try {
      return DateFormat('dd/MM/yyyy').format(DateTime.parse(date.toString()));
    } catch (e) {
      return date.toString();
    }
  }

  String _formatCurrency(dynamic amount) {
    if (amount == null) return '0 VNĐ';
    final formatter =
        NumberFormat.currency(locale: 'vi_VN', symbol: 'VNĐ', decimalDigits: 0);
    return formatter.format(amount);
  }
}
