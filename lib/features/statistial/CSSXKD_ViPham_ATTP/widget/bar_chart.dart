import 'package:flutter/material.dart';

class ScrollableHorizontalBarChart extends StatelessWidget {
  final List<Map<String, dynamic>> data;

  const ScrollableHorizontalBarChart({
    Key? key,
    required this.data,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.vertical,
            child: Column(
              children: data.map((item) {
                // Chuyển đổi giá trị "value" thành double
                double barWidth = (item["value"] as num).toDouble() * 40;
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 10.0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 50, // Độ rộng cố định cho nhãn năm
                        child: Text(item["year"]),
                      ),
                      Expanded(
                        child: Stack(
                          children: [
                            Container(
                              height: 30,
                              decoration: BoxDecoration(
                                color: Colors.green,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              width: barWidth, // Điều chỉnh độ dài theo giá trị
                            ),
                            Positioned(
                              right: 8,
                              child: Text(
                                item["percentage"],
                                style: TextStyle(color: Colors.white),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ),
        SizedBox(height: 10),
        // Trục X và nhãn Sale
        Column(
          children: [
            Row(
              children: [
                SizedBox(width: 50),
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: List.generate(8, (index) => Text("$index")),
                  ),
                ),
              ],
            ),
            Row(
              children: [
                SizedBox(width: 50),
                Expanded(
                  child: Divider(color: Colors.black),
                ),
              ],
            ),
            SizedBox(height: 10),
            Center(
              child: Text(
                "Sale",
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
