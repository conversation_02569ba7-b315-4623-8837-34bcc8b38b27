import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'indicator.dart'; // Import your custom Indicator widget

class PieChartDataModel {
  final String label;
  final double value;
  final Color color;

  PieChartDataModel(
      {required this.label, required this.value, required this.color});
}

class PieChartSample2 extends StatefulWidget {
  final List<PieChartDataModel> data;
  final String summary;
  final double? chartSize; // Kích thước Pie Chart
  final double? textSize; // <PERSON>ích thước của chữ trong Pie Chart

  const PieChartSample2({
    super.key,
    required this.data,
    required this.summary,
    this.chartSize,
    this.textSize,
  });

  @override
  State<StatefulWidget> createState() => PieChart2State();
}

class PieChart2State extends State<PieChartSample2> {
  int touchedIndex = -1;

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: widget.chartSize ?? 0.65, // Điều chỉnh aspectRatio còn 1/2
      child: Row(
        children: <Widget>[
          SizedBox(
            height: 50,
            width: 50,
            child: Stack(
              alignment: Alignment.center,
              children: [
                SizedBox(
                  child: AspectRatio(
                    aspectRatio: 1,
                    child: PieChart(
                      PieChartData(
                        pieTouchData: PieTouchData(
                          touchCallback:
                              (FlTouchEvent event, pieTouchResponse) {
                            setState(() {
                              if (!event.isInterestedForInteractions ||
                                  pieTouchResponse == null ||
                                  pieTouchResponse.touchedSection == null) {
                                touchedIndex = -1;
                                return;
                              }
                              touchedIndex = pieTouchResponse
                                  .touchedSection!.touchedSectionIndex;
                            });
                          },
                        ),
                        borderData: FlBorderData(show: false),
                        sectionsSpace: 1, // Giảm khoảng cách giữa các phần
                        centerSpaceRadius: 40, // Giảm kích thước khoảng giữa
                        sections: showingSections(),
                      ),
                    ),
                  ),
                ),
                // Thêm Text vào giữa Pie Chart
                Text(
                  widget.summary,
                  style: TextStyle(
                    fontSize: widget.textSize ?? 14, // Giảm kích thước text
                    fontWeight: FontWeight.bold,
                    color: Colors.black87, // Màu chữ trong Pie Chart
                  ),
                ),
              ],
            ),
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: widget.data.map((data) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 2.0),
                child: Row(
                  children: [
                    Indicator(
                      color: data.color,
                      text: data.label,
                      isSquare: true,
                      size: 12, // Giảm kích thước Indicator
                    ),
                    const SizedBox(
                        width: 4), // Giảm khoảng cách giữa Indicator và Text
                    Text(
                      (double.parse(widget.summary) *
                              data.value.toDouble() /
                              100)
                          .toStringAsFixed(0),
                      style: const TextStyle(
                          fontSize: 12, fontWeight: FontWeight.w600),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  List<PieChartSectionData> showingSections() {
    return List.generate(widget.data.length, (i) {
      final isTouched = i == touchedIndex;
      final fontSize = isTouched ? 16.0 : 12.0; // Giảm font khi nhấn vào
      final radius = isTouched ? 30.0 : 20.0; // Điều chỉnh bán kính nhỏ hơn
      const shadows = [Shadow(color: Colors.black, blurRadius: 1)];
      final data = widget.data[i];

      return PieChartSectionData(
        color: data.color,
        value: data.value,
        title: '${data.value.toInt()}%',
        radius: radius,
        titleStyle: TextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.bold,
          color: Colors.white,
          shadows: shadows,
        ),
      );
    });
  }
}
