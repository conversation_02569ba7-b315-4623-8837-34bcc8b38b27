import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/bindings_interface.dart';

import '../../../../core/data/api/configs/dio_configs.dart';
import '../../../../core/data/api/services/proc/proc_service.dart';
import '../presentation/controller/CSSXKD_ViPham_controller.dart';

class CSSXKDViPhamBinding extends Bindings {
  //final _procService = GetIt.I<ProcService>();
  @override
  void dependencies() {
    Get.lazyPut(() => CSSXKDViPhamController());
    Get.lazyPut(() => ProcService(Get.find<DioService>()));
  }
}
