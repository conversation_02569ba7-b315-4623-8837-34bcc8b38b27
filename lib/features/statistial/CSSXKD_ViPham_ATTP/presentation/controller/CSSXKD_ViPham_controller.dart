import 'package:attp_2024/core/data/models/user_access_model.dart';
import 'package:attp_2024/core/services/user_use_case.dart';
import 'package:attp_2024/features/statistial/CSSXKD_ViPham_ATTP/widget/detail_modal.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:attp_2024/core/configs/contanst/proc_constants.dart';
import 'package:attp_2024/core/data/api/services/proc/proc_service.dart';
import 'package:attp_2024/core/data/database/device_data.dart';
import 'package:attp_2024/core/data/dto/request/proc_request_model.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import '../../../../../core/data/api/configs/dio_configs.dart';

class CSSXKDViPhamController extends GetxController {
  @override
  void onInit() async {
    super.onInit();
    _dioService.init();
    await loadInfoProfile();
    await fetchDonViByID();
    await Future.wait([
      fetchAllProvinces(),
      //fetchStatistical(),
      fetchStatisticalInitial(),
      fetchStatisticalInitialData(),
    ]);
  }

  final RxList<String> labels = <String>[].obs;
  final RxList<List<double>> groupValues = <List<double>>[].obs;
  final RxList<Color> groupColors = <Color>[].obs;
  final List<String> groupNames = [
    'Nhắc nhở, cảnh báo',
    'Thu hồi giấy cam kết',
    'Xử phạt hành chính',
    'Thu hồi giấy chứng nhận',
    'Hình thức khác'
  ];
  final RxList<double> pieValues = <double>[0, 0, 0, 0].obs;
  final RxDouble summary = 0.0.obs;
  // final double summary = 100;
  // final List<double> pieValues = [40, 30, 20, 10];

  final List<String> pieLabels = [
    'Nhắc nhở, cảnh báo',
    'Thu hồi GCK',
    'Xử phạt HC',
    'Thu hồi GCN',
    'Hình thức khác'
  ];
  final List<String> pieLegends = [
    'Nhắc nhở, cảnh báo',
    'Thu hồi GCK',
    'Xử phạt HC',
    'Thu hồi GCN',
    'Hình thức khác'
  ];
  final List<Color> pieColors = [Colors.red, Colors.amber, Colors.orange];

  final ProcService _procService = Get.find<ProcService>();
  final DioService _dioService = Get.find<DioService>();

  var province = ''.obs;
  var district = ''.obs;
  var commune = ''.obs;
  var village = ''.obs;

  var provinces = [].obs;
  var districts = [].obs;
  var communes = [].obs;
  var villages = [].obs;
  var gCNs = [].obs;
  var listDataTap = [].obs;
  var isLoadingProvinces = false.obs;
  var isLoadingDistricts = false.obs;
  var isLoadingCommunes = false.obs;
  var isLoadingVillages = false.obs;
  var isFetchStatistical = false.obs;

  var provinceFilter = ''.obs;
  var districtFilter = ''.obs;
  var communeFilter = ''.obs;
  var villageFilter = ''.obs;
  Rx<DateTime> startDate = DateTime(DateTime.now().year, 1, 1).obs;
  Rx<DateTime> endDate = DateTime.now().obs;

  UserAccessModel? userAccessModel;
  var donViId = '';
  var userId = '';
  var userGroup = ''.obs;
  String defaultID = '00000000-0000-0000-0000-000000000000';
  var isLoadingdefaultTinhHuyenXa = false.obs;
  var defaultTinhHuyenXa = [].obs;

  //error text
  var provinceErrorText = ''.obs;

  void clearValue() {
    provinceFilter.value = '';
    districtFilter.value = '';
    communeFilter.value = '';
    villageFilter.value = '';
    provinceErrorText.value = '';
  }

  Future<void> fetchAllProvinces() async {
    isLoadingProvinces.value = true;
    final List<Map<String, dynamic>> body = [];
    try {
      List<dynamic> provincesResponse =
          await _procService.callProc("Proc_Mobile_GetAll_DiaBanHCTinh", body);
      List<Map<String, dynamic>> mappedTinhResponse =
          provincesResponse.cast<Map<String, dynamic>>();

      provinces.assignAll(
        mappedTinhResponse.map((Map<String, dynamic> tinhs) {
          return {
            "value": tinhs['DiaBanHCID'],
            "display": tinhs['TenDiaBan'],
          };
        }).toList(),
      );
      print('Tinh $provinces');
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isLoadingProvinces.value = false;
    }
  }

  Future<void> fetchAllDistricts({required String tinhID}) async {
    isLoadingDistricts.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "TinhID", "type": "guid", "value": tinhID},
    ];
    try {
      List<dynamic> districtsResponse =
          await _procService.callProc("Proc_Mobile_GetAll_DiaBanHCHuyen", body);
      List<Map<String, dynamic>> mappedHuyenResponse =
          districtsResponse.cast<Map<String, dynamic>>();

      districts.assignAll(
        mappedHuyenResponse.map((Map<String, dynamic> huyens) {
          return {
            "value": huyens['DiaBanHCID'],
            "display": huyens['TenDiaBan'],
          };
        }).toList(),
      );
      print('Huyen $districts');
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isLoadingDistricts.value = false;
    }
  }

  Future<void> fetchAllCommunes({required String huyenID}) async {
    isLoadingCommunes.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "HuyenID", "type": "guid", "value": huyenID},
    ];
    try {
      List<dynamic> communesResponse =
          await _procService.callProc("Proc_Mobile_GetAll_DiaBanHCXa", body);
      List<Map<String, dynamic>> mappedXaResponse =
          communesResponse.cast<Map<String, dynamic>>();

      communes.assignAll(
        mappedXaResponse.map((Map<String, dynamic> xas) {
          return {
            "value": xas['DiaBanHCID'],
            "display": xas['TenDiaBan'],
          };
        }).toList(),
      );
      print('Xa $communes');
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isLoadingCommunes.value = false;
    }
  }

  Future<void> fetchAllVillages({required String xaID}) async {
    isLoadingVillages.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "xaID", "type": "guid", "value": xaID},
    ];
    try {
      List<dynamic> villagesResponse =
          await _procService.callProc("Proc_Mobile_GetAll_DiaBanHCThon", body);
      List<Map<String, dynamic>> mappedThonResponse =
          villagesResponse.cast<Map<String, dynamic>>();

      villages.assignAll(
        mappedThonResponse.map((Map<String, dynamic> thons) {
          return {
            "value": thons['DiaBanHCID'],
            "display": thons['TenDiaBan'],
          };
        }).toList(),
      );
      print('Thon $villages');
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isLoadingVillages.value = false;
    }
  }

  Future<void> loadInfoProfile() async {
    userAccessModel = await UserUseCase.getUser();
    userGroup.value = userAccessModel?.userGroupCode ?? '';
    userId = userAccessModel?.userID ?? defaultID;
    donViId = userAccessModel?.donViID ?? defaultID;
  }

  Future<void> fetchDonViByID() async {
    isLoadingdefaultTinhHuyenXa.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "ID", "type": "guid", "value": donViId}
    ];
    try {
      List<dynamic> res =
          await _procService.callProc('Proc_Mobile_GetDonViByID', body);
      List<Map<String, dynamic>> mappedResponse =
          res.cast<Map<String, dynamic>>();
      defaultTinhHuyenXa.assignAll(
        mappedResponse.map((Map<String, dynamic> item) {
          return {
            "TinhID_": item['TinhID_'],
            "HuyenID_": item['HuyenID_'],
            "XaID_": item['XaID_'],
            "ThonID_": item['ThonID_'],
          };
        }).toList(),
      );
      setDefaultTinhHuyenXa();
    } finally {
      isLoadingdefaultTinhHuyenXa.value = false;
    }
  }

  void setDefaultTinhHuyenXa() {
    fetchAllProvinces();
    fetchAllDistricts(tinhID: defaultTinhHuyenXa.first['TinhID_']);
    fetchAllCommunes(huyenID: defaultTinhHuyenXa.first['HuyenID_']);
    fetchAllVillages(xaID: defaultTinhHuyenXa.first['XaID_']);
    if (defaultTinhHuyenXa.isNotEmpty &&
        defaultTinhHuyenXa.first['TinhID_'] !=
            '00000000-0000-0000-0000-000000000000') {
      provinceFilter.value = defaultTinhHuyenXa.first['TinhID_'];
    }
    if (defaultTinhHuyenXa.isNotEmpty &&
        defaultTinhHuyenXa.first['HuyenID_'] !=
            '00000000-0000-0000-0000-000000000000') {
      districtFilter.value = defaultTinhHuyenXa.first['HuyenID_'];
    }
    if (defaultTinhHuyenXa.isNotEmpty &&
        defaultTinhHuyenXa.first['XaID_'] !=
            '00000000-0000-0000-0000-000000000000') {
      communeFilter.value = defaultTinhHuyenXa.first['XaID_'];
    }
    if (defaultTinhHuyenXa.isNotEmpty &&
        defaultTinhHuyenXa.first['ThonID_'] !=
            '00000000-0000-0000-0000-000000000000') {
      villageFilter.value = defaultTinhHuyenXa.first['ThonID_'];
    }
  }

  // Future<void> fetchStatistical() async {
  //   isFetchStatistical.value = true;
  //   final List<Map<String, dynamic>> body = [
  //     {"Type": "guid", "Name": "TinhID", "Value": provinceFilter.value},
  //     {"Type": "guid", "Name": "HuyenID", "Value": districtFilter.value},
  //     {"Type": "guid", "Name": "XaID", "Value": communeFilter.value},
  //     {"Type": "guid", "Name": "ThonID", "Value": villageFilter.value},
  //     {"Type": "date", "Name": "TuNgay", "Value": DateFormat('dd-MM-yyyy').format(startDate.value)},
  //     {"Type": "date", "Name": "DenNgay", "Value": DateFormat('dd-MM-yyyy').format(endDate.value)},
  //   ];
  //   try {
  //     List<dynamic> response =
  //         await _procService.callProc(ProcConstants.thongKeCSSXKDViPhamATTP, body);
  //
  //     gCNs.assignAll(response);
  //     print('gCNs $gCNs');
  //     labels
  //         .assignAll(gCNs.map((item) => item['TenDiaBan'].toString()).toList());
  //     groupValues.assignAll(gCNs
  //         .map((item) {
  //           return [
  //             item['XuongLoai']?.toDouble() ?? 0.0,
  //             item['ThuHoiGCN']?.toDouble() ?? 0.0,
  //             item['ViPhamCamKet']?.toDouble() ?? 0.0,
  //           ];
  //         })
  //         .toList()
  //         .map((e) => e.cast<double>())
  //         .toList());
  //
  //     // Tính toán pieValues và summary
  //     double xuongLoai = 0;
  //     double thuHoiGCN = 0;
  //     double viPhamCamKet = 0;
  //
  //     for (var item in gCNs) {
  //       xuongLoai += item['XuongLoai']?.toDouble() ?? 0;
  //       thuHoiGCN += item['ThuHoiGCN']?.toDouble() ?? 0;
  //       viPhamCamKet += item['ViPhamCamKet']?.toDouble() ?? 0;
  //     }
  //     pieValues.assignAll([xuongLoai, thuHoiGCN, viPhamCamKet]);
  //     summary.value = xuongLoai + thuHoiGCN + viPhamCamKet;
  //     groupColors.assignAll([AppColors.ConHieuLucTren6Thang, AppColors.ConHieuLucDuoi6Thang, AppColors.DaThuHoi]);
  //     pieColors.assignAll([AppColors.ConHieuLucTren6Thang, AppColors.ConHieuLucDuoi6Thang, AppColors.DaThuHoi]);
  //    // groupNames.assignAll(['Trên 6 tháng', 'Dưới 6 tháng', 'Hết hiệu lực', 'Đã thu hồi']);
  //   } catch (e) {
  //     print("ERROR OCCURRED: $e");
  //   } finally {
  //     isFetchStatistical.value = false;
  //   }
  // }

  Future<void> fetchStatistical() async {
    isFetchStatistical.value = true;

    final List<Map<String, dynamic>> body = [
      {"Type": "guid", "Name": "TinhID", "Value": provinceFilter.value},
      {"Type": "guid", "Name": "HuyenID", "Value": districtFilter.value},
      {"Type": "guid", "Name": "XaID", "Value": communeFilter.value},
      {"Type": "guid", "Name": "ThonID", "Value": villageFilter.value},
      {
        "Type": "DateTime",
        "Name": "TuNgay",
        "Value": DateFormat('yyyy-MM-dd').format(startDate.value)
      },
      {
        "Type": "DateTime",
        "Name": "DenNgay",
        "Value": DateFormat('yyyy-MM-dd').format(endDate.value)
      },
    ];

    try {
      List<dynamic> response = await _procService.callProc(
          ProcConstants.thongKeCSSXKDViPhamATTP, body);

      gCNs.assignAll(response);
      fetchStatisticalData();
      print('gCNs $gCNs');

      labels
          .assignAll(gCNs.map((item) => item['TenDiaBan'].toString()).toList());

      groupValues.assignAll(gCNs
          .map((item) {
            return [
              (item['04']?.toDouble() ?? 0.0), // Nhắc nhở, cảnh báo
              (item['02']?.toDouble() ?? 0.0), // Thu hồi giấy cam kết
              (item['03']?.toDouble() ?? 0.0), // Xử phạt hành chính
              (item['01']?.toDouble() ?? 0.0), // Thu hồi giấy chứng nhận
              (item['05']?.toDouble() ?? 0.0), // Hình thức khác
            ];
          })
          .toList()
          .map((e) => e.cast<double>())
          .toList());

      double nhacNho = 0;
      double thuHoiCamKet = 0;
      double xuPhatHanhChinh = 0;
      double hinhThucKhac = 0;
      double thuHoiGCN = 0;

      for (var item in gCNs) {
        nhacNho += item['04']?.toDouble() ?? 0;
        thuHoiCamKet += item['02']?.toDouble() ?? 0;
        xuPhatHanhChinh += item['03']?.toDouble() ?? 0;
        thuHoiGCN += item['01']?.toDouble() ?? 0;
        hinhThucKhac += item['05']?.toDouble() ?? 0;
      }

      pieValues.assignAll(
          [nhacNho, thuHoiCamKet, xuPhatHanhChinh, thuHoiGCN, hinhThucKhac]);

      summary.value =
          nhacNho + thuHoiCamKet + xuPhatHanhChinh + hinhThucKhac + thuHoiGCN;

      groupColors.assignAll([
        AppColors.NhacNhoCanhBao,
        AppColors.ThuHoiGiayCamKet,
        AppColors.XuPhatHanhChinh,
        AppColors.ThuHoiGiayChungNhan,
        AppColors.HinhThucKhac
      ]);
      pieColors.assignAll([
        AppColors.NhacNhoCanhBao,
        AppColors.ThuHoiGiayCamKet,
        AppColors.XuPhatHanhChinh,
        AppColors.ThuHoiGiayChungNhan,
        AppColors.HinhThucKhac
      ]);
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isFetchStatistical.value = false;
    }
  }

  Future<void> fetchStatisticalInitial() async {
    isFetchStatistical.value = true;

    final List<Map<String, dynamic>> body = [
      {
        "Type": "guid",
        "Name": "TinhID",
        "Value": '59E5E684-F7D1-4430-B380-4914A9053926'
      },
      {"Type": "guid", "Name": "HuyenID", "Value": districtFilter.value},
      {"Type": "guid", "Name": "XaID", "Value": communeFilter.value},
      {"Type": "guid", "Name": "ThonID", "Value": villageFilter.value},
      {
        "Type": "DateTime",
        "Name": "TuNgay",
        "Value": DateFormat('yyyy-MM-dd').format(startDate.value)
      },
      {
        "Type": "DateTime",
        "Name": "DenNgay",
        "Value": DateFormat('yyyy-MM-dd').format(endDate.value)
      },
    ];

    try {
      List<dynamic> response = await _procService.callProc(
          ProcConstants.thongKeCSSXKDViPhamATTP, body);

      gCNs.assignAll(response);
      print('gCNs $gCNs');

      labels
          .assignAll(gCNs.map((item) => item['TenDiaBan'].toString()).toList());

      groupValues.assignAll(gCNs
          .map((item) {
            return [
              (item['04']?.toDouble() ?? 0.0), // Nhắc nhở, cảnh báo
              (item['02']?.toDouble() ?? 0.0), // Thu hồi giấy cam kết
              (item['03']?.toDouble() ?? 0.0), // Xử phạt hành chính
              (item['01']?.toDouble() ?? 0.0), // Thu hồi giấy chứng nhận
              (item['05']?.toDouble() ?? 0.0), // Hình thức khác
            ];
          })
          .toList()
          .map((e) => e.cast<double>())
          .toList());

      double nhacNho = 0;
      double thuHoiCamKet = 0;
      double xuPhatHanhChinh = 0;
      double hinhThucKhac = 0;
      double thuHoiGCN = 0;

      for (var item in gCNs) {
        nhacNho += item['04']?.toDouble() ?? 0;
        thuHoiCamKet += item['02']?.toDouble() ?? 0;
        xuPhatHanhChinh += item['03']?.toDouble() ?? 0;
        thuHoiGCN += item['01']?.toDouble() ?? 0;
        hinhThucKhac += item['05']?.toDouble() ?? 0;
      }

      pieValues.assignAll(
          [nhacNho, thuHoiCamKet, xuPhatHanhChinh, thuHoiGCN, hinhThucKhac]);

      summary.value =
          nhacNho + thuHoiCamKet + xuPhatHanhChinh + hinhThucKhac + thuHoiGCN;

      groupColors.assignAll([
        AppColors.NhacNhoCanhBao,
        AppColors.ThuHoiGiayCamKet,
        AppColors.XuPhatHanhChinh,
        AppColors.ThuHoiGiayChungNhan,
        AppColors.HinhThucKhac
      ]);
      pieColors.assignAll([
        AppColors.NhacNhoCanhBao,
        AppColors.ThuHoiGiayCamKet,
        AppColors.XuPhatHanhChinh,
        AppColors.ThuHoiGiayChungNhan,
        AppColors.HinhThucKhac
      ]);
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isFetchStatistical.value = false;
    }
  }

  Future<void> fetchStatisticalData() async {
    isFetchStatistical.value = true;

    final List<Map<String, dynamic>> body = [
      {"Type": "guid", "Name": "TinhID", "Value": provinceFilter.value},
      {"Type": "guid", "Name": "HuyenID", "Value": districtFilter.value},
      {"Type": "guid", "Name": "XaID", "Value": communeFilter.value},
      {"Type": "guid", "Name": "ThonID", "Value": villageFilter.value},
      {
        "Type": "DateTime",
        "Name": "TuNgay",
        "Value": DateFormat('yyyy-MM-dd').format(startDate.value)
      },
      {
        "Type": "DateTime",
        "Name": "DenNgay",
        "Value": DateFormat('yyyy-MM-dd').format(endDate.value)
      },
    ];

    try {
      List<dynamic> response = await _procService.callProc(
          ProcConstants.thongKeCSSXKDViPhamATTP_V2, body);
      listDataTap.assignAll(response);
      print("listDataTap: ${listDataTap}");
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isFetchStatistical.value = false;
    }
  }

  Future<void> fetchStatisticalInitialData() async {
    isFetchStatistical.value = true;

    final List<Map<String, dynamic>> body = [
      {
        "Type": "guid",
        "Name": "TinhID",
        "Value": '59E5E684-F7D1-4430-B380-4914A9053926'
      },
      {"Type": "guid", "Name": "HuyenID", "Value": districtFilter.value},
      {"Type": "guid", "Name": "XaID", "Value": communeFilter.value},
      {"Type": "guid", "Name": "ThonID", "Value": villageFilter.value},
      {
        "Type": "date",
        "Name": "TuNgay",
        "Value": DateFormat('dd-MM-yyyy').format(startDate.value)
      },
      {
        "Type": "date",
        "Name": "DenNgay",
        "Value": DateFormat('dd-MM-yyyy').format(endDate.value)
      },
    ];

    try {
      List<dynamic> response = await _procService.callProc(
          ProcConstants.thongKeCSSXKDViPhamATTP_V2, body);
      listDataTap.assignAll(response);
      print("listDataTap: ${listDataTap}");
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isFetchStatistical.value = false;
    }
  }

  Future<void> handleTapGroup(String label, int groupIndex) async {
    // Lọc dữ liệu theo TenDiaBan
    var filteredData =
        listDataTap.where((item) => item['TenDiaBan'] == label).toList();

    if (filteredData.isEmpty) {
      Get.snackbar(
        'Thông báo',
        'Không có dữ liệu chi tiết cho địa bàn này',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.white,
      );
      return;
    }

    // Nhóm dữ liệu theo HinhThucXuLy
    Map<String, List<dynamic>> groupedData = {};
    for (var item in filteredData) {
      String maHinhThuc =
          item['ThongKeThuocLoai']?.toString() ?? 'Không xác định';
      if (!groupedData.containsKey(maHinhThuc)) {
        groupedData[maHinhThuc] = [];
      }
      groupedData[maHinhThuc]!.add(item);
    }

    Get.bottomSheet(
      DetailModal(
        label: label,
        groupedData: groupedData,
      ),
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
    );
  }
}
