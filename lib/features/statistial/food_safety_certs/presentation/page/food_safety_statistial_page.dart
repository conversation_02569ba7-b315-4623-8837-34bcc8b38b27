import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/ui/widgets/chart/custom_horizontal_stack_bar_chart.dart';
import 'package:attp_2024/core/ui/widgets/chart/custom_stack_bar_chart.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/chart/custom_bar_chart.dart';
import '../../../../../core/ui/widgets/appbar/app_bar_widget.dart';
import '../../../../../core/ui/widgets/chart/custom_pie_chart.dart';
import '../../widget/filter_modal.dart';
import '../controller/food_safety_certs_statistial_controller.dart';

class FoodSafetyCertsStatisticalPage
    extends GetView<FoodSafetyCertsStatistialController> {
  const FoodSafetyCertsStatisticalPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          width: 100.w,
          color: AppColors.backgroundColorPrimary,
        ),
        Scaffold(
          backgroundColor: AppColors.backgroundColorPrimary,
          appBar: const AppBarWidget(
            title: 'Thống kê CSSXKD thuộc diện cấp GCN',
          ),
          body: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                children: [
                  const SizedBox(height: 2),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        'Điều kiện thống kê',
                        style: TextStyle(
                          fontSize: AppDimens.mediumText,
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        onPressed: () {
                          // controller.clearValue();
                          FilterModal.show(context);
                        },
                        icon: const Icon(Icons.tune_outlined),
                        color: AppColors.primary,
                        iconSize: AppDimens.textSize24,
                      ),
                    ],
                  ),
                  Card(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 5,
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.pie_chart_outline,
                                color: AppColors.primary,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'Biểu đồ thống kê CSSXKD thuộc diện cấp giấy chứng nhận ATTP',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 15.sp,
                                    color: AppColors.primary,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 4),
                            ],
                          ),
                          Row(
                            children: [
                              const Icon(
                                Icons.stacked_bar_chart_outlined,
                                color: Colors.white,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Tỉ lệ trên các địa bàn trong thành phố/quận/huyện',
                                style: TextStyle(fontSize: 14.sp),
                              )
                            ],
                          ),
                          const SizedBox(height: 15),
                          SizedBox(
                            child: Obx(
                              () {
                                if (controller.pieValues.isEmpty ||
                                    controller.labels.isEmpty ||
                                    controller.pieColors.isEmpty) {
                                  return const Center(
                                      child:
                                          Text('Không có dữ liệu để hiển thị'));
                                }
                                return CustomPieChart(
                                  sumary: controller.summary.value,
                                  values: controller.pieValues,
                                  labels: controller.pieLabels,
                                  title: 'Pie Chart Example',
                                  colors: controller.pieColors,
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Card(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 5,
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.stacked_bar_chart_outlined,
                                    color: AppColors.primary,
                                  ),
                                  const SizedBox(width: 8),
                                  // Add spacing between the icon and text
                                  Expanded(
                                    child: Text(
                                      'Biểu đồ thống kê CSSXKD thuộc diện cấp giấy chứng nhận ATTP',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 15.sp,
                                        color: AppColors.primary,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                ],
                              ),
                              Row(
                                children: [
                                  const Icon(
                                    Icons.stacked_bar_chart_outlined,
                                    color: Colors.white,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Chi tiết trên từng địa bàn thành phố/quận/huyện',
                                    style: TextStyle(fontSize: 14.sp),
                                  )
                                ],
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          SizedBox(
                            child: Obx(() {
                              if (controller.groupValues.isEmpty ||
                                  controller.labels.isEmpty ||
                                  controller.groupColors.isEmpty) {
                                return const Center(
                                    child:
                                        Text('Không có dữ liệu để hiển thị'));
                              }
                              return HorizontalStackedBarChart(
                                labels: controller.labels,
                                groupValues: controller.groupValues,
                                groupColors: controller.groupColors,
                                groupNames: controller.groupNames,
                                onTapGroup: controller.handleTapGroup,
                              );
                            }),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
