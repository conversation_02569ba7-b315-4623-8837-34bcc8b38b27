import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/bindings_interface.dart';

import '../../../../core/data/api/configs/dio_configs.dart';
import '../../../../core/data/api/services/proc/proc_service.dart';
import '../presentation/controller/food_safety_certs_statistial_controller.dart';

class FoodSafeTyCertsStatistialBinding extends Bindings {
  //final _procService = GetIt.I<ProcService>();
  @override
  void dependencies() {
    Get.lazyPut(() => FoodSafetyCertsStatistialController());
    Get.lazyPut(() => ProcService(Get.find<DioService>()));
  }
}
