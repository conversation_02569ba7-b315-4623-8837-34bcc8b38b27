import 'package:attp_2024/core/data/api/configs/dio_configs.dart';
import 'package:attp_2024/core/data/api/services/proc/proc_service.dart';
import 'package:get/get.dart';

import '../presentation/controller/thongtin_gcn_controller.dart';

class ThongTinGcnBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => ThongtinGcnController());
    Get.lazyPut(() => ProcService(Get.find<DioService>()));
  }
}
