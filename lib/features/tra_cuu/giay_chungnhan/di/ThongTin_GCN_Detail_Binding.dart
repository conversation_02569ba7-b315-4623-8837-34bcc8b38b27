import 'package:get/get.dart';

import '../../../../core/data/api/configs/dio_configs.dart';
import '../../../../core/data/api/services/proc/proc_service.dart';
import '../presentation/controller/thongtin_gcn_detail_controller.dart';

class ThongtinGcnDetailBinding extends Bindings {
  @override
  void dependencies() {
    Get.put(ThongtinGcnDetailController());
    Get.lazyPut(() => ProcService(Get.find<DioService>()));
  }
}
