import 'package:attp_2024/core/configs/contents/app_content.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/ui/widgets/webview/webview_page.dart';
import 'package:attp_2024/core/utils/convert_text.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import '../../model/ThongTin_GCNGCK_ATTP_Model.dart';
import '../controller/thongtin_gcn_detail_controller.dart';

class ThongtinGcnDetailPage extends GetView<ThongtinGcnDetailController> {
  const ThongtinGcnDetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    final args = Get.arguments;
    return Scaffold(
        appBar: const AppBarWidget(
          centerTitle: true,
          title: "Thông tin giấy chứng nhận ",
        ),
        body: _buildBody(args));
  }

  Widget _buildBody(ThongtinGcngckAttpModel item) {
    const defaultText = "Đang cập nhật";
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Thông tin cơ sở sản xuất kinh doanh",
              style: TextStyle(
                  fontSize: AppDimens.mediumText,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary),
            ),
            const Gap(10.0),
            _buildInfoRow("Mã cơ sở:", item.SoGCN ?? defaultText,
                icon: Icons.tag),
            _buildInfoRow(
                "Tên cơ sở:",
                getText(item.TenCoSoSXKD.toString(), before: false) ??
                    defaultText,
                icon: Icons.business),
            _buildInfoRow("Chủ cơ sở:", item.HoVaTen_DaiDien ?? defaultText,
                icon: Icons.stacked_bar_chart),
            _buildInfoRow(
                "Loại hình cơ sở:", item.TenLoaiHinhCoSo ?? defaultText,
                icon: Icons.stacked_bar_chart),
            _buildInfoRow("GGĐKKD:", item.SoGPKD ?? defaultText,
                icon: Icons.stacked_bar_chart),
            _buildInfoRow("Ngày cấp:", item.NgayCapGPKD ?? defaultText,
                icon: Icons.calendar_today),
            _buildInfoRow("Cơ quan cấp:", item.CoQuanCapGPKD ?? defaultText,
                icon: Icons.apartment),
            _buildInfoRow("Số điện thoại:", item.SoDienThoai ?? defaultText,
                icon: Icons.phone),
            _buildInfoRow("Email:", item.EmailCS ?? defaultText,
                icon: Icons.email),
            _buildInfoRow("Địa chỉ cơ sở:", item.DiaChiCS ?? defaultText,
                icon: Icons.location_on),
            const Divider(),
            Text(
              "Thông tin giấy chứng nhận ATTP",
              style: TextStyle(
                  fontSize: AppDimens.mediumText,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary),
            ),
            const Gap(10.0),
            _buildInfoRow("Số giấy chứng nhận:", item.SoGCN ?? defaultText,
                icon: Icons.tag),
            _buildInfoRow("Ngày cấp:", item.NgayCap ?? defaultText,
                icon: Icons.calendar_today),
            _buildInfoRow("Ngày hết hạn:", item.NgayHetHan ?? defaultText,
                icon: Icons.calendar_today),
            _buildInfoRow("Người cấp:", item.NguoiCap ?? defaultText,
                icon: Icons.person),
            _buildInfoRow("Chức vụ:", item.ChucVu ?? defaultText,
                icon: Icons.badge),
            _buildInfoRow("Cơ quan cấp:", item.CoQuan ?? defaultText,
                icon: Icons.apartment),
            _buildInfoRow(
                "Hình thức:",
                '${item.TrangThaiBH} của giấy chứng nhận số ${item.SoGCN} ngày ${item.NgayCap}' ??
                    defaultText,
                icon: Icons.apartment),
            TextButton.icon(
              onPressed: () {
                print(item.dinhKem);
                String baseUrl = controller.userAccessModel?.siteURL ?? "";
                String path = item.dinhKem ?? "";

                if (item.dinhKem == null || item.dinhKem == "") {
                  Fluttertoast.showToast(
                    msg: "Không có file đính kèm!",
                    toastLength: Toast.LENGTH_SHORT,
                    gravity: ToastGravity.BOTTOM,
                    backgroundColor: Colors.yellow,
                    textColor: Colors.white,
                    fontSize: 14.0,
                  );
                  return;
                }

                String finalUrl = "$baseUrl$path";
                if (finalUrl.endsWith('*')) {
                  finalUrl = finalUrl.substring(0, finalUrl.length - 1);
                }

                Get.to(() => WebViewPage(
                      title: AppContent.appTitleWebview,
                      initialUrl: finalUrl,
                    ));
              },
              label: const Text(
                "Đính kèm",
              ),
              icon: Icon(
                Icons.attach_file_rounded,
                size: 18.sp,
                color: AppColors.primary,
              ),
              style: TextButton.styleFrom(
                padding: EdgeInsets.zero,
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
          ],
        ),
      ),
    );
  }

  static Widget _buildInfoRow(String label, String value, {IconData? icon}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: .5.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (icon != null)
            Padding(
              padding: EdgeInsets.only(right: 2.w),
              child: Icon(
                icon,
                size: 18.sp,
                color: AppColors.primary,
              ),
            ),
          SizedBox(
            width: 35.w,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(height: 1.5),
            ),
          ),
        ],
      ),
    );
  }
}
