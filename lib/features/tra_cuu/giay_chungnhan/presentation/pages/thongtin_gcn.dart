import 'package:attp_2024/core/utils/color_utils.dart';
import 'package:attp_2024/core/utils/convert_text.dart';
import 'package:attp_2024/features/tra_cuu/giay_chungnhan/presentation/widgets/search.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/routes/routes.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:attp_2024/features/tra_cuu/widgets/custom_card.dart';
import '../../../../../core/configs/contents/app_content.dart';
import '../../model/ThongTin_GCNGCK_ATTP_Model.dart';
import '../controller/thongtin_gcn_controller.dart';

class ThongTinGcnPage extends GetView<ThongtinGcnController> {
  const ThongTinGcnPage({super.key});

  @override
  Widget build(BuildContext context) {
    final ScrollController scrollController = ScrollController();
    final ValueNotifier<bool> isVisible = ValueNotifier(true);

    scrollController.addListener(() {
      if (scrollController.position.userScrollDirection ==
          ScrollDirection.reverse) {
        isVisible.value = false;
      } else if (scrollController.position.userScrollDirection ==
          ScrollDirection.forward) {
        isVisible.value = true;
      }

      // Thêm logic infinity scroll
      if (scrollController.position.pixels >=
          scrollController.position.maxScrollExtent - 100) {
        controller.loadMore();
      }
    });

    return Scaffold(
      appBar: const AppBarWidget(
        title: "Tra cứu giấy chứng nhận",
        centerTitle: true,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SearchWidget(),
          Expanded(
            child: RefreshIndicator(
              onRefresh: () async {
                controller.fetchSearchResults("");
              },
              child: Obx(() {
                if (controller.isLoading.value &&
                    controller.filteredResults.isEmpty) {
                  return const ShimmerLoading();
                }
                if (controller.filteredResults.isEmpty) {
                  return Center(
                    child: Image.asset(
                      AppImageString.iDataNotFound,
                      width: 100,
                      height: 100,
                    ),
                  );
                }
                return ListView.builder(
                  controller: scrollController,
                  padding: const EdgeInsets.all(8.0),
                  itemCount: controller.filteredResults.length +
                      (controller.isLoadingMore.value ||
                              controller.hasMoreData.value
                          ? 1
                          : 0),
                  itemBuilder: (context, index) {
                    if (index < controller.filteredResults.length) {
                      final item = controller.filteredResults[index];
                      return _buildListItem(item);
                    } else {
                      // Hiển thị loading indicator ở cuối danh sách
                      return controller.isLoadingMore.value
                          ? const Center(
                              child: Padding(
                                padding: EdgeInsets.all(8.0),
                                child: CircularProgressIndicator(),
                              ),
                            )
                          : const SizedBox.shrink();
                    }
                  },
                );
              }),
            ),
          ),
        ],
      ),
    );
  }

  // Widget hiển thị từng item
  Widget _buildListItem(ThongtinGcngckAttpModel item) {
    return CustomCard(
      title: LabelValuePair(
          label: "Số GCN", value: item.SoGCN ?? AppContent.textDefault),
      statusText: item.TrangThaiBH ?? AppContent.textDefault,
      statusColor: hexToColor(item.Color_TrangThaiBH),
      labelValueList: [
        LabelValuePair(
            label: "Ngày ký", value: item.NgayCap ?? AppContent.textDefault),
        LabelValuePair(
            label: "Ngày hết hạn",
            value: item.NgayHetHan ?? AppContent.textDefault),
        LabelValuePair(
            label: "Tên cơ sở",
            value: getText(item.TenCoSoSXKD.toString(), before: false)),
        LabelValuePair(
            label: "Loại hình cơ sở",
            value: item.TenLoaiHinhCoSo ?? AppContent.textDefault),
        LabelValuePair(
            label: "Địa chỉ", value: item.DiaChiCS ?? AppContent.textDefault),
      ],
      onTap: () {
        Get.toNamed(Routes.thongTinGCNDetail, arguments: item);
      },
    );
  }
}

// Widget hiệu ứng tải dữ liệu
class ShimmerLoading extends StatelessWidget {
  const ShimmerLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.all(8.0),
      itemCount: 5,
      itemBuilder: (context, index) {
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(5.0),
          ),
          margin: const EdgeInsets.symmetric(vertical: 8.0),
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildShimmerBox(width: 80, height: 80),
                const Gap(10.0),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildShimmerBox(width: double.infinity, height: 16.0),
                      const Gap(8.0),
                      _buildShimmerBox(width: 150.0, height: 16.0),
                      const Gap(8.0),
                      _buildShimmerBox(width: double.infinity, height: 16.0),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildShimmerBox({required double width, required double height}) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.0),
        ),
      ),
    );
  }
}
