import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/cssxkd_model.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/cssxkd_nhanvien_model.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/cssxkd_sanpham_model.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/cssxkd_tailieu_model.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/cssxkd_trangtb_model.dart';
import 'package:attp_2024/core/services/user_use_case.dart';
import 'package:attp_2024/core/data/models/user_access_model.dart';

class ThongtinGcnDetailController extends GetxController {
  UserAccessModel? userAccessModel;
  final log = Logger();

  var isLoading = false.obs;
  var sanPhamList = <CoSoSXKDSanPham>[].obs;
  var nhanVienList = <CoSoSXKDNhanVien>[].obs;
  var trangThietBiList = <CoSoSXKDTTB>[].obs;
  var tailieuList = <CoSoSXKDTL>[].obs;
  var csSxkdDetail = <CSSXKD>[].obs;

  @override
  onInit() async {
    super.onInit();
    await loadInfoProfile();
  }

  Future<void> loadInfoProfile() async {
    userAccessModel = await UserUseCase.getUser();
    update(["bodyID"]);
  }
}
