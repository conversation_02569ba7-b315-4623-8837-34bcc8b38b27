import 'package:attp_2024/core/configs/contanst/proc_constants.dart';
import 'package:attp_2024/core/data/models/user_access_model.dart';
import 'package:attp_2024/features/tra_cuu/giay_chungnhan/model/ThongTin_GCNGCK_ATTP_Model.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:attp_2024/core/data/api/services/proc/proc_service.dart';
import 'package:attp_2024/core/services/user_use_case.dart';

class ThongtinGcnController extends GetxController {
  final ProcService _procService = Get.find<ProcService>();
  var isLoading = false.obs;
  var searchResults = <ThongtinGcngckAttpModel>[].obs;
  var filteredResults = <ThongtinGcngckAttpModel>[].obs;
  var searchQuery = "".obs;
  final log = Logger();
  UserAccessModel? userAccessModel;
  var donViId = '';
  var userId = '';
  var userGroup = ''.obs;

  var province = ''.obs;
  var district = ''.obs;
  var commune = ''.obs;
  var village = ''.obs;

  var provinces = [].obs;
  var districts = [].obs;
  var communes = [].obs;
  var villages = [].obs;
  var loaiHinhCSs = [].obs;
  var gcnList = [].obs;
  var tinhTrangHDs = [].obs;
  var trangThaiXls = [].obs;
  var tinhTrangCapGCN = [].obs;

  var provinceFilter = ''.obs;
  var districtFilter = ''.obs;
  var communeFilter = ''.obs;
  var villageFilter = ''.obs;
  var oeFilter = ''.obs;
  var ftFilter = ''.obs;
  var txtSearchFilter = ''.obs;

  var loaihinhCsId = ''.obs;
  var tinhtrangCapGCNId = ''.obs;
  var trangthaiHDId = ''.obs;
  var ketquaXlId = ''.obs;
  var idDefault = "00000000-0000-0000-0000-000000000000";
  var thuocLoai = "01".obs;

  var sapXep = "NgayCap".obs;
  var tangGiamSapXep = "1".obs;

  Rx<DateTime> startDate = DateTime(DateTime.now().year, 1, 1).obs;
  Rx<DateTime> endDate = DateTime.now().obs;

  var isLoadingProvinces = false.obs;
  var isLoadingDistricts = false.obs;
  var isLoadingCommunes = false.obs;
  var isLoadingVillages = false.obs;
  var isLoadingTraCuu = false.obs;
  var isLoadingLoaiHinhCS = false.obs;
  var isLoadingTinhTrangHD = false.obs;
  var isTinhTrangCapGCN = false.obs;

  var isLoadingdefaultTinhHuyenXa = false.obs;
  var defaultTinhHuyenXa = [].obs;

  var currentPage = 1.obs;
  var pageSize = 10.obs;
  var hasMoreData = true.obs;
  var isLoadingMore = false.obs;

  @override
  Future<void> onInit() async {
    super.onInit();
    fetchAllProvinces();
    await loadInfoProfile();
    await fetchDonViByID(donViID: donViId);
    fetchSearchResults("");
    //fetchSearchResultsWithFilters();
    fetchAllLoaiHinhCS();
    fetchAllTinhTrang(type: "CapGCN");
    fetchAllTinhTrangCapGCN();
    // fetchAllDefaultTinhHuyenXa(donViID: donViId);
  }

  Future<void> loadInfoProfile() async {
    userAccessModel = await UserUseCase.getUser();
    userGroup.value = userAccessModel?.userGroupCode ?? '';
    donViId = userAccessModel?.donViID ?? '';
    userId = userAccessModel?.userID ?? '';
  }

  Future<String> _getUserID() async {
    final userId = await UserUseCase.getUser();
    return userId!.userID;
  }

  void clearProvinceFilters() {
    provinceFilter.value = '';
    districtFilter.value = '';
    communeFilter.value = '';
    villageFilter.value = '';
    districts.clear();
    communes.clear();
    villages.clear();
  }

  void clearDistrictFilters() {
    districtFilter.value = '';
    communeFilter.value = '';
    villageFilter.value = '';
    communes.clear();
    villages.clear();
  }

  void clearCommuneFilters() {
    communeFilter.value = '';
    villageFilter.value = '';
    villages.clear();
  }

  Future<void> fetchSearchResults(String query, {bool loadMore = false}) async {
    if (loadMore) {
      isLoadingMore.value = true;
    } else {
      isLoading.value = true;
      currentPage.value = 1;
      searchResults.clear();
    }

    try {
      // print(
      //     'mybody' + _buildRequestParams(query, loadMore: loadMore).toString());
      final response = await _procService.callProc(
          "Proc_Mobile_GetAll_TraCuuCoSoSXKDDuDK_Pagination_",
          _buildRequestParams(query, loadMore: loadMore));
      log.d(response);

      final newItems = response.isNotEmpty
          ? response
              .map<ThongtinGcngckAttpModel>((item) =>
                  ThongtinGcngckAttpModel.fromJson(
                      item as Map<String, dynamic>))
              .toList()
          : <ThongtinGcngckAttpModel>[];

      if (loadMore) {
        if (newItems.isEmpty) {
          hasMoreData.value = false;
        } else {
          searchResults.addAll(newItems);
          currentPage.value++;
        }
      } else {
        searchResults.value = newItems;
        hasMoreData.value = newItems.isNotEmpty;
      }

      filteredResults.value = searchResults;
    } catch (e) {
      log.e("Error fetching search results: $e");
      if (!loadMore) {
        searchResults.clear();
        filteredResults.clear();
      }
      hasMoreData.value = false;
    } finally {
      if (loadMore) {
        isLoadingMore.value = false;
      } else {
        isLoading.value = false;
      }
    }
  }

  List<Map<String, dynamic>> _buildRequestParams(String query,
      {bool loadMore = false}) {
    return [
      {
        "Type": "DateTime",
        "Name": "TuNgay",
        "Value": DateFormat('yyyy-MM-dd').format(startDate.value)
      },
      {
        "Type": "DateTime",
        "Name": "DenNgay",
        "Value": DateFormat('yyyy-MM-dd').format(endDate.value)
      },
      {
        "Type": "guid",
        "Name": "TinhID",
        "Value": provinceFilter.value.isEmpty ? idDefault : provinceFilter.value
      },
      {
        "Type": "guid",
        "Name": "HuyenID",
        "Value": districtFilter.value.isEmpty ? idDefault : districtFilter.value
      },
      {
        "Type": "guid",
        "Name": "XaID",
        "Value": communeFilter.value.isEmpty ? idDefault : communeFilter.value
      },
      {
        "Type": "guid",
        "Name": "ThonID",
        "Value": villageFilter.value.isEmpty ? idDefault : villageFilter.value
      },
      {"Type": "nvarchar", "Name": "TinhTrang_GCN_GCK_TimKiem", "Value": ""},
      {"Type": "nvarchar", "Name": "TuKhoa", "Value": query},
      {"Type": "nvarchar", "Name": "SapXep", "Value": "NgayCap"},
      {"Type": "nvarchar", "Name": "TangGiamSapXep", "Value": "DESC"},
      {"Type": "guid", "Name": "DonViID", "Value": donViId},
      {"Type": "guid", "Name": "UserID", "Value": userId},
      {"Type": "nvarchar", "Name": "ThuocLoai", "Value": "01"},
      {"Type": "guid", "Name": "LoaiHinhCoSoID", "Value": loaihinhCsId.value},
      {
        "Type": "int",
        "Name": "PageNumber",
        "Value": loadMore ? currentPage.value + 1 : 1
      },
      {"Type": "int", "Name": "PageSize", "Value": pageSize.value}
    ];
  }

  Future<void> fetchAllProvinces() async {
    isLoadingProvinces.value = true;
    final List<Map<String, dynamic>> body = [];
    try {
      List<dynamic> provincesResponse =
          await _procService.callProc("Proc_Mobile_GetAll_DiaBanHCTinh", body);
      List<Map<String, dynamic>> mappedTinhResponse =
          provincesResponse.cast<Map<String, dynamic>>();

      provinces.assignAll(
        mappedTinhResponse.map((Map<String, dynamic> tinhs) {
          return {
            "value": tinhs['DiaBanHCID'],
            "display": tinhs['TenDiaBan'],
          };
        }).toList(),
      );
    } finally {
      isLoadingProvinces.value = false;
    }
  }

  Future<void> fetchAllDistricts({required String tinhID}) async {
    isLoadingDistricts.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "TinhID", "type": "guid", "value": tinhID},
    ];
    try {
      List<dynamic> districtsResponse =
          await _procService.callProc("Proc_Mobile_GetAll_DiaBanHCHuyen", body);
      List<Map<String, dynamic>> mappedHuyenResponse =
          districtsResponse.cast<Map<String, dynamic>>();

      districts.assignAll(
        mappedHuyenResponse.map((Map<String, dynamic> huyens) {
          return {
            "value": huyens['DiaBanHCID'],
            "display": huyens['TenDiaBan'],
          };
        }).toList(),
      );
    } finally {
      isLoadingDistricts.value = false;
    }
  }

  Future<void> fetchAllCommunes({required String huyenID}) async {
    isLoadingCommunes.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "HuyenID", "type": "guid", "value": huyenID},
    ];
    try {
      List<dynamic> communesResponse =
          await _procService.callProc("Proc_Mobile_GetAll_DiaBanHCXa", body);
      List<Map<String, dynamic>> mappedXaResponse =
          communesResponse.cast<Map<String, dynamic>>();

      communes.assignAll(
        mappedXaResponse.map((Map<String, dynamic> xas) {
          return {
            "value": xas['DiaBanHCID'],
            "display": xas['TenDiaBan'],
          };
        }).toList(),
      );
    } finally {
      isLoadingCommunes.value = false;
    }
  }

  Future<void> fetchAllVillages({required String xaID}) async {
    isLoadingVillages.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "xaID", "type": "guid", "value": xaID},
    ];
    try {
      List<dynamic> villagesResponse =
          await _procService.callProc("Proc_Mobile_GetAll_DiaBanHCThon", body);
      List<Map<String, dynamic>> mappedThonResponse =
          villagesResponse.cast<Map<String, dynamic>>();

      villages.assignAll(
        mappedThonResponse.map((Map<String, dynamic> thons) {
          return {
            "value": thons['DiaBanHCID'],
            "display": thons['TenDiaBan'],
          };
        }).toList(),
      );
    } finally {
      isLoadingVillages.value = false;
    }
  }

  // Future<void> fetchAllDefaultTinhHuyenXa({required String donViID}) async {
  //   isLoadingdefaultTinhHuyenXa.value = true;
  //   final List<Map<String, dynamic>> body = [
  //     {
  //       "name": "DonViID",
  //       "type": "guid",
  //       "value": "522A3761-17EB-4161-9D04-49A64F703E49"
  //     }
  //   ];
  //   try {
  //     List<dynamic> res = await _procService.callProc(
  //         'Proc_Mobile_Get_ThietLapHeThong_ByDonViID', body);
  //     List<Map<String, dynamic>> mappedResponse =
  //         res.cast<Map<String, dynamic>>();
  //     defaultTinhHuyenXa.assignAll(
  //       mappedResponse.map((Map<String, dynamic> item) {
  //         return {
  //           "DiaBanHCID_Tinh": item['DiaBanHCID_Tinh'],
  //           "DiaBanHCID_Huyen": item['DiaBanHCID_Huyen'],
  //           "DiaBanHCID_Xa": item['DiaBanHCID_Xa'],
  //           "DiaBanHCID_Thon": item['DiaBanHCID_Thon'],
  //         };
  //       }).toList(),
  //     );
  //     log.i(defaultTinhHuyenXa.toString());
  //     setDefaultTinhHuyenXa();
  //   } finally {
  //     isLoadingdefaultTinhHuyenXa.value = false;
  //   }
  // }

  Future<void> fetchDonViByID({required String donViID}) async {
    isLoadingdefaultTinhHuyenXa.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "ID", "type": "guid", "value": donViId}
    ];
    try {
      List<dynamic> res =
          await _procService.callProc('Proc_Mobile_GetDonViByID', body);
      List<Map<String, dynamic>> mappedResponse =
          res.cast<Map<String, dynamic>>();
      defaultTinhHuyenXa.assignAll(
        mappedResponse.map((Map<String, dynamic> item) {
          return {
            "TinhID_": item['TinhID_'],
            "HuyenID_": item['HuyenID_'],
            "XaID_": item['XaID_'],
            "ThonID_": item['ThonID_'],
          };
        }).toList(),
      );
      log.i(defaultTinhHuyenXa.toString());
      setDefaultTinhHuyenXa();
    } finally {
      isLoadingdefaultTinhHuyenXa.value = false;
    }
  }

  void setDefaultTinhHuyenXa() {
    fetchAllProvinces();
    fetchAllDistricts(tinhID: defaultTinhHuyenXa.first['TinhID_']);
    fetchAllCommunes(huyenID: defaultTinhHuyenXa.first['HuyenID_']);
    fetchAllVillages(xaID: defaultTinhHuyenXa.first['XaID_']);
    if (defaultTinhHuyenXa.isNotEmpty &&
        defaultTinhHuyenXa.first['TinhID_'] !=
            '00000000-0000-0000-0000-000000000000') {
      provinceFilter.value = defaultTinhHuyenXa.first['TinhID_'];
    }
    if (defaultTinhHuyenXa.isNotEmpty &&
        defaultTinhHuyenXa.first['HuyenID_'] !=
            '00000000-0000-0000-0000-000000000000') {
      districtFilter.value = defaultTinhHuyenXa.first['HuyenID_'];
    }
    if (defaultTinhHuyenXa.isNotEmpty &&
        defaultTinhHuyenXa.first['XaID_'] !=
            '00000000-0000-0000-0000-000000000000') {
      communeFilter.value = defaultTinhHuyenXa.first['XaID_'];
    }
    if (defaultTinhHuyenXa.isNotEmpty &&
        defaultTinhHuyenXa.first['ThonID_'] !=
            '00000000-0000-0000-0000-000000000000') {
      villageFilter.value = defaultTinhHuyenXa.first['ThonID_'];
    }
  }

  Future<void> fetchAllLoaiHinhCS() async {
    isLoadingLoaiHinhCS.value = true;
    final List<Map<String, dynamic>> body = [];
    try {
      List<dynamic> response =
          await _procService.callProc("Proc_Mobile_GetAll_LoaiHinhCoSo", body);
      List<Map<String, dynamic>> mappedResponse =
          response.cast<Map<String, dynamic>>();

      loaiHinhCSs.assignAll(
        mappedResponse.map((Map<String, dynamic> loaiHinhCS) {
          return {
            "value": loaiHinhCS['LoaiHinhCoSoID'],
            "display": loaiHinhCS['TenLoaiHinhCoSo'],
          };
        }).toList(),
      );
    } finally {
      isLoadingLoaiHinhCS.value = false;
    }
  }

  Future<void> fetchAllTinhTrangCapGCN() async {
    isTinhTrangCapGCN.value = true;
    try {
      List<dynamic> response =
          await _procService.callProc(ProcConstants.getAllTinhTrangCapGCN, []);
      List<Map<String, dynamic>> mappedResponse =
          response.cast<Map<String, dynamic>>();

      tinhTrangCapGCN.assignAll(
        mappedResponse.map((Map<String, dynamic> tinhTrangCapGCN) {
          return {
            "value": tinhTrangCapGCN['MaTrangThai'],
            "display": tinhTrangCapGCN['TenTrangThai'],
          };
        }).toList(),
      );
    } finally {
      isLoadingTinhTrangHD.value = false;
    }
  }

  Future<void> fetchAllTinhTrang({required String type}) async {
    isLoadingTinhTrangHD.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "GroupType", "type": "string", "value": type},
    ];
    try {
      List<dynamic> response =
          await _procService.callProc("Proc_Mobile_Get_TrangThai", body);
      List<Map<String, dynamic>> mappedResponse =
          response.cast<Map<String, dynamic>>();

      tinhTrangHDs.assignAll(
        mappedResponse.map((Map<String, dynamic> tinhTrangHD) {
          return {
            "value": tinhTrangHD['TrangThaiID'],
            "display": tinhTrangHD['TenTrangThai'],
          };
        }).toList(),
      );
    } finally {
      isLoadingTinhTrangHD.value = false;
    }
  }

  Future<void> fetchSearchResultsWithFilters({bool loadMore = false}) async {
    if (loadMore) {
      isLoadingMore.value = true;
    } else {
      isLoading.value = true;
      currentPage.value = 1;
      searchResults.clear();
    }

    try {
      final List<Map<String, dynamic>> body = [
        {
          "Type": "DateTime",
          "Name": "TuNgay",
          "Value": DateFormat('yyyy-MM-dd').format(startDate.value)
        },
        {
          "Type": "DateTime",
          "Name": "DenNgay",
          "Value": DateFormat('yyyy-MM-dd').format(endDate.value)
        },
        {
          "Type": "Guid",
          "Name": "TinhID",
          "Value":
              provinceFilter.value.isEmpty ? idDefault : provinceFilter.value
        },
        {
          "Type": "Guid",
          "Name": "HuyenID",
          "Value":
              districtFilter.value.isEmpty ? idDefault : districtFilter.value
        },
        {
          "Type": "Guid",
          "Name": "XaID",
          "Value": communeFilter.value.isEmpty ? idDefault : communeFilter.value
        },
        {
          "Type": "Guid",
          "Name": "ThonID",
          "Value": villageFilter.value.isEmpty ? idDefault : villageFilter.value
        },
        {
          "Type": "nvarchar",
          "Name": "TinhTrang_GCN_GCK_TimKiem",
          "Value": tinhtrangCapGCNId.value
        },
        {"Type": "nvarchar", "Name": "TuKhoa", "Value": ""},
        {"Type": "nvarchar", "Name": "SapXep", "Value": "NgayCap"},
        {"Type": "nvarchar", "Name": "TangGiamSapXep", "Value": "DESC"},
        {"Type": "guid", "Name": "DonViID", "Value": donViId},
        {"Type": "guid", "Name": "UserID", "Value": userId},
        {"Type": "nvarchar", "Name": "ThuocLoai", "Value": thuocLoai.value},
        {"Type": "guid", "Name": "LoaiHinhCoSoID", "Value": loaihinhCsId.value},
        {
          "Type": "int",
          "Name": "PageNumber",
          "Value": loadMore ? currentPage.value + 1 : 1
        },
        {"Type": "int", "Name": "PageSize", "Value": pageSize.value}
      ];

      final response = await _procService.callProc(
          "Proc_Mobile_GetAll_TraCuuCoSoSXKDDuDK_Pagination_", body);

      final newItems = response.isNotEmpty
          ? response
              .map<ThongtinGcngckAttpModel>((item) =>
                  ThongtinGcngckAttpModel.fromJson(
                      item as Map<String, dynamic>))
              .toList()
          : <ThongtinGcngckAttpModel>[];

      if (loadMore) {
        if (newItems.isEmpty) {
          hasMoreData.value = false;
        } else {
          searchResults.addAll(newItems);
          currentPage.value++;
        }
      } else {
        searchResults.value = newItems;
        hasMoreData.value = newItems.isNotEmpty;
      }

      filteredResults.value = searchResults;
    } catch (e) {
      log.e("Error fetching search results with filters: $e");
      if (!loadMore) {
        searchResults.clear();
        filteredResults.clear();
      }
      hasMoreData.value = false;
    } finally {
      if (loadMore) {
        isLoadingMore.value = false;
      } else {
        isLoading.value = false;
      }
    }
  }

  void loadMore() {
    if (!isLoading.value && !isLoadingMore.value && hasMoreData.value) {
      fetchSearchResults(searchQuery.value, loadMore: true);
    }
  }
}
