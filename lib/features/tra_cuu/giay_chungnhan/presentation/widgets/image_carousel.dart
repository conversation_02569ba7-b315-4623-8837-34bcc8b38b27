import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';

class DotIndicatorCarousel extends StatefulWidget {
  const DotIndicatorCarousel({super.key});

  @override
  State<DotIndicatorCarousel> createState() => _DotIndicatorCarouselState();
}

class _DotIndicatorCarouselState extends State<DotIndicatorCarousel> {
  final List<String> imageUrls = [
    'http://laptrinhattp.qlns.vn/Dinhkem/000.00.00.H30/VanBan/HinhAnhCoSoSXKD/t%E1%BA%A3ixu%E1%BB%91ng(4)261224101659.jpg',
    'http://laptrinhattp.qlns.vn/Dinhkem/95/VanBan/HinhAnhCoSoSXKD/dich-vu-say-rau-cu051224021111.jpg',
    'http://laptrinhattp.qlns.vn/Dinhkem/95/VanBan/HinhAnhCoSoSXKD/dich-vu-say-rau-cu051224021111.jpg',
  ];
  int currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CarouselSlider.builder(
          itemCount: imageUrls.length,
          itemBuilder: (BuildContext context, int index, int realIndex) {
            return ClipRRect(
              borderRadius: BorderRadius.circular(5),
              child: Image.network(
                imageUrls[index],
                fit: BoxFit.cover,
                width: double.infinity,
              ),
            );
          },
          options: CarouselOptions(
            height: 200,
            autoPlay: true,
            enlargeCenterPage: true,
            onPageChanged: (index, reason) {
              setState(() {
                currentIndex = index;
              });
            },
          ),
        ),
        const SizedBox(height: 10),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: imageUrls.asMap().entries.map((entry) {
            return GestureDetector(
              onTap: () => setState(() => currentIndex = entry.key),
              child: Container(
                width: 8.0,
                height: 8.0,
                margin: const EdgeInsets.symmetric(horizontal: 4.0),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: currentIndex == entry.key
                      ? AppColors.primary
                      : Colors.grey,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}
