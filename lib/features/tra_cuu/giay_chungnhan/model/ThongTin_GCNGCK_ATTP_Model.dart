// ignore_for_file: non_constant_identifier_names

const String defaultImage = "https://via.placeholder.com/150";

class ThongtinGcngckAttpModel {
  // ... existing fields ...
  final String? SoGCN;
  final String? NgayCap;
  final String? NgayHetHan;
  final String? NguoiCap;
  final String? ChucVu;
  final String? CoQuan;
  final String? SoHoSo;
  final String? NgayNop;
  final String? ChucVu_NguoiXuLy;
  final String? NguoiXuLy;
  final String? TenCoSoSXKD;
  final String? SoGPKD;
  final String? NgayCapGPKD;
  final String? CoQuanCapGPKD;
  final String? SoDienThoai;
  final String? EmailCS;
  final String? TenLoaiHinhCoSo;
  final String? DiaChiCS;
  final String? HoVaTen_DaiDien;
  final String? ChucVu_DaiDien;
  final String? DiDong_DaiDien;
  final String? SoCMND_DaiDien;
  final String? CapGCNID;
  final String? TrangThai_BanHanh;
  final String? CoSoSXKDID;
  final String? MauGCNID;
  final String? TrangThaiBH;
  final String? Color_TrangThaiBH;
  final String? ThuocLoai;
  final String? dinhKem;
  final int? TotalResults;

  ThongtinGcngckAttpModel({
    // ... existing parameters ...
    this.SoGCN,
    this.NgayCap,
    this.NgayHetHan,
    this.NguoiCap,
    this.ChucVu,
    this.CoQuan,
    this.SoHoSo,
    this.NgayNop,
    this.ChucVu_NguoiXuLy,
    this.NguoiXuLy,
    this.TenCoSoSXKD,
    this.SoGPKD,
    this.NgayCapGPKD,
    this.CoQuanCapGPKD,
    this.SoDienThoai,
    this.EmailCS,
    this.TenLoaiHinhCoSo,
    this.DiaChiCS,
    this.HoVaTen_DaiDien,
    this.ChucVu_DaiDien,
    this.DiDong_DaiDien,
    this.SoCMND_DaiDien,
    this.CapGCNID,
    this.TrangThai_BanHanh,
    this.CoSoSXKDID,
    this.MauGCNID,
    this.TrangThaiBH,
    this.Color_TrangThaiBH,
    this.ThuocLoai,
    this.dinhKem,
    this.TotalResults,
  });

  factory ThongtinGcngckAttpModel.fromJson(Map<String, dynamic> json) {
    return ThongtinGcngckAttpModel(
        // ... existing fields ...
        SoGCN: json['SoGCN'],
        NgayCap: json['NgayCap'],
        NgayHetHan: json['NgayHetHan'],
        NguoiCap: json['NguoiCap'],
        ChucVu: json['ChucVu'],
        CoQuan: json['CoQuan'],
        SoHoSo: json['SoHoSo'],
        NgayNop: json['NgayNop'],
        ChucVu_NguoiXuLy: json['ChucVu_NguoiXuLy'],
        NguoiXuLy: json['NguoiXuLy'],
        TenCoSoSXKD: json['TenCoSoSXKD'],
        SoGPKD: json['SoGPKD'],
        NgayCapGPKD: json['NgayCapGPKD'],
        CoQuanCapGPKD: json['CoQuanCapGPKD'],
        SoDienThoai: json['SoDienThoai'],
        EmailCS: json['EmailCS'],
        TenLoaiHinhCoSo: json['TenLoaiHinhCoSo'],
        DiaChiCS: json['DiaChiCS'],
        HoVaTen_DaiDien: json['HoVaTen_DaiDien'],
        ChucVu_DaiDien: json['ChucVu_DaiDien'],
        DiDong_DaiDien: json['DiDong_DaiDien'],
        SoCMND_DaiDien: json['SoCMND_DaiDien'],
        CapGCNID: json['CapGCNID'],
        TrangThai_BanHanh: json['TrangThai_BanHanh'],
        CoSoSXKDID: json['CoSoSXKDID'],
        MauGCNID: json['MauGCNID'],
        TrangThaiBH: json['TrangThaiBH'],
        Color_TrangThaiBH: json['Color_TrangThaiBH'],
        ThuocLoai: json['ThuocLoai'],
        dinhKem: json['DinhKem'] as String?,
        TotalResults: json['TotalResults'] as int?);
  }

  Map<String, dynamic> toJson() {
    return {
      // ... existing fields ...
      'SoGCN': SoGCN,
      'NgayCap': NgayCap,
      'NgayHetHan': NgayHetHan,
      'NguoiCap': NguoiCap,
      'ChucVu': ChucVu,
      'CoQuan': CoQuan,
      'SoHoSo': SoHoSo,
      'NgayNop': NgayNop,
      'ChucVu_NguoiXuLy': ChucVu_NguoiXuLy,
      'NguoiXuLy': NguoiXuLy,
      'TenCoSoSXKD': TenCoSoSXKD,
      'SoGPKD': SoGPKD,
      'NgayCapGPKD': NgayCapGPKD,
      'CoQuanCapGPKD': CoQuanCapGPKD,
      'SoDienThoai': SoDienThoai,
      'EmailCS': EmailCS,
      'TenLoaiHinhCoSo': TenLoaiHinhCoSo,
      'DiaChiCS': DiaChiCS,
      'HoVaTen_DaiDien': HoVaTen_DaiDien,
      'ChucVu_DaiDien': ChucVu_DaiDien,
      'DiDong_DaiDien': DiDong_DaiDien,
      'SoCMND_DaiDien': SoCMND_DaiDien,
      'CapGCNID': CapGCNID,
      'TrangThai_BanHanh': TrangThai_BanHanh,
      'CoSoSXKDID': CoSoSXKDID,
      'MauGCNID': MauGCNID,
      'TrangThaiBH': TrangThaiBH,
      'Color_TrangThaiBH': Color_TrangThaiBH,
      'ThuocLoai': ThuocLoai,
      'DinhKem': dinhKem,
      'TotalResults': TotalResults,
    };
  }
}
