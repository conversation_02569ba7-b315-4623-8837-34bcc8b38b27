import 'package:get/get.dart';
import 'package:attp_2024/features/tra_cuu/giayxac<PERSON>an_kienthuc/presentation/controller/giay_xac_nhan_kien_thuc_attp_controller.dart';

import '../../../../core/data/api/configs/dio_configs.dart';
import '../../../../core/data/api/services/proc/proc_service.dart';

class GiayXacNhanKienThucATTPBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<GiayXacNhanKienThucATTPController>(
      () => GiayXacNhanKienThucATTPController(),
    );
    Get.lazyPut(() => ProcService(Get.find<DioService>()));
  }
}
