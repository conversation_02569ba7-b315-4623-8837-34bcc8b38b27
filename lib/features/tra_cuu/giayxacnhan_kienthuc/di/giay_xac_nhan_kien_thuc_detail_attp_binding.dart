import 'package:get/get.dart';
import 'package:attp_2024/features/tra_cuu/giayxacnhan_kienthuc/presentation/controller/giay_xac_nhan_kien_thuc_attp_controller.dart';

import '../../../../core/data/api/configs/dio_configs.dart';
import '../../../../core/data/api/services/proc/proc_service.dart';
import '../presentation/controller/giay_xac_nhan_kien_thuc_detail_controller.dart';

class GiayXacNhanKienThucDetailAttpBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<GiayXacNhanKienThucDetailController>(
      () => GiayXacNhanKienThucDetailController(),
    );
    Get.lazyPut(() => ProcService(Get.find<DioService>()));
  }
}
