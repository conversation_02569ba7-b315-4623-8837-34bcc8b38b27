// import 'package:flutter/material.dart';
// import 'package:gap/gap.dart';
// import 'package:attp_2024/core/configs/theme/app_colors.dart';
// import 'package:attp_2024/core/ui/widgets/customCachedImage/customCachedImage.dart';
// import 'package:attp_2024/core/utils/color_utils.dart';
// import 'package:attp_2024/features/tra_cuu/co_so_du_dieu_kien/presentation/widgets/label_value_row.dart';

// class CommonListCard extends StatelessWidget {
//   final String? title;
//   final String? logoUrl;
//   final List<LabelValuePair> labelValueList;
//   final String? badgeText;
//   final String? badgeColor;
//   final String? secondaryBadgeText;
//   final String? secondaryBadgeColor;
//   final VoidCallback? onTap;

//   const CommonListCard({
//     Key? key,
//     required this.title,
//     required this.logoUrl,
//     required this.labelValueList,
//     this.badgeText,
//     this.badgeColor,
//     this.secondaryBadgeText,
//     this.secondaryBadgeColor,
//     this.onTap,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return GestureDetector(
//       onTap: onTap,
//       child: Card(
//         elevation: 2,
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(5.0),
//         ),
//         margin: const EdgeInsets.symmetric(vertical: 8.0),
//         child: Stack(
//           children: [
//             if (badgeText != null && badgeColor != null)
//               _buildBadge(
//                 text: badgeText!,
//                 color: badgeColor!,
//                 alignment: Alignment.topRight,
//                 borderRadius: const BorderRadius.only(
//                   bottomLeft: Radius.circular(5.0),
//                 ),
//               ),
//             if (secondaryBadgeText != null && secondaryBadgeColor != null)
//               _buildBadge(
//                 text: secondaryBadgeText!,
//                 color: secondaryBadgeColor!,
//                 alignment: Alignment.bottomLeft,
//                 borderRadius: const BorderRadius.only(
//                   bottomRight: Radius.circular(5.0),
//                 ),
//               ),
//             Padding(
//               padding:
//                   const EdgeInsets.only(top: 30, left: 5, right: 5, bottom: 10),
//               child: Row(
//                 crossAxisAlignment: CrossAxisAlignment.center,
//                 children: [
//                   Column(
//                     children: [
//                       ClipRRect(
//                         borderRadius: BorderRadius.circular(8.0),
//                         child: CustomCachedImage(
//                           width: 100,
//                           height: 130,
//                           imageUrl: logoUrl ?? '',
//                           defaultImage: 'https://via.placeholder.com/150',
//                         ),
//                       ),
//                       const Gap(5.0),
//                       if (badgeText != null)
//                         Container(
//                           padding: const EdgeInsets.symmetric(
//                               horizontal: 8.0, vertical: 5.0),
//                           decoration: BoxDecoration(
//                             color: AppColors.primary,
//                             borderRadius: BorderRadius.circular(14.0),
//                           ),
//                           child: Text(
//                             badgeText!,
//                             style: const TextStyle(
//                               fontSize: 10,
//                               fontWeight: FontWeight.bold,
//                               color: Colors.white,
//                             ),
//                           ),
//                         ),
//                     ],
//                   ),
//                   const Gap(10.0),
//                   Expanded(
//                     child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         Text(
//                           title ?? '',
//                           style: const TextStyle(
//                             fontWeight: FontWeight.bold,
//                             fontSize: 14,
//                           ),
//                         ),
//                         const Divider(height: 16.0, thickness: 1.0),
//                         ...labelValueList.map(
//                           (lv) => LabelValueRow(
//                             label: lv.label,
//                             value: lv.value,
//                             labelFontWeight: FontWeight.bold,
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget _buildBadge({
//     required String text,
//     required String color,
//     required Alignment alignment,
//     BorderRadius? borderRadius,
//   }) {
//     return Align(
//       alignment: alignment,
//       child: Container(
//         padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
//         decoration: BoxDecoration(
//           color: hexToColor(color),
//           borderRadius: borderRadius ?? BorderRadius.circular(5.0),
//         ),
//         child: Text(
//           text,
//           style: const TextStyle(
//             color: Colors.white,
//             fontSize: 12,
//             fontWeight: FontWeight.bold,
//           ),
//         ),
//       ),
//     );
//   }
// }

// class LabelValuePair {
//   final String label;
//   final String value;

//   LabelValuePair({required this.label, required this.value});
// }

// import 'package:flutter/material.dart';
// import 'package:gap/gap.dart';
// import 'package:attp_2024/core/configs/theme/app_colors.dart';
// import 'package:attp_2024/core/ui/widgets/customCachedImage/customCachedImage.dart';
// import 'package:attp_2024/core/utils/color_utils.dart';
// import 'package:attp_2024/features/tra_cuu/co_so_du_dieu_kien/presentation/widgets/label_value_row.dart';

// class CommonListCard extends StatelessWidget {
//   final String? title;
//   final String? logoUrl;
//   final List<LabelValuePair> labelValueList;
//   final String? badgeText;
//   final String? badgeColor;
//   final String? secondaryBadgeText;
//   final String? secondaryBadgeColor;
//   final VoidCallback? onTap;
//   final double? height;

//   const CommonListCard({
//     super.key,
//     required this.title,
//     required this.logoUrl,
//     required this.labelValueList,
//     this.badgeText,
//     this.badgeColor,
//     this.secondaryBadgeText,
//     this.secondaryBadgeColor,
//     this.onTap,
//     this.height,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return GestureDetector(
//       onTap: onTap,
//       child: Card(
//         elevation: 2,
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(5.0),
//         ),
//         margin: const EdgeInsets.symmetric(vertical: 8.0),
//         child: SizedBox(
//           height: height, // Use the provided height or adapt to content
//           child: Stack(
//             children: [
//               if (badgeText != null && badgeColor != null)
//                 _buildBadge(
//                   text: badgeText!,
//                   color: badgeColor!,
//                   alignment: Alignment.topRight,
//                   borderRadius: const BorderRadius.only(
//                     bottomLeft: Radius.circular(5.0),
//                   ),
//                 ),
//               if (secondaryBadgeText != null && secondaryBadgeColor != null)
//                 _buildBadge(
//                   text: secondaryBadgeText!,
//                   color: secondaryBadgeColor!,
//                   alignment: Alignment.bottomLeft,
//                   borderRadius: const BorderRadius.only(
//                     bottomRight: Radius.circular(5.0),
//                   ),
//                 ),
//               Padding(
//                 padding: const EdgeInsets.only(
//                     top: 30, left: 5, right: 5, bottom: 10),
//                 child: Row(
//                   crossAxisAlignment: CrossAxisAlignment.center,
//                   children: [
//                     Column(
//                       children: [
//                         ClipRRect(
//                           borderRadius: BorderRadius.circular(8.0),
//                           child: CustomCachedImage(
//                             width: 100,
//                             height: 130,
//                             imageUrl: logoUrl ?? '',
//                             defaultImage: 'https://via.placeholder.com/150',
//                           ),
//                         ),
//                         const Gap(5.0),
//                         if (badgeText != null)
//                           Container(
//                             padding: const EdgeInsets.symmetric(
//                                 horizontal: 8.0, vertical: 5.0),
//                             decoration: BoxDecoration(
//                               color: AppColors.primary,
//                               borderRadius: BorderRadius.circular(14.0),
//                             ),
//                             child: Text(
//                               badgeText!,
//                               style: const TextStyle(
//                                 fontSize: 10,
//                                 fontWeight: FontWeight.bold,
//                                 color: Colors.white,
//                               ),
//                             ),
//                           ),
//                       ],
//                     ),
//                     const Gap(10.0),
//                     Expanded(
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           Text(
//                             title ?? '',
//                             style: const TextStyle(
//                               fontWeight: FontWeight.bold,
//                               fontSize: 14,
//                             ),
//                           ),
//                           const Divider(height: 16.0, thickness: 1.0),
//                           ...labelValueList.map(
//                             (lv) => LabelValueRow(
//                               label: lv.label,
//                               value: lv.value,
//                               labelFontWeight: FontWeight.bold,
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildBadge({
//     required String text,
//     required String color,
//     required Alignment alignment,
//     BorderRadius? borderRadius,
//   }) {
//     return Align(
//       alignment: alignment,
//       child: Container(
//         padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
//         decoration: BoxDecoration(
//           color: hexToColor(color),
//           borderRadius: borderRadius ?? BorderRadius.circular(5.0),
//         ),
//         child: Text(
//           text,
//           style: const TextStyle(
//             color: Colors.white,
//             fontSize: 12,
//             fontWeight: FontWeight.bold,
//           ),
//         ),
//       ),
//     );
//   }
// }

// class LabelValuePair {
//   final String label;
//   final String value;

//   LabelValuePair({required this.label, required this.value});
// }

// import 'package:flutter/material.dart';
// import 'package:gap/gap.dart';
// import 'package:attp_2024/core/configs/theme/app_colors.dart';
// import 'package:attp_2024/core/ui/widgets/customCachedImage/customCachedImage.dart';
// import 'package:attp_2024/core/utils/color_utils.dart';
// import 'package:attp_2024/features/tra_cuu/co_so_du_dieu_kien/presentation/widgets/label_value_row.dart';

// class CommonListCard extends StatelessWidget {
//   final String? title;
//   final String? logoUrl;
//   final List<LabelValuePair> labelValueList;
//   final String? badgeText;
//   final String? badgeColor;
//   final String? secondaryBadgeText;
//   final String? secondaryBadgeColor;
//   final VoidCallback? onTap;
//   final double? height;
//   final double imageWidth; // Chiều rộng ảnh
//   final double imageHeight;

//   const CommonListCard({
//     super.key,
//     required this.title,
//     required this.logoUrl,
//     required this.labelValueList,
//     this.badgeText,
//     this.badgeColor,
//     this.secondaryBadgeText,
//     this.secondaryBadgeColor,
//     this.onTap,
//     this.height,
//     this.imageWidth = 100, // Giá trị mặc định cho chiều rộng ảnh
//     this.imageHeight = 130,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return GestureDetector(
//       onTap: onTap,
//       child: Card(
//         elevation: 2,
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(5.0),
//         ),
//         margin: const EdgeInsets.symmetric(vertical: 8.0),
//         child: SizedBox(
//           height: height, // Use the provided height or adapt to content
//           child: Stack(
//             children: [
//               if (badgeText != null && badgeColor != null)
//                 _buildBadge(
//                   text: badgeText!,
//                   color: badgeColor!,
//                   alignment: Alignment.topRight,
//                   borderRadius: const BorderRadius.only(
//                     bottomLeft: Radius.circular(5.0),
//                   ),
//                 ),
//               if (secondaryBadgeText != null && secondaryBadgeColor != null)
//                 _buildBadge(
//                   text: secondaryBadgeText!,
//                   color: secondaryBadgeColor!,
//                   alignment: Alignment.bottomLeft,
//                   borderRadius: const BorderRadius.only(
//                     bottomRight: Radius.circular(5.0),
//                   ),
//                 ),
//               Padding(
//                 padding: EdgeInsets.only(
//                   top: (badgeText != null || secondaryBadgeText != null)
//                       ? 30
//                       : 10, // Conditional padding
//                   left: 5,
//                   right: 5,
//                   bottom: 10,
//                 ),
//                 child: Row(
//                   crossAxisAlignment: CrossAxisAlignment.center,
//                   children: [
//                     Column(
//                       children: [
//                         ClipRRect(
//                           borderRadius: BorderRadius.circular(8.0),
//                           child: CustomCachedImage(
//                             width: imageWidth, // Sử dụng giá trị tùy chỉnh
//                             height: imageHeight,
//                             imageUrl: logoUrl ?? '',
//                             defaultImage: 'https://via.placeholder.com/150',
//                           ),
//                         ),
//                         const Gap(5.0),
//                         if (badgeText != null)
//                           Container(
//                             padding: const EdgeInsets.symmetric(
//                                 horizontal: 8.0, vertical: 5.0),
//                             decoration: BoxDecoration(
//                               color: AppColors.primary,
//                               borderRadius: BorderRadius.circular(14.0),
//                             ),
//                             child: Text(
//                               badgeText!,
//                               style: const TextStyle(
//                                 fontSize: 10,
//                                 fontWeight: FontWeight.bold,
//                                 color: Colors.white,
//                               ),
//                             ),
//                           ),
//                       ],
//                     ),
//                     const Gap(10.0),
//                     Expanded(
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           Text(
//                             title ?? '',
//                             style: const TextStyle(
//                               fontWeight: FontWeight.bold,
//                               fontSize: 14,
//                             ),
//                           ),
//                           const Divider(height: 16.0, thickness: 1.0),
//                           ...labelValueList.map(
//                             (lv) => LabelValueRow(
//                               label: lv.label,
//                               value: lv.value,
//                               labelFontWeight: FontWeight.bold,
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildBadge({
//     required String text,
//     required String color,
//     required Alignment alignment,
//     BorderRadius? borderRadius,
//   }) {
//     return Align(
//       alignment: alignment,
//       child: Container(
//         padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
//         decoration: BoxDecoration(
//           color: hexToColor(color),
//           borderRadius: borderRadius ?? BorderRadius.circular(5.0),
//         ),
//         child: Text(
//           text,
//           style: const TextStyle(
//             color: Colors.white,
//             fontSize: 12,
//             fontWeight: FontWeight.bold,
//           ),
//         ),
//       ),
//     );
//   }
// }

// class LabelValuePair {
//   final String label;
//   final String value;

//   LabelValuePair({required this.label, required this.value});
// }

// import 'package:flutter/material.dart';
// import 'package:gap/gap.dart';
// import 'package:attp_2024/core/configs/theme/app_colors.dart';
// import 'package:attp_2024/core/ui/widgets/customCachedImage/customCachedImage.dart';
// import 'package:attp_2024/core/utils/color_utils.dart';
// import 'package:attp_2024/features/tra_cuu/co_so_du_dieu_kien/presentation/widgets/label_value_row.dart';

// class CommonListCard extends StatelessWidget {
//   final String? title;
//   final String? logoUrl;
//   final List<LabelValuePair> labelValueList;
//   final String? badgeText;
//   final String? badgeColor;
//   final String? secondaryBadgeText;
//   final String? secondaryBadgeColor;
//   final VoidCallback? onTap;
//   final double? height;
//   final double imageWidth;
//   final double imageHeight;
//   final bool showImage;

//   const CommonListCard({
//     super.key,
//     required this.title,
//     required this.logoUrl,
//     required this.labelValueList,
//     this.badgeText,
//     this.badgeColor,
//     this.secondaryBadgeText,
//     this.secondaryBadgeColor,
//     this.onTap,
//     this.height,
//     this.imageWidth = 100,
//     this.imageHeight = 130,
//     this.showImage = true,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return GestureDetector(
//       onTap: onTap,
//       child: Card(
//         elevation: 2,
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(5.0),
//         ),
//         margin: const EdgeInsets.symmetric(vertical: 8.0),
//         child: SizedBox(
//           height: height,
//           child: Stack(
//             children: [
//               if (badgeText != null && badgeColor != null)
//                 _buildBadge(
//                   text: badgeText!,
//                   color: badgeColor!,
//                   alignment: Alignment.topRight,
//                   borderRadius: const BorderRadius.only(
//                     bottomLeft: Radius.circular(5.0),
//                     topRight: Radius.circular(5.0),
//                   ),
//                 ),
//               if (secondaryBadgeText != null && secondaryBadgeColor != null)
//                 _buildBadge(
//                   text: secondaryBadgeText!,
//                   color: secondaryBadgeColor!,
//                   alignment: Alignment.bottomLeft,
//                   borderRadius: const BorderRadius.only(
//                     bottomRight: Radius.circular(5.0),
//                     topLeft: Radius.circular(5.0),
//                   ),
//                 ),
//               Padding(
//                 padding: EdgeInsets.symmetric(
//                   vertical:
//                       badgeText != null || secondaryBadgeText != null ? 30 : 10,
//                   horizontal: 5,
//                 ),
//                 child: Row(
//                   crossAxisAlignment: CrossAxisAlignment.center,
//                   children: [
//                     if (showImage) _buildImageOrPlaceholder(),
//                     const Gap(10.0),
//                     _buildContent(),
//                   ],
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildImageOrPlaceholder() {
//     return Column(
//       children: [
//         ClipRRect(
//           borderRadius: BorderRadius.circular(8.0),
//           child: CustomCachedImage(
//             width: imageWidth,
//             height: imageHeight,
//             imageUrl: logoUrl ?? '',
//             defaultImage: 'https://via.placeholder.com/150',
//           ),
//         ),
//         const Gap(5.0),
//         if (badgeText != null)
//           Container(
//             padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 5.0),
//             decoration: BoxDecoration(
//               color: AppColors.primary,
//               borderRadius: BorderRadius.circular(14.0),
//             ),
//             child: Text(
//               badgeText!,
//               style: const TextStyle(
//                 fontSize: 10,
//                 fontWeight: FontWeight.bold,
//                 color: Colors.white,
//               ),
//             ),
//           ),
//       ],
//     );
//   }

//   Widget _buildContent() {
//     return Expanded(
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Text(
//             title ?? '',
//             style: const TextStyle(
//               fontWeight: FontWeight.bold,
//               fontSize: 14,
//             ),
//           ),
//           const Divider(height: 16.0, thickness: .4),
//           ...labelValueList.map(
//             (lv) => LabelValueRow(
//               label: lv.label,
//               value: lv.value,
//               labelFontWeight: FontWeight.bold,
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildBadge({
//     required String text,
//     required String color,
//     required Alignment alignment,
//     BorderRadius? borderRadius,
//   }) {
//     return Align(
//       alignment: alignment,
//       child: Container(
//         padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
//         decoration: BoxDecoration(
//           color: hexToColor(color),
//           borderRadius: borderRadius ?? BorderRadius.circular(5.0),
//         ),
//         child: Text(
//           text,
//           style: const TextStyle(
//             color: Colors.white,
//             fontSize: 12,
//             fontWeight: FontWeight.bold,
//           ),
//         ),
//       ),
//     );
//   }
// }

// class LabelValuePair {
//   final String label;
//   final String value;

//   LabelValuePair({required this.label, required this.value});
// }
import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/customCachedImage/customCachedImage.dart';
import 'package:attp_2024/core/utils/color_utils.dart';
import 'package:attp_2024/features/tra_cuu/widgets/label_value_row.dart';

class CommonListCard extends StatelessWidget {
  final String? title;
  final IconData? titleIcon; // Icon cho tiêu đề
  final String? logoUrl;
  final List<LabelValuePair> labelValueList;
  final String? badgeText;
  final String? badgeColor;
  final String? secondaryBadgeText;
  final String? secondaryBadgeColor;
  final VoidCallback? onTap;
  final double? height;
  final double imageWidth;
  final double imageHeight;
  final bool showImage;

  const CommonListCard({
    super.key,
    required this.title,
    this.titleIcon, // Thêm titleIcon vào constructor
    required this.logoUrl,
    required this.labelValueList,
    this.badgeText,
    this.badgeColor,
    this.secondaryBadgeText,
    this.secondaryBadgeColor,
    this.onTap,
    this.height,
    this.imageWidth = 100,
    this.imageHeight = 130,
    this.showImage = true,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(5.0),
        ),
        margin: const EdgeInsets.symmetric(vertical: 8.0),
        child: SizedBox(
          height: height,
          child: Stack(
            children: [
              if (badgeText != null && badgeColor != null)
                _buildBadge(
                  text: badgeText!,
                  color: badgeColor!,
                  alignment: Alignment.topRight,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(5.0),
                    topRight: Radius.circular(5.0),
                  ),
                ),
              if (secondaryBadgeText != null && secondaryBadgeColor != null)
                _buildBadge(
                  text: secondaryBadgeText!,
                  color: secondaryBadgeColor!,
                  alignment: Alignment.bottomLeft,
                  borderRadius: const BorderRadius.only(
                    bottomRight: Radius.circular(5.0),
                    topLeft: Radius.circular(5.0),
                  ),
                ),
              Padding(
                padding: EdgeInsets.symmetric(
                  vertical:
                      badgeText != null || secondaryBadgeText != null ? 30 : 10,
                  horizontal: 5,
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    if (showImage) _buildImageOrPlaceholder(),
                    const Gap(10.0),
                    _buildContent(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageOrPlaceholder() {
    return Column(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(8.0),
          child: CustomCachedImage(
            width: imageWidth,
            height: imageHeight,
            imageUrl: logoUrl ?? AppImageString.imageNotFount,
            defaultImage: AppImageString.imageNotFount,
          ),
        ),
        const Gap(5.0),
        if (badgeText != null)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 5.0),
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(14.0),
            ),
            child: Text(
              badgeText!,
              style: const TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildContent() {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTitleWithIcon(), // Gọi hàm hiển thị tiêu đề với icon
          const Divider(height: 16.0, thickness: .4),
          ...labelValueList.map(
            (lv) => LabelValueRow(
              label: lv.label,
              value: lv.value,
              labelFontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTitleWithIcon() {
    return Row(
      children: [
        if (titleIcon != null)
          Icon(
            titleIcon, // Hiển thị icon
            size: 18,
            color: AppColors.primary,
          ),
        if (titleIcon != null)
          const Gap(5.0), // Khoảng cách giữa icon và tiêu đề
        Expanded(
          child: Text(
            title ?? '',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBadge({
    required String text,
    required String color,
    required Alignment alignment,
    BorderRadius? borderRadius,
  }) {
    return Align(
      alignment: alignment,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
        decoration: BoxDecoration(
          color: hexToColor(color),
          borderRadius: borderRadius ?? BorderRadius.circular(5.0),
        ),
        child: Text(
          text,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}

class LabelValuePair {
  final String label;
  final String value;

  LabelValuePair({required this.label, required this.value});
}
