import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/ui/widgets/webview/webview_page.dart';
import 'package:attp_2024/core/utils/date_time.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/presentation/controller/coso_dudk_detail_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/utils/convert_text.dart';
import '../../../../../../core/configs/contents/app_content.dart';
import '../../controller/giay_xac_nhan_kien_thuc_detail_controller.dart';
import '../image_carousel.dart';

class ThongTinChung extends StatelessWidget {
  final GiayXacNhanKienThucDetailController controller;
  const ThongTinChung({super.key, required this.controller});
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading.value) {
        return _buildShimmerDetails();
      }
      if (controller.giayxnktDetail.isEmpty) {
        return Center(
          child: Text(
            "Không có thông tin cơ sở.",
            style: TextStyle(fontSize: 16, color: AppColors.primary),
          ),
        );
      }
      final giayxnkt = controller.giayxnktDetail.first;
      const defaultText = "Đang cập nhật";
      return SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Thông tin cơ sở sản xuất kinh doanh",
                style: TextStyle(
                    fontSize: AppDimens.mediumText,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary),
              ),
              const Gap(10),
              _buildInfoRow("Tên cơ sở:", giayxnkt.TenCoSoSXKD ?? defaultText,
                  icon: Icons.business),
              _buildInfoRow("Số ĐKKD:", giayxnkt.SoGPKD ?? defaultText,
                  icon: Icons.document_scanner),
              _buildInfoRow(
                "Ngày cấp:",
                giayxnkt.NgayCapGPKD != null
                    ? giayxnkt.NgayCapGPKD!
                    : defaultText,
                icon: Icons.calendar_today,
              ),
              _buildInfoRow(
                  "Cơ quan cấp:", giayxnkt.CoQuanCapGPKD ?? defaultText,
                  icon: Icons.apartment),
              _buildInfoRow(
                  "Số điện thoại:", giayxnkt.SoDienThoai ?? defaultText,
                  icon: Icons.phone),
              _buildInfoRow("Email:", giayxnkt.EmailCS ?? defaultText,
                  icon: Icons.email),
              _buildInfoRow("Địa chỉ cơ sở:", giayxnkt.DiaChiCS ?? defaultText,
                  icon: Icons.location_on),
              _buildInfoRow(
                  "Loại hình cơ sở:", giayxnkt.LoaiHinhCoSo ?? defaultText,
                  icon: Icons.category),
              // _buildInfoRow("Trạng thái:", giayxnkt.TrangThai_BanHanh ?? defaultText,
              //     icon: Icons.info),

              const Divider(),
              const Gap(8),
              Text(
                "Thông tin chung",
                style: TextStyle(
                    fontSize: AppDimens.mediumText,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary),
              ),
              const Gap(10),
              _buildInfoRow("Số văn bản:", giayxnkt.SoVanBan ?? defaultText,
                  icon: Icons.description),
              _buildInfoRow(
                "Ngày ký:",
                giayxnkt.NgayKy != null ? giayxnkt.NgayKy! : defaultText,
                icon: Icons.calendar_today,
              ),
              _buildInfoRow("Người ký:", giayxnkt.NguoiKy ?? defaultText,
                  icon: Icons.person),
              _buildInfoRow("Chức vụ:", giayxnkt.ChucVu ?? defaultText,
                  icon: Icons.work),
              _buildInfoRow(
                "Ngày hết hạn:",
                giayxnkt.NgayHetHan != null
                    ? giayxnkt.NgayHetHan!
                    : defaultText,
                icon: Icons.timer_off,
              ),
              _buildInfoRow(
                  "Cơ quan ban hành:", giayxnkt.CoQuanBanHanh ?? defaultText,
                  icon: Icons.account_balance),
              // _buildInfoRow("Đính kèm:", giayxnkt.CoQuanBanHanh ?? defaultText,
              //     icon: CupertinoIcons.paperclip),
              TextButton.icon(
                onPressed: () {
                  print("giayxnkt.DinhKem ${giayxnkt.DinhKem}");
                  String baseUrl = controller.userAccessModel?.siteURL ?? "";
                  String path = giayxnkt.DinhKem ?? "";

                  if (giayxnkt.DinhKem == null || giayxnkt.DinhKem == "") {
                    Fluttertoast.showToast(
                      msg: "Không có file đính kèm!",
                      toastLength: Toast.LENGTH_SHORT,
                      gravity: ToastGravity.BOTTOM,
                      textColor: Colors.white,
                      fontSize: 14.0,
                    );
                    return;
                  }

                  String finalUrl = "$baseUrl$path";
                  if (finalUrl.endsWith('*')) {
                    finalUrl = finalUrl.substring(0, finalUrl.length - 1);
                  }

                  Get.to(() => WebViewPage(
                        title: AppContent.appTitleWebview,
                        initialUrl: finalUrl,
                      ));
                },
                label: const Text(
                  "Đính kèm",
                ),
                icon: Icon(
                  Icons.attach_file_rounded,
                  size: 18.sp,
                  color: AppColors.primary,
                ),
                style: TextButton.styleFrom(
                  padding: EdgeInsets.zero,
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildInfoRow(String label, String value, {IconData? icon}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (icon != null)
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: Icon(
                icon,
                size: 20,
                color: AppColors.primary,
              ),
            ),
          SizedBox(
            width: 140, // Đặt chiều rộng cố định cho label
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(height: 1.5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShimmerDetails() {
    return SizedBox(
      height: 80.h,
      child: ListView.builder(
        itemCount: 5,
        itemBuilder: (context, index) {
          return Padding(
            padding: const EdgeInsets.all(8.0),
            child: Container(
              height: 20,
              color: Colors.grey[300],
            ),
          );
        },
      ),
    );
  }
}
