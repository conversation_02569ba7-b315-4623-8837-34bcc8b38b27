import 'package:attp_2024/core/data/models/user_access_model.dart';
import 'package:attp_2024/core/services/user_use_case.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../model/giay_xac_nhan_kien_thuc_model.dart';
import '../../model/giayxnkt_detail_model.dart';
import '../../model/giayxnkt_nhanvien_model.dart';
import '../../model/giayxnkt_tailieu_model.dart';
import '../service/giayxnkt_service.dart';

class GiayXacNhanKienThucDetailController extends GetxController
    with GetSingleTickerProviderStateMixin {
  final giayxnktService _service = giayxnktService();
  final log = Logger();

  var isLoading = false.obs;
  var giayxnktDetail = <GiayXacNhanKienThucDetailModel>[].obs;
  var nhanVienList = <GiayxnktNhanvienModel>[].obs;
  var tailieuList = <GiayxnktTailieuModel>[].obs;

  late TabController tabController;
  final PageController pageController = PageController();
  UserAccessModel? userAccessModel;

  @override
  void onInit() {
    super.onInit();
    // final String? id = Get.arguments;
    loadInfoProfile();
    final GiayXacNhanKienThucModel args = Get.arguments as GiayXacNhanKienThucModel;
    tabController = TabController(length: 3, vsync: this);
    tabController.addListener(() {
      if (tabController.indexIsChanging) {
        pageController.jumpToPage(tabController.index);
      }
    });
    if (args.GXNKienThucATTPID != []) {
      fetchGiayXNKTDetails(args.GXNKienThucATTPID!);
      fetchGridDetails(args.CoSoSXKDID!);
    } else {
      log.w("No valid ID passed to the detail page or ID is null");
    }
  }

  /// Fetch all required details concurrently
  Future<void> fetchGiayXNKTDetails(String id) async {
    try {
      isLoading(true);

      final results = await Future.wait([
        _service.fetchGiayXnktByID(id),

      ]);
      giayxnktDetail.value = results[0];

    } catch (e) {
      log.e("Error fetching details: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> fetchGridDetails(String id) async {
    try {
      isLoading(true);

      final results = await Future.wait([
        _service.fetchNhanVien(id),
        _service.fetchCoSoSXKDTL(id),

      ]);
      nhanVienList.value = results[0] as List<GiayxnktNhanvienModel>;
      tailieuList.value = results[1] as List<GiayxnktTailieuModel>;

    } catch (e) {
      log.e("Error fetching details: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> loadInfoProfile() async {
    userAccessModel = await UserUseCase.getUser();
    update(["bodyID"]);
  }
}
