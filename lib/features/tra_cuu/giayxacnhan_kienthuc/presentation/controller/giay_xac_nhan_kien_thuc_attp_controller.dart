import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:attp_2024/core/configs/contanst/proc_constants.dart';
import 'package:attp_2024/core/data/api/services/proc/proc_service.dart';
import 'package:attp_2024/core/services/user_use_case.dart';
import 'package:attp_2024/features/tra_cuu/giayxacnhan_kienthuc/model/giay_xac_nhan_kien_thuc_model.dart';
import '../../../../../core/data/models/user_access_model.dart';

class GiayXacNhanKienThucATTPController extends GetxController {
  final ProcService _procService = Get.find<ProcService>();
  var isLoading = false.obs;
  var searchResults = <GiayXacNhanKienThucModel>[].obs;
  var filteredResults = <GiayXacNhanKienThucModel>[].obs;
  var searchQuery = "".obs;
  final log = Logger();

  static const String PROC_NAME =
      "Proc_Mobile_GetAll_GXNKienThucATTP_Pagination";
  UserAccessModel? userAccessModel;
  var donViId = '';
  var userId = '';
  var userGroup = ''.obs;

  var provinces = [].obs;
  var districts = [].obs;
  var communes = [].obs;
  var villages = [].obs;
  var loaiHinhCSs = [].obs;
  var tinhTrangBanHanh = [].obs;
  var provinceFilter = ''.obs;
  var districtFilter = ''.obs;
  var communeFilter = ''.obs;
  var villageFilter = ''.obs;
  var loaihinhCsId = ''.obs;
  var tuNgay = ''.obs;
  var denNgay = ''.obs;
  var tinhTrangBanHanhId = ''.obs;
  var isLoadingProvinces = false.obs;
  var isLoadingDistricts = false.obs;
  var isLoadingCommunes = false.obs;
  var isLoadingVillages = false.obs;
  var isLoadingLoaiHinhCS = false.obs;
  var isLoadingtinhTrangBanHanh = false.obs;

  Rx<DateTime> startDate = DateTime(DateTime.now().year, 1, 1).obs;
  Rx<DateTime> endDate = DateTime.now().obs;

  var isLoadingdefaultTinhHuyenXa = false.obs;
  var defaultTinhHuyenXa = [].obs;

  // Thêm các biến cho phân trang
  var currentPage = 1.obs;
  var pageSize = 10.obs;
  var hasMoreData = true.obs;
  var isLoadingMore = false.obs;

  void clearProvinceFilters() {
    provinceFilter.value = '';
    districtFilter.value = '';
    communeFilter.value = '';
    villageFilter.value = '';
    districts.clear();
    communes.clear();
    villages.clear();
  }

  void clearDistrictFilters() {
    districtFilter.value = '';
    communeFilter.value = '';
    villageFilter.value = '';
    communes.clear();
    villages.clear();
  }

  void clearCommuneFilters() {
    communeFilter.value = '';
    villageFilter.value = '';
    villages.clear();
  }

  @override
  Future<void> onInit() async {
    super.onInit();

    fetchAllProvinces();
    await loadInfoProfile();
    await fetchDonViByID();
    fetchSearchResults(""); // Tải dữ liệu ban đầu.
    fetchAllLoaiHinhCS();
    fetchAllTinhTrangCapGCN();
  }

  // Future<String> _getDonViID() async {
  //   final userId = await UserUseCase.getUser();
  //   return userId!.donViID;
  // }

  // Future<String> _getUserID() async {
  //   final userId = await UserUseCase.getUser();
  //   return userId!.userID;
  // }

  Future<void> fetchSearchResults(String query) async {
    if (isLoading.value) return;

    isLoading.value = true;
    currentPage.value = 1; // Reset về trang đầu tiên
    hasMoreData.value = true; // Reset trạng thái còn dữ liệu

    try {
      final response =
          await _procService.callProc(PROC_NAME, _buildRequestParams(query));

      searchResults.value = response.isNotEmpty
          ? response
              .map<GiayXacNhanKienThucModel>((item) =>
                  GiayXacNhanKienThucModel.fromJson(
                      item as Map<String, dynamic>))
              .toList()
          : [];

      filteredResults.value = searchResults;

      // Kiểm tra nếu không đủ dữ liệu cho một trang đầy đủ
      if (response.length < pageSize.value) {
        hasMoreData.value = false;
      }
    } catch (e) {
      log.e("Error fetching search results: $e");
      searchResults.clear();
      filteredResults.clear();
      hasMoreData.value = false;
    } finally {
      isLoading.value = false;
    }
  }

  List<Map<String, dynamic>> _buildRequestParams(String query) {
    return [
      {
        "Type": "guid",
        "Name": "TinhID",
        "Value": provinceFilter.value.isEmpty
            ? "00000000-0000-0000-0000-000000000000"
            : provinceFilter.value
      },
      {
        "Type": "guid",
        "Name": "HuyenID",
        "Value": districtFilter.value.isEmpty
            ? "00000000-0000-0000-0000-000000000000"
            : districtFilter.value
      },
      {
        "Type": "guid",
        "Name": "XaID",
        "Value": communeFilter.value.isEmpty
            ? "00000000-0000-0000-0000-000000000000"
            : communeFilter.value
      },
      {
        "Type": "guid",
        "Name": "ThonID",
        "Value": villageFilter.value.isEmpty
            ? "00000000-0000-0000-0000-000000000000"
            : villageFilter.value
      },
      {
        "Type": "guid",
        "Name": "LoaiHinhCoSoID",
        "Value": "00000000-0000-0000-0000-000000000000"
      },
      {"Type": "string", "Name": "TinhTrangBanHanh", "Value": null},
      {
        "Type": "string",
        "Name": "TuNgay_TimKiem",
        "Value": DateFormat('yyyy-MM-dd').format(startDate.value)
      },
      {
        "Type": "string",
        "Name": "DenNgay_TimKiem",
        "Value": DateFormat('yyyy-MM-dd').format(endDate.value)
      },
      {"Type": "string", "Name": "SapXep", "Value": "NgayKy"},
      {"Type": "string", "Name": "TuKhoa", "Value": query},
      {"Type": "guid", "Name": "DonViID", "Value": donViId},
      {"Type": "guid", "Name": "UserID", "Value": userId},
      {"Type": "int", "Name": "PageNumber", "Value": currentPage.value},
      {"Type": "int", "Name": "PageSize", "Value": pageSize.value},
    ];
  }

  Future<void> fetchAllProvinces() async {
    isLoadingProvinces.value = true;
    final List<Map<String, dynamic>> body = [];
    try {
      List<dynamic> provincesResponse =
          await _procService.callProc(ProcConstants.getAllDiaBanHCTinh, body);
      List<Map<String, dynamic>> mappedTinhResponse =
          provincesResponse.cast<Map<String, dynamic>>();

      provinces.assignAll(
        mappedTinhResponse.map((Map<String, dynamic> tinhs) {
          return {
            "value": tinhs['DiaBanHCID'],
            "display": tinhs['TenDiaBan'],
          };
        }).toList(),
      );
    } finally {
      isLoadingProvinces.value = false;
    }
  }

  Future<void> fetchAllDistricts({required String tinhID}) async {
    isLoadingDistricts.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "TinhID", "type": "guid", "value": tinhID},
    ];
    try {
      List<dynamic> districtsResponse =
          await _procService.callProc(ProcConstants.getAllDiaBanHCHuyen, body);
      List<Map<String, dynamic>> mappedHuyenResponse =
          districtsResponse.cast<Map<String, dynamic>>();

      districts.assignAll(
        mappedHuyenResponse.map((Map<String, dynamic> huyens) {
          return {
            "value": huyens['DiaBanHCID'],
            "display": huyens['TenDiaBan'],
          };
        }).toList(),
      );
    } finally {
      isLoadingDistricts.value = false;
    }
  }

  Future<void> fetchAllCommunes({required String huyenID}) async {
    isLoadingCommunes.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "HuyenID", "type": "guid", "value": huyenID},
    ];
    try {
      List<dynamic> communesResponse =
          await _procService.callProc(ProcConstants.getAllDiaBanHCXa, body);
      List<Map<String, dynamic>> mappedXaResponse =
          communesResponse.cast<Map<String, dynamic>>();

      communes.assignAll(
        mappedXaResponse.map((Map<String, dynamic> xas) {
          return {
            "value": xas['DiaBanHCID'],
            "display": xas['TenDiaBan'],
          };
        }).toList(),
      );
    } finally {
      isLoadingCommunes.value = false;
    }
  }

  Future<void> fetchAllVillages({required String xaID}) async {
    isLoadingVillages.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "xaID", "type": "guid", "value": xaID},
    ];
    try {
      List<dynamic> villagesResponse =
          await _procService.callProc(ProcConstants.getAllDiaBanHCThon, body);
      List<Map<String, dynamic>> mappedThonResponse =
          villagesResponse.cast<Map<String, dynamic>>();

      villages.assignAll(
        mappedThonResponse.map((Map<String, dynamic> thons) {
          return {
            "value": thons['DiaBanHCID'],
            "display": thons['TenDiaBan'],
          };
        }).toList(),
      );
    } finally {
      isLoadingVillages.value = false;
    }
  }

  Future<void> fetchDonViByID() async {
    isLoadingdefaultTinhHuyenXa.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "ID", "type": "guid", "value": donViId}
    ];
    try {
      List<dynamic> res =
          await _procService.callProc('Proc_Mobile_GetDonViByID', body);
      List<Map<String, dynamic>> mappedResponse =
          res.cast<Map<String, dynamic>>();
      defaultTinhHuyenXa.assignAll(
        mappedResponse.map((Map<String, dynamic> item) {
          return {
            "TinhID_": item['TinhID_'],
            "HuyenID_": item['HuyenID_'],
            "XaID_": item['XaID_'],
            "ThonID_": item['ThonID_'],
          };
        }).toList(),
      );
      log.i(defaultTinhHuyenXa.toString());
      setDefaultTinhHuyenXa();
    } finally {
      isLoadingdefaultTinhHuyenXa.value = false;
    }
  }

  void setDefaultTinhHuyenXa() {
    fetchAllProvinces();
    fetchAllDistricts(tinhID: defaultTinhHuyenXa.first['TinhID_']);
    fetchAllCommunes(huyenID: defaultTinhHuyenXa.first['HuyenID_']);
    fetchAllVillages(xaID: defaultTinhHuyenXa.first['XaID_']);
    if (defaultTinhHuyenXa.isNotEmpty &&
        defaultTinhHuyenXa.first['TinhID_'] !=
            '00000000-0000-0000-0000-000000000000') {
      provinceFilter.value = defaultTinhHuyenXa.first['TinhID_'];
    }
    if (defaultTinhHuyenXa.isNotEmpty &&
        defaultTinhHuyenXa.first['HuyenID_'] !=
            '00000000-0000-0000-0000-000000000000') {
      districtFilter.value = defaultTinhHuyenXa.first['HuyenID_'];
    }
    if (defaultTinhHuyenXa.isNotEmpty &&
        defaultTinhHuyenXa.first['XaID_'] !=
            '00000000-0000-0000-0000-000000000000') {
      communeFilter.value = defaultTinhHuyenXa.first['XaID_'];
    }
    if (defaultTinhHuyenXa.isNotEmpty &&
        defaultTinhHuyenXa.first['ThonID_'] !=
            '00000000-0000-0000-0000-000000000000') {
      villageFilter.value = defaultTinhHuyenXa.first['ThonID_'];
    }
  }

  Future<void> fetchSearchResultsWithFilters() async {
    isLoading.value = true;
    currentPage.value = 1; // Reset về trang đầu tiên khi lọc
    hasMoreData.value = true; // Reset trạng thái còn dữ liệu

    try {
      final List<Map<String, dynamic>> body = [
        {
          "Type": "guid",
          "Name": "TinhID",
          "Value": provinceFilter.value.isEmpty
              ? "00000000-0000-0000-0000-000000000000"
              : provinceFilter.value
        },
        {
          "Type": "guid",
          "Name": "HuyenID",
          "Value": districtFilter.value.isEmpty
              ? "00000000-0000-0000-0000-000000000000"
              : districtFilter.value
        },
        {
          "Type": "guid",
          "Name": "XaID",
          "Value": communeFilter.value.isEmpty
              ? "00000000-0000-0000-0000-000000000000"
              : communeFilter.value
        },
        {
          "Type": "guid",
          "Name": "ThonID",
          "Value": villageFilter.value.isEmpty
              ? "00000000-0000-0000-0000-000000000000"
              : villageFilter.value
        },
        {
          "Type": "guid",
          "Name": "LoaiHinhCoSoID",
          "Value": loaihinhCsId.value.isEmpty
              ? "00000000-0000-0000-0000-000000000000"
              : loaihinhCsId.value
        },
        {
          "Type": "string",
          "Name": "TinhTrangBanHanh",
          "Value":
              tinhTrangBanHanhId.value.isEmpty ? null : tinhTrangBanHanhId.value
        },
        {
          "Type": "string",
          "Name": "TuNgay_TimKiem",
          "Value": DateFormat('yyyy-MM-dd').format(startDate.value)
        },
        {
          "Type": "string",
          "Name": "DenNgay_TimKiem",
          "Value": DateFormat('yyyy-MM-dd').format(endDate.value)
        },
        {"Type": "string", "Name": "SapXep", "Value": "NgayKy"},
        {"Type": "string", "Name": "TuKhoa", "Value": ""},
        {"Type": "guid", "Name": "DonViID", "Value": donViId},
        {"Type": "guid", "Name": "UserID", "Value": userId},
        {"Type": "int", "Name": "PageNumber", "Value": currentPage.value},
        {"Type": "int", "Name": "PageSize", "Value": pageSize.value}
      ];

      final response = await _procService.callProc(PROC_NAME, body);

      searchResults.value = response.isNotEmpty
          ? response
              .map<GiayXacNhanKienThucModel>((item) =>
                  GiayXacNhanKienThucModel.fromJson(
                      item as Map<String, dynamic>))
              .toList()
          : [];

      filteredResults.value = searchResults;

      // Kiểm tra nếu không đủ dữ liệu cho một trang đầy đủ
      if (response.length < pageSize.value) {
        hasMoreData.value = false;
      }
    } catch (e) {
      log.e("Error fetching search results: $e");
      searchResults.clear();
      filteredResults.clear();
      hasMoreData.value = false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> fetchAllLoaiHinhCS() async {
    isLoadingLoaiHinhCS.value = true;
    final List<Map<String, dynamic>> body = [];
    try {
      List<dynamic> response =
          await _procService.callProc(ProcConstants.getAllLoaiHinhCoSo, body);
      List<Map<String, dynamic>> mappedResponse =
          response.cast<Map<String, dynamic>>();

      loaiHinhCSs.assignAll(
        mappedResponse.map((Map<String, dynamic> loaiHinhCS) {
          return {
            "value": loaiHinhCS['LoaiHinhCoSoID'],
            "display": loaiHinhCS['TenLoaiHinhCoSo'],
          };
        }).toList(),
      );
    } finally {
      isLoadingLoaiHinhCS.value = false;
    }
  }

  Future<void> fetchAllTinhTrangCapGCN() async {
    isLoadingtinhTrangBanHanh.value = true;
    try {
      List<dynamic> response =
          await _procService.callProc(ProcConstants.getAllTrangThaiBanHanh, []);
      List<Map<String, dynamic>> mappedResponse =
          response.cast<Map<String, dynamic>>();
      tinhTrangBanHanh.assignAll(
        mappedResponse.map((Map<String, dynamic> tinhTrangCapGCN) {
          return {
            "value": tinhTrangCapGCN['MaTrangThai'],
            "display": tinhTrangCapGCN['TenTrangThai'],
          };
        }).toList(),
      );
    } finally {
      isLoadingtinhTrangBanHanh.value = false;
    }
  }

  Future<void> loadInfoProfile() async {
    userAccessModel = await UserUseCase.getUser();
    userGroup.value = userAccessModel?.userGroupCode ?? '';
    donViId = userAccessModel?.donViID ?? '';
    userId = userAccessModel?.userID ?? '';
    update(["bodyID"]);
  }

  Future<void> loadMoreData() async {
    if (isLoadingMore.value || !hasMoreData.value || isLoading.value) return;

    isLoadingMore.value = true;
    try {
      currentPage.value++; // Tăng số trang

      final response =
          await _procService.callProc(PROC_NAME, _buildRequestParams(""));

      if (response.isNotEmpty) {
        final newItems = response
            .map<GiayXacNhanKienThucModel>((item) =>
                GiayXacNhanKienThucModel.fromJson(item as Map<String, dynamic>))
            .toList();

        searchResults.addAll(newItems);
        filteredResults.addAll(newItems);

        // Kiểm tra nếu không đủ dữ liệu cho một trang đầy đủ
        if (response.length < pageSize.value) {
          hasMoreData.value = false;
        }
      } else {
        hasMoreData.value = false;
      }
    } catch (e) {
      log.e("Error loading more data: $e");
      hasMoreData.value = false;
    } finally {
      isLoadingMore.value = false;
    }
  }
}
