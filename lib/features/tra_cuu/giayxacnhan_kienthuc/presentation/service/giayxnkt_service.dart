import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/cssxkd_model.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import '../../../../../core/data/api/services/proc/proc_service.dart';
import '../../model/giayxnkt_detail_model.dart';
import '../../model/giayxnkt_nhanvien_model.dart';
import '../../model/giayxnkt_tailieu_model.dart';

class giayxnktService {
  final ProcService _procService = Get.find<ProcService>();
  Future<List<T>> fetchData<T>(
      String procedureName,
      List<Map<String, dynamic>> params,
      T Function(Map<String, dynamic>) fromJson) async {
    final response = await _procService.callProc(procedureName, params);
    return response.isNotEmpty
        ? response
            .map<T>((item) => fromJson(item as Map<String, dynamic>))
            .toList()
        : [];
  }

  Future<List<GiayxnktNhanvienModel>> fetchNhanVien(String id) async {
    return fetchData(
      "Proc_GetAll_CoSoSXKDNhanVien",
      [
        {"Type": "guid", "Name": "ID", "Value": id}
      ],
      (json) => GiayxnktNhanvienModel.fromJson(json),
    );
  }

  //Danh sách tài liệu
  Future<List<GiayxnktTailieuModel>> fetchCoSoSXKDTL(String id) async {
    return fetchData(
      "Proc_GetAll_CoSoSXKDTL",
      [
        {"Type": "guid", "Name": "ID", "Value": id}
      ],
      (json) => GiayxnktTailieuModel.fromJson(json),
    );
  }

  Future<List<GiayXacNhanKienThucDetailModel>> fetchGiayXnktByID(String id) async {
    return fetchData(
      "Proc_Get_ChiTiet_GXNKienThucATTP",
      [
        {"Type": "guid", "Name": "GXNKienThucATTPID", "Value": id}
      ],
          (json) => GiayXacNhanKienThucDetailModel.fromJson(json),
    );
  }
}
