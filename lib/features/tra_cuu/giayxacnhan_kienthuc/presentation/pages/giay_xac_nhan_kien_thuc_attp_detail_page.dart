import 'package:attp_2024/core/configs/contents/app_content.dart';
import 'package:attp_2024/core/ui/widgets/webview/webview_page.dart';
import 'package:attp_2024/core/utils/convert_text.dart';
import 'package:attp_2024/features/tra_cuu/widgets/info_row.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:attp_2024/features/tra_cuu/giayxacnhan_kienthuc/model/giay_xac_nhan_kien_thuc_model.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../controller/giay_xac_nhan_kien_thuc_attp_controller.dart';

class GiayXacNhanKienThucATTPDetailPage
    extends GetView<GiayXacNhanKienThucATTPController> {
  const GiayXacNhanKienThucATTPDetailPage({super.key});

  bool get isFile => true;

  @override
  Widget build(BuildContext context) {
    final GiayXacNhanKienThucModel args = Get.arguments;
    return Scaffold(
      appBar: AppBarWidget(
        title: "Thông tin giấy chứng nhận",
        actions: _buildActions(args),
        centerTitle: true,
      ),
      body: _buildBody(args),
    );
  }

  List<Widget> _buildActions(GiayXacNhanKienThucModel item) {
    if (item.DinhKem == null || item.DinhKem!.isEmpty) {
      return [];
    }

    return [
      Padding(
        padding: EdgeInsets.only(right: 4.w),
        child: IconButton(
          color: AppColors.white,
          onPressed: () {
            String baseUrl = controller.userAccessModel?.siteURL ?? "";
            String path = item.DinhKem ?? "";
            String finalUrl = "$baseUrl$path";
            if (finalUrl.endsWith('*')) {
              finalUrl = finalUrl.substring(0, finalUrl.length - 1);
            }
            Get.to(() => WebViewPage(
                  title: AppContent.appTitleWebview,
                  initialUrl: finalUrl,
                ));
          },
          icon: const Icon(CupertinoIcons.paperclip),
        ),
      ),
    ];
  }

  Widget _buildBody(GiayXacNhanKienThucModel item) {
    const defaultText = "Đang cập nhật";
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CupertinoListSection.insetGrouped(
            children: [
              InfoRowWidget(
                  label: "Số văn bản:",
                  value: item.SoVanBan ?? defaultText,
                  icon: Icons.tag),
              InfoRowWidget(
                  label: "Tên CSSXKD:",
                  value: getText(item.TenCoSoSXKD.toString()) ?? defaultText,
                  icon: Icons.business),
              InfoRowWidget(
                  label: "Ngày ký:",
                  value: item.NgayKy ?? defaultText,
                  icon: Icons.calendar_today),
              InfoRowWidget(
                  label: "Người ký:",
                  value: item.NguoiKy ?? defaultText,
                  icon: Icons.person),
              InfoRowWidget(
                  label: "Chức vụ:",
                  value: item.ChucVu ?? defaultText,
                  icon: Icons.badge),
              InfoRowWidget(
                  label: "Cơ quan ban hành:",
                  value: item.CoQuanBanHanh ?? defaultText,
                  icon: Icons.apartment),
              InfoRowWidget(
                  label: "Ngày hết hạn:",
                  value: item.NgayHetHan ?? defaultText,
                  icon: Icons.calendar_today),
              InfoRowWidget(
                  label: "Trích yếu:",
                  value: item.TrichYeu ?? defaultText,
                  icon: Icons.edit_attributes_outlined),
              InfoRowWidget(
                  label: "Trạng thái:",
                  value: item.TrangThai.toString(),
                  icon: Icons.stacked_bar_chart),
              InfoRowWidget(
                  label: "Trạng thái ban hành:",
                  value: item.TrangThai_BanHanh.toString(),
                  icon: Icons.stacked_bar_chart),
              InfoRowWidget(
                  label: "Số đăng ký kinh doanh:",
                  value: item.SoDKKD ?? defaultText,
                  icon: Icons.tag),
              InfoRowWidget(
                  label: "Ngày cấp:",
                  value: item.NgayCapGPKD ?? defaultText,
                  icon: Icons.calendar_today),
              InfoRowWidget(
                  label: "Cơ quan cấp:",
                  value: item.CoQuanCapGPKD ?? defaultText,
                  icon: Icons.apartment),
              InfoRowWidget(
                  label: "Số điện thoại:",
                  value: item.SoDienThoai ?? defaultText,
                  icon: Icons.phone),
              InfoRowWidget(
                  label: "Email:",
                  value: item.Email ?? defaultText,
                  icon: Icons.email),
              InfoRowWidget(
                  label: "Loại hình cơ sở:",
                  value: item.LoaiHinhCoSo ?? defaultText,
                  icon: Icons.stacked_bar_chart),
              InfoRowWidget(
                  label: "Địa chỉ cơ sở:",
                  value: item.DiaChiCS ?? defaultText,
                  icon: Icons.location_on),
            ],
          )
        ],
      ),
    );
  }

  // Helper to build rows
  static Widget _buildInfoRow(String label, String value, {IconData? icon}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (icon != null)
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: Icon(
                icon,
                size: 20,
                color: AppColors.primary,
              ),
            ),
          SizedBox(
            width: 140, // Đặt chiều rộng cố định cho label
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(height: 1.5),
            ),
          ),
        ],
      ),
    );
  }
}
