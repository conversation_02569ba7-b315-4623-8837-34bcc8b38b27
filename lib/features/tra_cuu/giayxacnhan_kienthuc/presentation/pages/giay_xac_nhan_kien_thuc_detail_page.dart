import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';

import '../controller/giay_xac_nhan_kien_thuc_detail_controller.dart';
import '../widgets/lists/dstailieu_list.dart';
import '../widgets/lists/nhan_vien_list.dart';
import '../widgets/lists/thong_tin_chung.dart';

class GiayXacNhanKienThucATTPDetailPage
    extends GetView<GiayXacNhanKienThucDetailController> {
  const GiayXacNhanKienThucATTPDetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppBarWidget(
        title: "Thông tin giấy xác nhận kiến thức",
        // title: "<PERSON> tiết",
        centerTitle: true,
      ),
      body: Column(
        children: [
          // TabBar moved into the body
          Container(
              color: Colors.white,
              child: TabBar(
                isScrollable: true,
                controller: controller.tabController,
                tabAlignment: TabAlignment.start,
                indicatorSize: TabBarIndicatorSize.tab,
                labelColor: AppColors.primary,
                unselectedLabelColor: Colors.grey,
                indicator: BoxDecoration(
                  color: const Color(0xFFEAFFF9),
                  border: Border(
                    bottom: BorderSide(color: AppColors.primary, width: 2),
                  ),
                ),
                tabs: const [
                  Tab(text: "Thông tin chung"),
                  Tab(text: "Đối tượng cấp"),
                  Tab(text: "Tài liệu"),
                ],
                onTap: (index) {
                  controller.pageController.jumpToPage(index);
                },
              )),
          Expanded(
            child: PageView(
              controller: controller.pageController,
              onPageChanged: (index) {
                controller.tabController.animateTo(index);
              },
              children: [
                ThongTinChung(
                  controller: controller,
                ),
                NhanVienList(
                  controller: controller,
                ),
                DanhSachTaiLieuList(controller: controller),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
