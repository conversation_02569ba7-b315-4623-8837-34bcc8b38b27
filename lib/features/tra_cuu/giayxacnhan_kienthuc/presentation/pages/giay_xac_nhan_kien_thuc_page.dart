import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/utils/color_utils.dart';
import 'package:attp_2024/core/utils/convert_text.dart';
import 'package:attp_2024/features/tra_cuu/widgets/custom_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/routes/routes.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:attp_2024/core/ui/widgets/floating_action_button/animated_floating_action_button.dart';
import 'package:attp_2024/core/ui/widgets/searchPage/search_page.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/features/tra_cuu/giayxacnhan_kienthuc/model/giay_xac_nhan_kien_thuc_model.dart';
// import '../../../widgets/common_list_card.dart';
import '../../../../../core/configs/contents/app_content.dart';
import '../controller/giay_xac_nhan_kien_thuc_attp_controller.dart';
import '../widgets/search.dart';
// import '../widgets/common_list_card.dart';

class GiayXacNhanKienThucATTPPage
    extends GetView<GiayXacNhanKienThucATTPController> {
  const GiayXacNhanKienThucATTPPage({super.key});

  Widget buildShimmerEffect() {
    return ListView.builder(
      padding: const EdgeInsets.all(8.0),
      itemCount: 5,
      itemBuilder: (context, index) {
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(5.0),
          ),
          margin: const EdgeInsets.symmetric(vertical: 8.0),
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildShimmerBox(width: 80, height: 80),
                const Gap(10.0),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildShimmerBox(width: double.infinity, height: 16.0),
                      const Gap(8.0),
                      _buildShimmerBox(width: 150.0, height: 16.0),
                      const Gap(8.0),
                      _buildShimmerBox(width: double.infinity, height: 16.0),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildShimmerBox({required double width, required double height}) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.0),
        ),
      ),
    );
  }

  Widget _buildListItem(GiayXacNhanKienThucModel item) {
    return CustomCard(
      title: LabelValuePair(
          label: "Số", value: item.SoVanBan ?? AppContent.textDefault),
      statusText: item.TenTrangThai ?? AppContent.textDefault,
      statusColor: hexToColor(item.TrangThai_MauSac),
      labelValueList: [
        LabelValuePair(
            label: "Ngày ký", value: item.NgayKy ?? AppContent.textDefault),
        LabelValuePair(
            label: "Ngày hết hạn",
            value: item.NgayHetHan ?? AppContent.textDefault),
        LabelValuePair(
            label: "Tên cơ sở",
            value: getText(item.TenCoSoSXKD.toString(), before: false)),
        LabelValuePair(
            label: "Loại hình cơ sở",
            value: item.LoaiHinhCoSo ?? AppContent.textDefault),
        LabelValuePair(
            label: "Địa chỉ", value: item.DiaChiCS ?? AppContent.textDefault),
      ],
      onTap: () {
        Get.toNamed(Routes.thongTinGiayXacNhanKienThucDetail, arguments: item);
      },
    );
  }

  Widget build(BuildContext context) {
    final ScrollController scrollController = ScrollController();
    final ValueNotifier<bool> isVisible = ValueNotifier(true);

    scrollController.addListener(() {
      if (scrollController.position.userScrollDirection ==
          ScrollDirection.reverse) {
        isVisible.value = false;
      } else if (scrollController.position.userScrollDirection ==
          ScrollDirection.forward) {
        isVisible.value = true;
      }

      // Thêm logic cho infinity scroll
      if (scrollController.position.pixels >=
          scrollController.position.maxScrollExtent - 200) {
        controller.loadMoreData();
      }
    });

    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        appBar: const AppBarWidget(
          title: "Tra cứu giấy xác nhận kiến thức",
          centerTitle: true,
        ),
        body: Stack(
          children: [
            Column(
              children: [
                const SearchWidget(),
                Expanded(
                  child: RefreshIndicator(
                    onRefresh: () async {
                      controller.fetchSearchResults("");
                    },
                    child: Obx(() {
                      if (controller.isLoading.value) {
                        return buildShimmerEffect();
                      }
                      if (controller.filteredResults.isEmpty) {
                        return const Center(
                          child: TextWidget(
                            text: "Không có kết quả nào được tìm thấy.",
                            size: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        );
                      }
                      return ListView.builder(
                        controller: scrollController,
                        padding: const EdgeInsets.all(8.0),
                        itemCount: controller.filteredResults.length +
                            (controller.hasMoreData.value ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index >= controller.filteredResults.length) {
                            return Obx(() => controller.isLoadingMore.value
                                ? const Padding(
                                    padding: EdgeInsets.all(16.0),
                                    child: Center(
                                      child: CircularProgressIndicator(),
                                    ),
                                  )
                                : const SizedBox.shrink());
                          }
                          final item = controller.filteredResults[index];
                          return _buildListItem(item);
                        },
                      );
                    }),
                  ),
                ),
              ],
            ),
            // ValueListenableBuilder<bool>(
            //   valueListenable: isVisible,
            //   builder: (context, visible, child) {
            //     return AnimatedFloatingActionButton(
            //       isVisible: visible,
            //       onPressed: () async {
            //         Get.to(() => SearchPage(
            //               data: controller.filteredResults,
            //               searchByField: (item) => (item as dynamic).TenCoSoSXKD,
            //               searchedItemBuilder: (item) {
            //                 final coSo = item as GiayXacNhanKienThucModel;
            //                 return _buildListItem(coSo);
            //               },
            //             ));
            //         // Get.to(() => PDFViewerPage(
            //         //       pdfUrl:
            //         //           'http://laptrinhattp.qlns.vn/Dinhkem/000.00.00.H30/VanBan/TaiLieuCapGiayChungNhan/issues(1)090125014155.pdf',
            //         //       title: 'Đính kèm', // Optional
            //         //     ));
            //       },
            //       icon: Icons.search,
            //       backgroundColor: AppColors.primary,
            //     );
            //   },
            // ),
          ],
        ),
      ),
    );
  }
}
