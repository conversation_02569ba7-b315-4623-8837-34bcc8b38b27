// ignore_for_file: public_member_api_docs, sort_constructors_first

const String defaultImage = "https://via.placeholder.com/150";

class GiayXacNhanKienThucModel {
  final String? GXNKienThucATTPID;
  final String? CoSoSXKDID;
  final String? SoVanBan;
  final String? NgayKy;
  final String? NguoiKy;
  final String? ChucVu;
  final String? CoQuanBanHanh;
  final String? NgayHetHan;
  final String? TrichYeu;
  final String? DinhKem;
  final bool? TrangThai;
  final String? TrangThai_BanHanh;
  final String? TenCoSoSXKD;
  final String? SoDKKD;
  final String? NgayCapGPKD;
  final String? CoQuanCapGPKD;
  final String? SoDienThoai;
  final String? Email;
  final String? LoaiHinhCoSo;
  final String? DiaChiCS;
  final String? TenTrangThai;
  final String? TrangThai_MauSac;
  final int? TotalResults;

  GiayXacNhanKienThucModel({
    this.GXNKienThucATTPID,
    this.CoSoSXKDID,
    this.SoVan<PERSON><PERSON>,
    this.NgayKy,
    this.NguoiKy,
    this.ChucVu,
    this.CoQuanBanHanh,
    this.NgayHetHan,
    this.TrichYeu,
    this.DinhKem,
    this.TrangThai,
    this.TrangThai_BanHanh,
    this.TenCoSoSXKD,
    this.SoDKKD,
    this.NgayCapGPKD,
    this.CoQuanCapGPKD,
    this.SoDienThoai,
    this.Email,
    this.LoaiHinhCoSo,
    this.DiaChiCS,
    this.TenTrangThai,
    this.TrangThai_MauSac,
    this.TotalResults,
  });

  factory GiayXacNhanKienThucModel.fromJson(Map<String, dynamic> json) {
    return GiayXacNhanKienThucModel(
        GXNKienThucATTPID: json['GXNKienThucATTPID'],
        CoSoSXKDID: json['CoSoSXKDID'],
        SoVanBan: json['SoVanBan'],
        NgayKy: json['NgayKy'],
        NguoiKy: json['NguoiKy'],
        ChucVu: json['ChucVu'],
        CoQuanBanHanh: json['CoQuanBanHanh'],
        NgayHetHan: json['NgayHetHan'],
        TrichYeu: json['TrichYeu'],
        DinhKem: json['DinhKem'],
        TrangThai: json['TrangThai'],
        TrangThai_BanHanh: json['TrangThai_BanHanh'],
        TenCoSoSXKD: json['TenCoSoSXKD'],
        SoDKKD: json['SoDKKD'],
        NgayCapGPKD: json['NgayCapGPKD'],
        CoQuanCapGPKD: json['CoQuanCapGPKD'],
        SoDienThoai: json['SoDienThoai'],
        Email: json['Email'],
        LoaiHinhCoSo: json['LoaiHinhCoSo'],
        DiaChiCS: json['DiaChiCS'],
        TenTrangThai: json['TenTrangThai'],
        TrangThai_MauSac: json['TrangThai_MauSac'],
        TotalResults: json['TotalResults']);
  }

  Map<String, dynamic> toJson() {
    return {
      'GXNKienThucATTPID': GXNKienThucATTPID,
      'CoSoSXKDID': CoSoSXKDID,
      'SoVanBan': SoVanBan,
      'NgayKy': NgayKy,
      'NguoiKy': NguoiKy,
      'ChucVu': ChucVu,
      'CoQuanBanHanh': CoQuanBanHanh,
      'NgayHetHan': NgayHetHan,
      'TrichYeu': TrichYeu,
      'DinhKem': DinhKem,
      'TrangThai': TrangThai,
      'TrangThai_BanHanh': TrangThai_BanHanh,
      'TenCoSoSXKD': TenCoSoSXKD,
      'SoDKKD': SoDKKD,
      'NgayCapGPKD': NgayCapGPKD,
      'CoQuanCapGPKD': CoQuanCapGPKD,
      'SoDienThoai': SoDienThoai,
      'Email': Email,
      'LoaiHinhCoSo': LoaiHinhCoSo,
      'DiaChiCS': DiaChiCS,
      'TenTrangThai': TenTrangThai,
      'TrangThai_MauSac': TrangThai_MauSac,
      'TotalResults': TotalResults,
    };
  }
}
