class GiayXacNhanKienThucDetailModel {
  final String? GXNKienThucATTPID;
  final String? SoVanBan;
  final String? NgayKy;
  final String? NguoiKy;
  final String? ChucVu;
  final String? NgayHetHan;
  final String? CoQuanBanHanh;
  final String? CoSoSXKDID;
  final String? TenCoSoSXKD;
  final String? SoGPKD;
  final String? NgayCapGPKD;
  final String? CoQuanCapGPKD;
  final String? SoDienThoai;
  final String? EmailCS;
  final String? LoaiHinhCoSo;
  final String? NoiDung;
  final String? DinhKem;
  final String? TrangThai_BanHanh;
  final bool? TrangThai;
  final String? DiaChiCS;

  GiayXacNhanKienThucDetailModel({
    this.GXNKienThucATTPID,
    this.SoVanBan,
    this.NgayKy,
    this.NguoiKy,
    this.ChucVu,
    this.NgayHetHan,
    this.CoQuanBanHanh,
    this.CoSoSXKDID,
    this.TenCoSoSXKD,
    this.SoGPKD,
    this.NgayCapGPKD,
    this.CoQuanCapGPKD,
    this.SoDienThoai,
    this.EmailCS,
    this.LoaiHinhCoSo,
    this.NoiDung,
    this.DinhKem,
    this.TrangThai_BanHanh,
    this.TrangThai,
    this.DiaChiCS,
  });

  factory GiayXacNhanKienThucDetailModel.fromJson(Map<String, dynamic> json) {
    return GiayXacNhanKienThucDetailModel(
      GXNKienThucATTPID: json['GXNKienThucATTPID'],
      SoVanBan: json['SoVanBan'],
      NgayKy: json['NgayKy'],
      NguoiKy: json['NguoiKy'],
      ChucVu: json['ChucVu'],
      NgayHetHan: json['NgayHetHan'],
      CoQuanBanHanh: json['CoQuanBanHanh'],
      CoSoSXKDID: json['CoSoSXKDID'],
      TenCoSoSXKD: json['TenCoSoSXKD'],
      SoGPKD: json['SoGPKD'],
      NgayCapGPKD: json['NgayCapGPKD'],
      CoQuanCapGPKD: json['CoQuanCapGPKD'],
      SoDienThoai: json['SoDienThoai'],
      EmailCS: json['EmailCS'],
      LoaiHinhCoSo: json['LoaiHinhCoSo'],
      NoiDung: json['NoiDung'],
      DinhKem: json['DinhKem'],
      TrangThai_BanHanh: json['TrangThai_BanHanh'],
      TrangThai: json['TrangThai'],
      DiaChiCS: json['DiaChiCS'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'GXNKienThucATTPID': GXNKienThucATTPID,
      'SoVanBan': SoVanBan,
      'NgayKy': NgayKy,
      'NguoiKy': NguoiKy,
      'ChucVu': ChucVu,
      'NgayHetHan': NgayHetHan,
      'CoQuanBanHanh': CoQuanBanHanh,
      'CoSoSXKDID': CoSoSXKDID,
      'TenCoSoSXKD': TenCoSoSXKD,
      'SoGPKD': SoGPKD,
      'NgayCapGPKD': NgayCapGPKD,
      'CoQuanCapGPKD': CoQuanCapGPKD,
      'SoDienThoai': SoDienThoai,
      'EmailCS': EmailCS,
      'LoaiHinhCoSo': LoaiHinhCoSo,
      'NoiDung': NoiDung,
      'DinhKem': DinhKem,
      'TrangThai_BanHanh': TrangThai_BanHanh,
      'TrangThai': TrangThai,
      'DiaChiCS': DiaChiCS,
    };
  }
}
