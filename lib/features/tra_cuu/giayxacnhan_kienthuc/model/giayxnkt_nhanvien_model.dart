class GiayxnktNhanvienModel {
  String? coSoSXKDID;
  String? coSoSXKDNhanVienID;
  String? maCoSoSXKDNhanVien;
  String? hoVaTen;
  String? ngaySinh;
  String? gioiTinh;
  String? soCMND;
  String? ngayCap;
  String? noiCap;
  String? diDong;
  String? email;
  String? viTriCongViec;
  String? soHDLD;
  String? ngayKyHDLD;
  String? loaiHDLD;
  bool? trangThai;
  String? dinhKem;

  GiayxnktNhanvienModel({
    this.coSoSXKDID,
    this.coSoSXKDNhanVienID,
    this.maCoSoSXKDNhanVien,
    this.hoVaTen,
    this.ngaySinh,
    this.gioiTinh,
    this.soCMND,
    this.ngayCap,
    this.noiCap,
    this.diDong,
    this.email,
    this.viTriCongViec,
    this.soHDLD,
    this.ngayKyHDLD,
    this.loaiHDLD,
    this.trangThai,
    this.dinhKem,
  });

  factory GiayxnktNhanvienModel.fromJson(Map<String, dynamic> json) {
    return GiayxnktNhanvienModel(
      coSoSXKDID: json['CoSoSXKDID'],
      coSoSXKDNhanVienID: json['CoSoSXKDNhanVienID'],
      maCoSoSXKDNhanVien: json['MaCoSoSXKDNhanVien'],
      hoVaTen: json['HoVaTen'],
      ngaySinh: json['NgaySinh'],
      gioiTinh: json['GioiTinh'],
      soCMND: json['SoCMND'],
      ngayCap: json['NgayCap'],
      noiCap: json['NoiCap'],
      diDong: json['DiDong'],
      email: json['Email'],
      viTriCongViec: json['ViTriCongViec'],
      soHDLD: json['SoHDLD'],
      ngayKyHDLD: json['NgayKyHDLD'],
      loaiHDLD: json['LoaiHDLD'],
      trangThai: json['TrangThai'],
      dinhKem: json['DinhKem'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'CoSoSXKDID': coSoSXKDID,
      'CoSoSXKDNhanVienID': coSoSXKDNhanVienID,
      'MaCoSoSXKDNhanVien': maCoSoSXKDNhanVien,
      'HoVaTen': hoVaTen,
      'NgaySinh': ngaySinh,
      'GioiTinh': gioiTinh,
      'SoCMND': soCMND,
      'NgayCap': ngayCap,
      'NoiCap': noiCap,
      'DiDong': diDong,
      'Email': email,
      'ViTriCongViec': viTriCongViec,
      'SoHDLD': soHDLD,
      'NgayKyHDLD': ngayKyHDLD,
      'LoaiHDLD': loaiHDLD,
      'TrangThai': trangThai,
      'DinhKem': dinhKem,
    };
  }
}
