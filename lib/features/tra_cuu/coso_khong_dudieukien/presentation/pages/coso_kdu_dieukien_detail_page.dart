import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:attp_2024/features/tra_cuu/coso_khong_dudieukien/presentation/widgets/list/dstailieu_list.dart';
import 'package:attp_2024/features/tra_cuu/coso_khong_dudieukien/presentation/widgets/list/gcn_list.dart';
import 'package:attp_2024/features/tra_cuu/coso_khong_dudieukien/presentation/widgets/list/giaycnkhac_list.dart';
import 'package:attp_2024/features/tra_cuu/coso_khong_dudieukien/presentation/widgets/list/san_pham_list.dart';
import 'package:attp_2024/features/tra_cuu/coso_khong_dudieukien/presentation/widgets/list/thiet_bi_list.dart';
import 'package:attp_2024/features/tra_cuu/coso_khong_dudieukien/presentation/widgets/thong_tin_chung.dart';
import '../controller/coso_kdudk_detail_controller.dart';

class CoSoKDuDieuKienDetailPage
    extends GetView<CoSoKhongDuDieuKienDetailController> {
  const CoSoKDuDieuKienDetailPage({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppBarWidget(
        title: "Cơ sở không thuộc diện cấp GCN",
        centerTitle: true,
      ),
      body: Column(
        children: [
          Container(
              color: Colors.white,
              child: TabBar(
                isScrollable: true,
                controller: controller.tabController,
                tabAlignment: TabAlignment.start,
                indicatorSize: TabBarIndicatorSize.tab,
                labelColor: AppColors.primary,
                unselectedLabelColor: Colors.grey,
                indicator: BoxDecoration(
                  color: const Color(0xFFEAFFF9),
                  border: Border(
                    bottom: BorderSide(color: AppColors.primary, width: 2),
                  ),
                ),
                tabs: const [
                  Tab(text: "Thông tin chung"),
                  Tab(text: "Sản phẩm"),
                  Tab(text: "Thiết bị"),
                  Tab(text: "Giấy cam kết ATTP"),
                  Tab(text: "Giấy chứng hệ thống đảm bảo chất lượng"),
                  Tab(text: "Danh sách tài liệu"),
                ],
                onTap: (index) {
                  controller.pageController
                      .jumpToPage(index); // This is also not observable
                },
              )),
          Expanded(
            child: PageView(
              controller: controller.pageController,
              onPageChanged: (index) {
                controller.tabController.animateTo(index);
              },
              children: [
                ThongTinChung(
                  controller: controller,
                ),
                SanPhamList(
                  controller: controller,
                ),
                ThietBiList(
                  controller: controller,
                ),
                GiayCNList(controller: controller),
                GiayCNKhacList(controller: controller),
                DanhSachTaiLieuList(controller: controller),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
