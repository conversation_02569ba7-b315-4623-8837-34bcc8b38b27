import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/presentation/widgets/common_list_card.dart';
import '../../../../../../core/configs/contents/app_content.dart';
import '../../controller/coso_kdudk_detail_controller.dart';

class ThietBiList extends StatelessWidget {
  final CoSoKhongDuDieuKienDetailController controller;

  const ThietBiList({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.trangThietBiList.isEmpty) {
        return SizedBox(
          height: 80.h,
          child: Center(
            // child: Text(
            //   "<PERSON>hông có thiết bị nào.",
            //   style: TextStyle(fontSize: 16, color: AppColors.primary),
            // )
            // ,
            child: Image.asset(
              AppImageString.iDataNotFound,
              width: 100,
              height: 100,
            ),
          ),
        );
      }
      return SizedBox(
        height: 80.h,
        child: ListView.builder(
          physics: const BouncingScrollPhysics(),
          padding: const EdgeInsets.all(8.0),
          itemCount: controller.trangThietBiList.length,
          itemBuilder: (context, index) {
            final items = controller.trangThietBiList[index];
            return CommonListCard(
              showImage: false,
              imageHeight: 130,
              imageWidth: 100,
              title: items.maCoSoSXKDTTB ?? AppContent.textDefault,
              labelValueList: [
                LabelValuePair(
                  label: "Tên thiết bị",
                  value: items.tenTTB != null
                      ? items.tenTTB!.toString()
                      : AppContent.textDefault,
                ),
                LabelValuePair(
                  label: "Số lượng",
                  value: items.soLuong != null
                      ? items.soLuong!.toString()
                      : AppContent.textDefault,
                ),
                LabelValuePair(
                    label: "Nước sản xuất",
                    value: items.tenNuocSanXuat ?? AppContent.textDefault),
                LabelValuePair(
                    label: "Tổng công suất",
                    value: items.tongCongSuat ?? AppContent.textDefault),
                LabelValuePair(
                  label: "Năm bắt đầu sử dụng",
                  value: items.namBatDauSD != null
                      ? items.namBatDauSD!.toString()
                      : AppContent.textDefault,
                ),
              ],
              logoUrl:
                  'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQH2ryv63hqy8OUEkcyFlwo6L6K5LI1Cpj4tg&s',
            );
          },
        ),
      );
    });
  }
}
