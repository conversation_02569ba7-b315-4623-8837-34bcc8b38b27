import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import '../../../../../../core/configs/contents/app_content.dart';
import '../../controller/coso_kdudk_detail_controller.dart';
import '../common_list_card.dart';

class SanPhamList extends StatelessWidget {
  final CoSoKhongDuDieuKienDetailController controller;
  const SanPhamList({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.sanPhamList.isEmpty) {
        return Center(
          
          child: Image.asset(
            AppImageString.iDataNotFound,
            width: 100,
            height: 100,
          ),
        );
      }
      return ListView.builder(
        itemCount: controller.sanPhamList.length,
        itemBuilder: (context, index) {
          final item = controller.sanPhamList[index];
          return CommonListCard(
            title: item.MaCoSoSXKDSanPham ?? AppContent.textDefault,
            labelValueList: [
              LabelValuePair(
                  label: "Tên sản phẩm", value: item.tenSanPham ?? ""),
              // LabelValuePair(
              //     label: "Thuộc nhóm", value: item.DanhSachNhomSanPham ?? ""),
              LabelValuePair(
                  label: "Tên nguyên liệu",
                  value: item.TenNguyenLieuChinh ?? ""),
              LabelValuePair(label: "Nguồn gốc", value: item.TenNguonGoc ?? ""),
              LabelValuePair(
                  label: "Cách thức đóng gói",
                  value: item.CachThucDongGoi ?? ""),
              // LabelValuePair(label: "Ghi chú", value: item.tenSanPham ?? ""),
            ],
            logoUrl: '',
            showImage: false,
          );
        },
      );
    });
  }
}
