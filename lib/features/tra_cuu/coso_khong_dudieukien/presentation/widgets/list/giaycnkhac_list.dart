import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/features/tra_cuu/coso_khong_dudieukien/presentation/controller/coso_kdudk_detail_controller.dart';

import '../../../../../../core/configs/contents/app_content.dart';
import '../../../../../../core/configs/theme/app_colors.dart';
import '../common_list_card.dart';

class GiayCNKhacList extends StatelessWidget {
  const GiayCNKhacList({
    super.key,
    required this.controller,
  });

  final CoSoKhongDuDieuKienDetailController controller;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.gcnDbClList.isEmpty) {
        return SizedBox(
          // height: 80.h,
          child: Center(
            child: Image.asset(
              AppImageString.iDataNotFound,
              width: 100,
              height: 100,
            ),
          ),
        );
      }
      return SizedBox(
        // height: 80.h,
        child: ListView.builder(
          physics: const BouncingScrollPhysics(),
          padding: const EdgeInsets.all(8.0),
          itemCount: controller.gcnDbClList.length,
          itemBuilder: (context, index) {
            final item = controller.gcnDbClList[index];
            return CommonListCard(
              imageHeight: 150,
              imageWidth: 100,
              showImage: false,
              title: item.maSo ?? AppContent.textDefault,
              labelValueList: [
                // LabelValuePair(label: "Số ", value: item.maSo ?? "Chưa rõ"),
                LabelValuePair(
                    label: "Ngày ký", value: item.ngayKy ?? AppContent.textDefault),
                LabelValuePair(
                    label: "Người ký", value: item.nguoiKy ?? AppContent.textDefault),
                LabelValuePair(
                    label: "Chức vụ", value: item.chucVu ?? AppContent.textDefault),
                LabelValuePair(
                    label: "Cơ quan cấp",
                    value: item.coQuanBanHanh ?? AppContent.textDefault),
                LabelValuePair(
                    label: "Ngày hết hạn", value: item.ngayHetHan ?? AppContent.textDefault),
                LabelValuePair(
                    label: "Diễn dãi", value: item.noiDung ?? AppContent.textDefault)
              ],
              logoUrl: '',
            );
          },
        ),
      );
    });
  }
}
