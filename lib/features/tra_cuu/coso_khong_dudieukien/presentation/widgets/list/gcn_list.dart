import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/features/tra_cuu/coso_khong_dudieukien/presentation/controller/coso_kdudk_detail_controller.dart';
import '../common_list_card.dart';

class GiayCNList extends StatelessWidget {
  const GiayCNList({
    super.key,
    required this.controller,
  });

  final CoSoKhongDuDieuKienDetailController controller;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.gcnATTPList.isEmpty) {
        return SizedBox(
          // height: 80.h,
          child: Center(
            child: Image.asset(
              AppImageString.iDataNotFound,
              width: 100,
              height: 100,
            ),
          ),
        );
      }
      return SizedBox(
        // height: 80.h,
        child: ListView.builder(
          physics: const BouncingScrollPhysics(),
          padding: const EdgeInsets.all(8.0),
          itemCount: controller.gcnATTPList.length,
          itemBuilder: (context, index) {
            final item = controller.gcnATTPList[index];
            return CommonListCard(
              imageHeight: 150,
              imageWidth: 100,
              showImage: false,
              title: item.soGCN,
              labelValueList: [
                // LabelValuePair(label: "Số ", value: item.soGCN),
                LabelValuePair(label: "Ngày cấp", value: item.ngayCap),
                LabelValuePair(label: "Người cấp", value: item.nguoiCap),
                LabelValuePair(label: "Chức vụ", value: item.chucVu),
                LabelValuePair(label: "Cơ quan cấp", value: item.coQuanCap),
                LabelValuePair(label: "Ngày hết hạn", value: item.ngayHetHan),
                // LabelValuePair(
                //     label: "Tình trạng ban hành",
                //     value: HtmlWidget(item.tinhTrang)),
                // LabelValuePair(
                //     label: "Tình trạng ban hành",
                //     value: HtmlWidget(item.tinhTrang))
              ],
              logoUrl: '',
            );
          },
        ),
      );
    });
  }
}
