import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import '../../../../../../core/configs/contents/app_content.dart';
import '../../controller/coso_kdudk_detail_controller.dart';
import '../common_list_card.dart';

class NhanVienList extends StatelessWidget {
  final CoSoKhongDuDieuKienDetailController controller;

  const NhanVienList({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.nhanVienList.isEmpty) {
        return Center(
          child: Image.asset(
            AppImageString.iDataNotFound,
            width: 100,
            height: 100,
          ),
          // child: Text(
          //   "Không có nhân viên nào.",
          //   style: TextStyle(fontSize: 16, color: AppColors.primary),
          // ),
        );
      }
      return ListView.builder(
        physics: const BouncingScrollPhysics(),
        padding: const EdgeInsets.all(8.0),
        itemCount: controller.nhanVienList.length,
        itemBuilder: (context, index) {
          final nhanVien = controller.nhanVienList[index];
          return CommonListCard(
            imageHeight: 150,
            imageWidth: 100,
            showImage: false,
            title: nhanVien.maCoSoSXKDNhanVien ?? AppContent.textDefault,
            labelValueList: [
              LabelValuePair(
                label: "Tên nhân viên",
                value: nhanVien.hoVaTen ?? AppContent.textDefault,
              ),
              LabelValuePair(
                label: "Số CMND",
                value: nhanVien.soCMND ?? AppContent.textDefault,
              ),
              LabelValuePair(
                label: "Ngày cấp",
                value: nhanVien.ngayCap ?? AppContent.textDefault,
              ),
              LabelValuePair(
                label: "Vị trí công việc",
                value: nhanVien.viTriCongViec ?? AppContent.textDefault,
              ),
              LabelValuePair(
                label: "Số HĐ",
                value: nhanVien.soHDLD ?? AppContent.textDefault,
              ),
              LabelValuePair(
                label: "Ngày ký",
                value: nhanVien.ngayKyHDLD ?? AppContent.textDefault,
              ),
              LabelValuePair(
                label: "Loại HĐ",
                value: nhanVien.loaiHDLD ?? AppContent.textDefault,
              ),
            ],
            logoUrl: '',
          );
        },
      );
    });
  }
}
