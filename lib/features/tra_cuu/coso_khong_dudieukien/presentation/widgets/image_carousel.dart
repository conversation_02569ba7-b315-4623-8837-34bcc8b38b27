// import 'package:carousel_slider/carousel_slider.dart';
// import 'package:flutter/material.dart';
// import 'package:attp_2024/core/configs/theme/app_colors.dart';
// import 'package:gap/gap.dart';
// import 'package:responsive_sizer/responsive_sizer.dart';

// class DotIndicatorCarousel extends StatefulWidget {
//   final List<String> imageUrls;

//   const DotIndicatorCarousel({
//     super.key,
//     required this.imageUrls,
//   });

//   @override
//   State<DotIndicatorCarousel> createState() => _DotIndicatorCarouselState();
// }

// class _DotIndicatorCarouselState extends State<DotIndicatorCarousel> {
//   int currentIndex = 0;

//   @override
//   Widget build(BuildContext context) {
//     final imageUrls = widget.imageUrls;

//     return Column(
//       children: [
//         // Carousel Slider
//         CarouselSlider.builder(
//           itemCount: imageUrls.length,
//           itemBuilder: (BuildContext context, int index, int realIndex) {
//             return ClipRRect(
//               borderRadius: BorderRadius.circular(5),
//               child: Image.network(
//                 imageUrls[index],
//                 fit: BoxFit.cover,
//                 width: double.infinity,
//                 errorBuilder: (context, error, stackTrace) {
//                   return const Center(
//                     child: Icon(Icons.broken_image, color: Colors.grey),
//                   );
//                 },
//               ),
//             );
//           },
//           options: CarouselOptions(
//             height: 22.h,
//             autoPlay: true,
//             enlargeCenterPage: true,
//             onPageChanged: (index, reason) {
//               setState(() {
//                 currentIndex = index;
//               });
//             },
//           ),
//         ),
//         Gap(1.h),
//         // Dot Indicator
//         Row(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: imageUrls.asMap().entries.map((entry) {
//             return GestureDetector(
//               onTap: () => setState(() => currentIndex = entry.key),
//               child: AnimatedContainer(
//                 duration: const Duration(milliseconds: 500),
//                 width: currentIndex == entry.key ? 12.0 : 8.0,
//                 height: 8.0,
//                 margin: const EdgeInsets.symmetric(horizontal: 4.0),
//                 decoration: BoxDecoration(
//                   shape: BoxShape.circle,
//                   color: currentIndex == entry.key
//                       ? AppColors.primary
//                       : Colors.grey,
//                 ),
//               ),
//             );
//           }).toList(),
//         ),
//       ],
//     );
//   }
// }

import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:gap/gap.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class DotIndicatorCarousel extends StatefulWidget {
  final List<String> imageUrls;

  const DotIndicatorCarousel({
    super.key,
    required this.imageUrls,
  });

  @override
  State<DotIndicatorCarousel> createState() => _DotIndicatorCarouselState();
}

class _DotIndicatorCarouselState extends State<DotIndicatorCarousel> {
  int currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    final imageUrls = widget.imageUrls;

    // Nếu danh sách rỗng, hiển thị ảnh mặc định
    if (imageUrls.isEmpty) {
      return Column(
        children: [
          Container(
            height: 22.h,
            width: double.infinity,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(5),
            ),
            child: const Icon(Icons.image_not_supported,
                size: 50, color: Colors.grey),
          ),
        ],
      );
    }

    return Column(
      children: [
        // Carousel Slider
        CarouselSlider.builder(
          itemCount: imageUrls.length,
          itemBuilder: (BuildContext context, int index, int realIndex) {
            return ClipRRect(
              borderRadius: BorderRadius.circular(5),
              child: Image.network(
                imageUrls[index],
                fit: BoxFit.cover,
                width: double.infinity,
                errorBuilder: (context, error, stackTrace) {
                  return const Center(
                    child: Icon(Icons.broken_image, color: Colors.grey),
                  );
                },
              ),
            );
          },
          options: CarouselOptions(
            height: 22.h,
            autoPlay: true,
            enlargeCenterPage: true,
            onPageChanged: (index, reason) {
              setState(() {
                currentIndex = index;
              });
            },
          ),
        ),
        Gap(1.h),
        // Dot Indicator
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: imageUrls.asMap().entries.map((entry) {
            return GestureDetector(
              onTap: () => setState(() => currentIndex = entry.key),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 500),
                width: currentIndex == entry.key ? 12.0 : 8.0,
                height: 8.0,
                margin: const EdgeInsets.symmetric(horizontal: 4.0),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: currentIndex == entry.key
                      ? AppColors.primary
                      : Colors.grey,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}
