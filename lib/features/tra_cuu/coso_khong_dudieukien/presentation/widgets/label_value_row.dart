// import 'package:flutter/material.dart';
// import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';

// class LabelValueRow extends StatelessWidget {
//   final String label;
//   final String value;
//   final double fontSize;
//   final FontWeight labelFontWeight;
//   final FontWeight valueFontWeight;
//   final FontStyle valueFontStyle;
//   final int? maxLine;

//   const LabelValueRow({
//     super.key,
//     required this.label,
//     required this.value,
//     this.fontSize = 12,
//     this.labelFontWeight = FontWeight.w400,
//     this.valueFontWeight = FontWeight.normal,
//     this.valueFontStyle = FontStyle.normal,
//     this.maxLine,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return Row(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         TextWidget(
//           text: "$label: ",
//           size: fontSize,
//           fontWeight: labelFontWeight,
//         ),
//         Expanded(
//           child: TextWidget(
//             text: value.isNotEmpty ? value : "Chưa cập nhật",
//             size: fontSize,
//             fontWeight: valueFontWeight,
//             fontStyle: valueFontStyle,
//             maxLines: maxLine ?? 1000,
//           ),
//         ),
//       ],
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';

class LabelValueRow extends StatelessWidget {
  final String label;
  final String value;
  final double fontSize;
  final FontWeight labelFontWeight;
  final FontWeight valueFontWeight;
  final FontStyle valueFontStyle;
  final int? maxLine;
  final bool enableTooltip; // Thêm thuộc tính bật/tắt Tooltip

  const LabelValueRow({
    super.key,
    required this.label,
    required this.value,
    this.fontSize = 13,
    this.labelFontWeight = FontWeight.w400,
    this.valueFontWeight = FontWeight.normal,
    this.valueFontStyle = FontStyle.normal,
    this.maxLine,
    this.enableTooltip = true, // Mặc định bật Tooltip
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextWidget(
          text: "$label: ",
          size: fontSize,
          fontWeight: labelFontWeight,
        ),
        Expanded(
          child: enableTooltip
              ? Tooltip(
                  message: value.isNotEmpty ? value : "Chưa cập nhật",
                  child: TextWidget(
                    text: value.isNotEmpty ? value : "Chưa cập nhật",
                    size: fontSize,
                    fontWeight: valueFontWeight,
                    fontStyle: valueFontStyle,
                    maxLines: maxLine ?? 1,
                  ),
                )
              : TextWidget(
                  text: value.isNotEmpty ? value : "Chưa cập nhật",
                  size: fontSize,
                  fontWeight: valueFontWeight,
                  fontStyle: valueFontStyle,
                  maxLines: maxLine ?? 1,
                ),
        ),
      ],
    );
  }
}
