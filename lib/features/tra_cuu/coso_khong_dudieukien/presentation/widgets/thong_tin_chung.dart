import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/utils/convert_text.dart';
import '../../../../../core/utils/date_time.dart';
import '../controller/coso_kdudk_detail_controller.dart';
import 'image_carousel.dart';

class ThongTinChung extends StatelessWidget {
  final CoSoKhongDuDieuKienDetailController controller;
  const ThongTinChung({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading.value) {
        return _buildShimmerDetails();
      }
      if (controller.csSxkdDetail.isEmpty) {
        return Center(
          child: Text(
            "Không có thông tin cơ sở.",
            style: TextStyle(fontSize: 16, color: AppColors.primary),
          ),
        );
      }
      final cs = controller.csSxkdDetail.first;
      const defaultText = "Đang cập nhật";
      return SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Thông tin cơ sở sản xuất kinh doanh",
                style: TextStyle(
                    fontSize: AppDimens.mediumText,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary),
              ),
              const Gap(10),
              DotIndicatorCarousel(
                imageUrls: convertImageStringToList(
                  controller.url.value,
                  controller.csSxkdDetail.first.imgHinhAnhNDD
                      .toString()
                      .replaceFirst(RegExp(r'^/+'), ''),
                ),
              ),
              const SizedBox(height: 16),
              _buildInfoRow("Mã số:", cs.lblMaCoSo_TTC ?? defaultText,
                  icon: Icons.tag),
              _buildInfoRow("Tên cơ sở:", cs.lblTenCoSo_TTC ?? defaultText,
                  icon: Icons.business),
              _buildInfoRow(
                  "Số giấy phép ĐKKD:", cs.lblSoGiayPhepDKKDTTC ?? defaultText,
                  icon: Icons.document_scanner),
              _buildInfoRow(
                "Ngày cấp:",
                cs.lblNgayCapDKKDTTC != null
                    ? DatetimeUtil.formatCustom(cs.lblNgayCapDKKDTTC)
                    : defaultText,
                icon: Icons.calendar_today,
              ),
              _buildInfoRow(
                  "Cơ quan cấp:", cs.lblCoQuanCapDKKDTTC ?? defaultText,
                  icon: Icons.apartment),
              _buildInfoRow(
                  "Số điện thoại:", cs.lblSoDienThoaiTTC ?? defaultText,
                  icon: Icons.phone),
              _buildInfoRow("Số fax:", cs.lblSoFaxTTC ?? defaultText,
                  icon: Icons.phone),
              _buildInfoRow("Email:", cs.lblEmailTTC ?? defaultText,
                  icon: Icons.email),
              _buildInfoRow("Website:", cs.lblWebsiteTTC ?? defaultText,
                  icon: Icons.web),
              _buildInfoRow("Tỉnh/thành phố:", cs.lblTinhTTC ?? defaultText,
                  icon: Icons.location_city),
              _buildInfoRow("Quận/huyện:", cs.lblHuyenTTC ?? defaultText,
                  icon: Icons.location_city),
              _buildInfoRow("Phường/xã:", cs.lblXaTTC ?? defaultText,
                  icon: Icons.location_city),
              _buildInfoRow("Thôn/ấp:", cs.lblThonTTC ?? defaultText,
                  icon: Icons.location_city),
              _buildInfoRow("Địa chỉ cơ sở:", cs.lblDiaChiTTC ?? defaultText,
                  icon: Icons.location_on),
              _buildInfoRow(
                  "Loại hình cơ sở:", cs.lblLoaiHinhCoSoTTC ?? defaultText,
                  icon: Icons.stacked_bar_chart),
              // _buildInfoRow(
              //     "Mặt hàng sản xuất, kinh doanh:", cs.matHangSX ?? defaultText,
              //     icon: Icons.location_city),
              _buildInfoRow(
                  "Năm hoạt động:", cs.lblNamHoatDongTTC ?? defaultText,
                  icon: Icons.date_range),
              _buildInfoRow("Trạng thái:", cs.lblTrangThaiHDTTC ?? defaultText,
                  icon: Icons.stacked_bar_chart),
              // _buildInfoRow("Năm hoạt động:", cs.san ?? defaultText,
              //   icon: Icons.date_range),
              const Gap(10),
              // _buildMapSection(),
              const SizedBox(height: 16),
              const Divider(),
              const Gap(8),
              Text(
                "Người đại diện pháp luật",
                style: TextStyle(
                    fontSize: AppDimens.mediumText,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary),
              ),
              const Gap(8),
              _buildInfoRow("Họ và tên:", cs.lblHoVaTenNDD ?? defaultText,
                  icon: Icons.person),
              _buildInfoRow("Chức vụ:", cs.lblChucVuNDD ?? defaultText,
                  icon: Icons.badge),
              _buildInfoRow("Giới tính:", cs.lblGioiTinhNDD ?? defaultText,
                  icon: Icons.male),
              _buildInfoRow("Số CMND/CCCD:", cs.lblCCCDNDD ?? defaultText,
                  icon: Icons.perm_identity),

              _buildInfoRow(
                  "Ngày cấp:",
                  cs.lblNgayCapDKKDTTC != null
                      ? DatetimeUtil.formatCustom(cs.lblCCCD_NDD)
                      : defaultText,
                  icon: Icons.calendar_today),
              _buildInfoRow("Nơi cấp:", cs.lblNoiCap_NDD ?? defaultText,
                  icon: Icons.local_police),
              _buildInfoRow("Địa chỉ:", cs.lblDiaChiNDD ?? defaultText,
                  icon: Icons.home),
              _buildInfoRow("Dân tộc:", cs.lblDanTocNDD ?? defaultText,
                  icon: Icons.group),
              _buildInfoRow("Quốc tịch:", cs.lblQuocTichNDD ?? defaultText,
                  icon: Icons.flag),
              _buildInfoRow("Email:", cs.lblEmailNDD ?? defaultText,
                  icon: Icons.email),
              _buildInfoRow(
                  "Số điện thoại:", cs.lblSoDienThoaiNDD ?? defaultText,
                  icon: Icons.phone),
              // _buildInfoRow("Số điện thoại:", cs.imgHinhAnhNDD ?? defaultText,
              //     icon: Icons.phone),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildInfoRow(String label, String value, {IconData? icon}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (icon != null)
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: Icon(
                icon,
                size: 20,
                color: AppColors.primary,
              ),
            ),
          SizedBox(
            width: 140, // Đặt chiều rộng cố định cho label
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(height: 1.5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShimmerDetails() {
    return SizedBox(
      height: 80.h,
      child: ListView.builder(
        itemCount: 5,
        itemBuilder: (context, index) {
          return Padding(
            padding: const EdgeInsets.all(8.0),
            child: Container(
              height: 20,
              color: Colors.grey[300],
            ),
          );
        },
      ),
    );
  }
}
