import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gap/gap.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/button/button_widget.dart';
import 'package:attp_2024/core/ui/widgets/custom_combo/combo.dart';
import 'package:attp_2024/features/tra_cuu/coso_khong_dudieukien/presentation/controller/coso_kdu_dieukien_controller.dart';

class FilterModal extends GetView<CoSoKhongDuDieuKienController> {
  const FilterModal({super.key});

  static void show(BuildContext context) {
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (context) {
        return SizedBox(height: 80.h, child: const FilterModal());
      },
    );
  }

  Widget buildCombobox({
    required String title,
    required RxList<dynamic> items,
    required Function(DropdownModel?) onChange,
    required String Function(dynamic) displayMapper,
    required String Function(dynamic) valueMapper,
    Function()? onDelete,
    DropdownModel? defaultSelectedItem, // Thêm tham số defaultSelectedItem
    bool isEnabled = true,
    bool showDelete = true,
  }) {
    return Obx(() => CustomCombobox(
          title: title,
          weight: 100.w,
          onChange: (selected) => onChange(selected),
          delete: onDelete,
          defaultSelectedItem: defaultSelectedItem, // Truyền giá trị mặc định
          dropDownList: items
              .map((e) => DropdownModel(
                    id: valueMapper(e),
                    display: displayMapper(e),
                  ))
              .toList(),
          isEnabled: isEnabled,
          showDelete: showDelete,
        ));
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Divider(
            color: Colors.grey.withOpacity(0.3),
            thickness: 3.5,
            indent: 150,
            endIndent: 150,
          ),
          Row(
            children: [
              const Spacer(),
              const Spacer(),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Tra cứu nâng cao',
                    style: TextStyle(
                      fontSize: AppDimens.largeText,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryFocus,
                    ),
                  ),
                ],
              ),
              const Spacer(),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  IconButton(
                    icon: const Icon(
                      Icons.clear,
                      color: Colors.red,
                    ),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ],
              ),
            ],
          ),
          Gap(1.h),
          Expanded(
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: buildCombobox(
                        title: "Tỉnh/Thành Phố",
                        items: controller.provinces,
                        defaultSelectedItem:
                            controller.provinceFilter.value.isNotEmpty
                                ? DropdownModel(
                                    id: controller.provinceFilter.value,
                                    display: controller.provinces.firstWhere(
                                      (province) =>
                                          province['value'] ==
                                          controller.provinceFilter.value,
                                      orElse: () => {'display': ''},
                                    )['display'],
                                  )
                                : null,
                        onChange: (selected) {
                          if (selected != null) {
                            controller.provinceFilter.value = selected.id!;
                            controller.fetchAllDistricts(tinhID: selected.id!);
                          }
                        },
                        onDelete: () {
                          controller.clearProvinceFilters();
                        },
                        displayMapper: (e) => e['display'],
                        valueMapper: (e) => e['value'],
                        isEnabled: controller.userGroup.value == 'Admin'
                            ? true
                            : false,
                        showDelete: controller.userGroup.value == 'Admin'
                            ? true
                            : false,
                      ),
                    ),
                    const Gap(10),
                    Expanded(
                      child: buildCombobox(
                        title: "Quận/Huyện",
                        items: controller.districts,
                        defaultSelectedItem:
                            controller.provinceFilter.value.isNotEmpty
                                ? DropdownModel(
                                    id: controller.districtFilter.value,
                                    display: controller.districts.firstWhere(
                                      (province) =>
                                          province['value'] ==
                                          controller.districtFilter.value,
                                      orElse: () => {'display': ''},
                                    )['display'],
                                  )
                                : null,
                        onChange: (selected) {
                          if (selected != null) {
                            controller.districtFilter.value = selected.id!;
                            controller.fetchAllCommunes(huyenID: selected.id!);
                          }
                        },
                        onDelete: () {
                          controller.clearDistrictFilters();
                        },
                        displayMapper: (e) => e['display'],
                        valueMapper: (e) => e['value'],
                        isEnabled:
                            controller.userGroup.value == 'User' ? false : true,
                        showDelete:
                            controller.userGroup.value == 'User' ? false : true,
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Expanded(
                      child: buildCombobox(
                        title: "Xã/Phường",
                        items: controller.communes,
                        onChange: (selected) {
                          if (selected != null) {
                            controller.communeFilter.value = selected.id!;
                            controller.fetchAllVillages(xaID: selected.id!);
                          }
                        },
                        defaultSelectedItem:
                            controller.communeFilter.value.isNotEmpty
                                ? DropdownModel(
                                    id: controller.communeFilter.value,
                                    display: controller.communes.firstWhere(
                                      (province) =>
                                          province['value'] ==
                                          controller.communeFilter.value,
                                      orElse: () => {'display': ''},
                                    )['display'],
                                  )
                                : null,
                        onDelete: () {
                          controller.clearCommuneFilters();
                        },
                        displayMapper: (e) => e['display'],
                        valueMapper: (e) => e['value'],
                      ),
                    ),
                    const Gap(10),
                    Expanded(
                      child: buildCombobox(
                        title: "Thôn/Xóm",
                        items: controller.villages,
                        onChange: (selected) {
                          if (selected != null) {
                            controller.villageFilter.value = selected.id!;
                          }
                        },
                        defaultSelectedItem:
                            controller.villageFilter.value.isNotEmpty
                                ? DropdownModel(
                                    id: controller.villageFilter.value,
                                    display: controller.villages.firstWhere(
                                      (province) =>
                                          province['value'] ==
                                          controller.villageFilter.value,
                                      orElse: () => {'display': ''},
                                    )['display'],
                                  )
                                : null,
                        displayMapper: (e) => e['display'],
                        valueMapper: (e) => e['value'],
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Expanded(
                      child: buildCombobox(
                        title: "Loại hình cơ sở",
                        items: controller.loaiHinhCSs,
                        onChange: (selected) {
                          controller.loaihinhCsId.value = selected?.id ?? '';
                        },
                        defaultSelectedItem:
                            controller.loaihinhCsId.value.isNotEmpty
                                ? DropdownModel(
                                    id: controller.loaihinhCsId.value,
                                    display: controller.loaiHinhCSs.firstWhere(
                                      (province) =>
                                          province['value'] ==
                                          controller.loaihinhCsId.value,
                                      orElse: () => {'display': ''},
                                    )['display'],
                                  )
                                : null,
                        displayMapper: (e) => e['display'],
                        valueMapper: (e) => e['value'],
                      ),
                    ),
                    const Gap(10),
                    Expanded(
                      child: buildCombobox(
                        title: "Tình trạng ký GCK",
                        items: controller.tinhTrangCapGCN,
                        onChange: (selected) {
                          controller.tinhtrangCapGCNId.value =
                              selected?.id ?? '';
                        },
                        defaultSelectedItem: controller
                                .tinhtrangCapGCNId.value.isNotEmpty
                            ? DropdownModel(
                                id: controller.tinhtrangCapGCNId.value,
                                display: controller.tinhTrangCapGCN.firstWhere(
                                  (province) =>
                                      province['value'] ==
                                      controller.tinhtrangCapGCNId.value,
                                  orElse: () => {'display': ''},
                                )['display'],
                              )
                            : null,
                        displayMapper: (e) => e['display'],
                        valueMapper: (e) => e['value'],
                      ),
                    ),
                  ],
                ),
                Column(
                  children: [
                    buildCombobox(
                      // width: 100.w,
                      title: "Trạng thái hoạt động",
                      items: controller.tinhTrangHDs,
                      onChange: (selected) {
                        controller.trangthaiHDId.value = selected?.id ?? '';
                      },
                      defaultSelectedItem:
                          controller.trangthaiHDId.value.isNotEmpty
                              ? DropdownModel(
                                  id: controller.trangthaiHDId.value,
                                  display: controller.tinhTrangHDs.firstWhere(
                                    (province) =>
                                        province['value'] ==
                                        controller.trangthaiHDId.value,
                                    orElse: () => {'display': ''},
                                  )['display'],
                                )
                              : null,
                      displayMapper: (e) => e['display'],
                      valueMapper: (e) => e['value'],
                    ),
                  ],
                ),
                const Spacer(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ButtonWidget(
                      text: 'Áp dụng',
                      ontap: () {
                        // Todo: Implement search functionality
                        controller.fetchSearchResultsWithFilters();
                        Navigator.of(context).pop();
                      },
                      width: MediaQuery.of(context).size.width * 0.90,
                      backgroundColor: Colors.green,
                      textColor: AppColors.white,
                      isBorder: true,
                      borderColor: Colors.grey[100],
                      borderRadius: 6,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12.0, vertical: 10.0),
                      textSize: 16.0,
                    ),
                  ],
                ),
                const Gap(10)
              ],
            ),
          ),
        ],
      ),
    );
  }
}
