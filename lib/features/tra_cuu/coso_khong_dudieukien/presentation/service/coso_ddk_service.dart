import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/cssxkd_model.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/cssxkd_nhanvien_model.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/cssxkd_qlcl.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/cssxkd_sanpham_model.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/cssxkd_tailieu_model.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/cssxkd_trangtb_model.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/giay_chungnhan_model.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import '../../../../../core/data/api/services/proc/proc_service.dart';

class CoSoDuDieuKienService {
  final ProcService _procService = Get.find<ProcService>();
  Future<List<T>> fetchData<T>(
      String procedureName,
      List<Map<String, dynamic>> params,
      T Function(Map<String, dynamic>) fromJson) async {
    final response = await _procService.callProc(procedureName, params);
    return response.isNotEmpty
        ? response
            .map<T>((item) => fromJson(item as Map<String, dynamic>))
            .toList()
        : [];
  }

  Future<List<CoSoSXKDSanPham>> fetchSanPham(String id) async {
    return fetchData(
      "Proc_Mobile_GetAll_CoSoSXKDSanPham_By_CoSoSXKDID",
      [
        {"Type": "guid", "Name": "ID", "Value": id},
      ],
      (json) => CoSoSXKDSanPham.fromJson(json),
    );
  }

  // ignore: non_constant_identifier_names
  Future<List<CSSXKD>> fetchCoSoSXKD_ByID(String id) async {
    return fetchData(
      "Proc_GetCoSoSXKD_ByID",
      [
        {"Type": "guid", "Name": "ID", "Value": id}
      ],
      (json) => CSSXKD.fromJson(json),
    );
  }

  Future<List<CoSoSXKDTTB>> fetchTrangThietBi(String id) async {
    return fetchData(
      "Proc_Mobile_GetAll_CoSoSXKDTTB_By_CoSoSXKDID",
      [
        {"Type": "guid", "Name": "CoSoSXKDID", "Value": id}
      ],
      (json) => CoSoSXKDTTB.fromJson(json),
    );
  }

  Future<List<CoSoSXKDTTB>> fetchDsGCNATTP(String id) async {
    return fetchData(
      "Proc_Mobile_GetAll_CoSoSXKDTTB_By_CoSoSXKDID",
      [
        {"Type": "guid", "Name": "CoSoSXKDID", "Value": id}
      ],
      (json) => CoSoSXKDTTB.fromJson(json),
    );
  }

  Future<List<dynamic>> fetchCoSoSXKDXLVP(String id) async {
    return fetchData(
      "Proc_GetAll_CoSoSXKDXLVP",
      [
        {"Type": "guid", "Name": "CoSoSXKDID", "Value": id}
      ],
      (json) => json,
    );
  }

  Future<List<dynamic>> fetchGetPhaAnhXLVP(String id) async {
    return fetchData(
      "Proc_GetPhaAnh_XLVP",
      [
        {"Type": "guid", "Name": "CoSoSXKDID", "Value": id}
      ],
      (json) => json,
    );
  }

  //Proc_GetAll_CoSoSXKDQLCL
  Future<List<CoSoSXKDQLCL>> fetchGetAllCoSoSXKDQLCL(String id) async {
    return fetchData(
      "Proc_GetAll_CoSoSXKDQLCL",
      [
        {"Type": "guid", "Name": "ID", "Value": id}
      ],
      (json) => CoSoSXKDQLCL.fromJson(json),
    );
  }

  Future<List<GiayChungNhanATTP>> fetchGCKATTP(String id) async {
    return fetchData(
      "Proc_GetAll_CoSoSXKDCapGCN",
      [
        {"Type": "guid", "Name": "ID", "Value": id},
        {"Type": "String", "Name": "Loai", "Value": 'KyGCK'}
      ],
      (json) => GiayChungNhanATTP.fromJson(json),
    );
  }

  Future<List<GiayChungNhanATTP>> fetchGCNATTP(String id) async {
    return fetchData(
      "Proc_GetAll_CoSoSXKDCapGCN",
      [
        {"Type": "guid", "Name": "ID", "Value": id},
        {"Type": "String", "Name": "Loai", "Value": 'CapGCN'}
      ],
      (json) => GiayChungNhanATTP.fromJson(json),
    );
  }


  Future<List<CoSoSXKDNhanVien>> fetchNhanVien(String id) async {
    return fetchData(
      "Proc_GetAll_CoSoSXKDNhanVien",
      [
        {"Type": "guid", "Name": "ID", "Value": id}
      ],
      (json) => CoSoSXKDNhanVien.fromJson(json),
    );
  }

  //Danh sách tài liệu
  Future<List<CoSoSXKDTL>> fetchCoSoSXKDTL(String id) async {
    return fetchData(
      "Proc_GetAll_CoSoSXKDTL",
      [
        {"Type": "guid", "Name": "ID", "Value": id}
      ],
      (json) => CoSoSXKDTL.fromJson(json),
    );
  }

  // Ký giấy cam kết
  Future<dynamic> fetchCoSoSXKDKyGCK() async {
    return fetchData(
      "Proc_GetAll_CoSoSXKD_KyGCK",
      [
        {
          "Type": "guid",
          "Name": "ID",
          "Value": "00000000-0000-0000-0000-000000000000"
        }
      ],
      (json) => json,
    );
  }

  //Proc_GetAll_CoSoSXKDTL
}

    //Proc_GetCapGCN_ByID
    //Proc_GetAllCoSoSXKDQLCL_ByID
    //Proc_GetAllCoSoSXKDSanPham_ByID
    //Proc_GetAll_CoSoSXKDTTB
    //Proc_GetAllCoSoSXKDTTB_ByID
    //Proc_GetAllCoSoSXKDHCPG_ByID
    //Proc_GetAllCoSoSXKDNhanVien_ByID
    //Proc_GetAllCoSoSXKDQLCL_ByID
    //Proc_GetAllCoSoSXKDTL_ByID
    //Proc_GetAll_CoSoSXKDBienBanTD
    //Proc_GetAll_CoSoSXKDXLVP
    //Proc_GetPhaAnh_XLVP