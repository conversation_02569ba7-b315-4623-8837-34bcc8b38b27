import 'package:attp_2024/core/data/models/user_access_model.dart';
import 'package:attp_2024/core/services/user_use_case.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/cssxkd_model.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/cssxkd_nhanvien_model.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/cssxkd_qlcl.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/cssxkd_sanpham_model.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/cssxkd_tailieu_model.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/cssxkd_trangtb_model.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/giay_chungnhan_model.dart';
import '../service/coso_ddk_service.dart';

class CoSoKhongDuDieuKienDetailController extends GetxController
    with GetSingleTickerProviderStateMixin {
  final CoSoDuDieuKienService _service = CoSoDuDieuKienService();
  final log = Logger();

  var isLoading = false.obs;
  var sanPhamList = <CoSoSXKDSanPham>[].obs;
  var nhanVienList = <CoSoSXKDNhanVien>[].obs;
  var trangThietBiList = <CoSoSXKDTTB>[].obs;
  var tailieuList = <CoSoSXKDTL>[].obs;
  var gcnDbClList = <CoSoSXKDQLCL>[].obs;
  var csSxkdDetail = <CSSXKD>[].obs;
  var gcnATTPList = <GiayChungNhanATTP>[].obs;

  late TabController tabController;
  final PageController pageController = PageController();

  UserAccessModel? userAccessModel;
  var url = ''.obs;

  @override
  void onInit() {
    super.onInit();
    final String? id = Get.arguments;
    tabController = TabController(length: 6, vsync: this);
    tabController.addListener(() {
      if (tabController.indexIsChanging) {
        pageController.jumpToPage(tabController.index);
      }
    });
    if (id?.isNotEmpty == true) {
      fetchDetails(id!);
      loadInfoProfile();
    } else {
      log.w("No valid ID passed to the detail page or ID is null");
    }
  }

  Future<void> fetchDetails(String id) async {
    try {
      isLoading(true);
      final results = await Future.wait([
        _service.fetchSanPham(id),
        _service.fetchTrangThietBi(id),
        _service.fetchNhanVien(id),
        _service.fetchCoSoSXKDTL(id),
        _service.fetchGetAllCoSoSXKDQLCL(id),
        _service.fetchCoSoSXKD_ByID(id),
        _service.fetchGCKATTP(id)
      ]);
      sanPhamList.value = results[0] as List<CoSoSXKDSanPham>;
      trangThietBiList.value = results[1] as List<CoSoSXKDTTB>;
      nhanVienList.value = results[2] as List<CoSoSXKDNhanVien>;
      tailieuList.value = results[3] as List<CoSoSXKDTL>;
      gcnDbClList.value = results[4] as List<CoSoSXKDQLCL>;
      csSxkdDetail.value = results[5] as List<CSSXKD>;
      gcnATTPList.value = results[6] as List<GiayChungNhanATTP>;
    } catch (e) {
      log.e("Error fetching details: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> loadInfoProfile() async {
    userAccessModel = await UserUseCase.getUser();
    url.value = userAccessModel!.siteURL.toString();
    print(url.value);
  }
}
