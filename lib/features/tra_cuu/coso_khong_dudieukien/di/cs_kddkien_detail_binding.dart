import 'package:get/get.dart';

import '../../../../core/data/api/configs/dio_configs.dart';
import '../../../../core/data/api/services/proc/proc_service.dart';
import '../presentation/controller/coso_kdudk_detail_controller.dart';

class CoSoKhongDuDieuKienDetailBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<CoSoKhongDuDieuKienDetailController>(
      () => CoSoKhongDuDieuKienDetailController(),
    );
    Get.lazyPut(() => ProcService(Get.find<DioService>()));
  }
}
