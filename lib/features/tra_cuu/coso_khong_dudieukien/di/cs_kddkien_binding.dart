import 'package:get/get.dart';

import '../../../../core/data/api/configs/dio_configs.dart';
import '../../../../core/data/api/services/proc/proc_service.dart';
import '../presentation/controller/coso_kdu_dieukien_controller.dart';


class CoSoKDuDieuKienBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<CoSoKhongDuDieuKienController>(
      () => CoSoKhongDuDieuKienController(),
    );
    Get.lazyPut(() => ProcService(Get.find<DioService>()));
  }
}
