import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class CustomCard extends StatelessWidget {
  final LabelValuePair title;
  final String statusText;
  final Color statusColor;
  final List<LabelValuePair> labelValueList;
  final VoidCallback? onTap;
  final String? imgPath;

  const CustomCard({
    super.key,
    required this.title,
    required this.statusText,
    required this.statusColor,
    required this.labelValueList,
    this.imgPath = AppImageString.imgaeCapGcn,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        elevation: 2,
        margin: EdgeInsets.symmetric(vertical: .3.h, horizontal: 0.w),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
        child: Padding(
          padding: EdgeInsets.all(2.w),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Column(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(5),
                    child: Image.asset(
                      imgPath!,
                      width: 18.w,
                      height: 10.h,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Icon(Icons.image,
                            size: 15.w, color: Colors.grey);
                      },
                    ),
                  ),
                  Gap(.1.h),
                  Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 2.w, vertical: .5.h),
                    decoration: BoxDecoration(
                      color: statusColor,
                      borderRadius: BorderRadius.circular(5),
                    ),
                    child: Text(
                      statusText,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 13.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              Gap(2.w),
              // Nội dung thông tin chứng nhận
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildLabelRow(title, isTitle: true),
                    ...labelValueList.expand((labelValue) {
                      if (labelValue.label == "Tên cơ sở") {
                        return [
                          Divider(color: AppColors.primary),
                          _buildLabelRow(labelValue),
                        ];
                      }
                      return [_buildLabelRow(labelValue)];
                    }),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLabelRow(LabelValuePair labelValue, {bool isTitle = false}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: .3.h),
      child: Row(
        children: [
          Icon(_getIcon(labelValue.label), size: 14.sp, color: Colors.black54),
          Gap(1.w),
          Text(
            "${labelValue.label}: ",
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: isTitle ? 14.sp : 14.sp,
            ),
          ),
          Expanded(
            child: Text(
              labelValue.value,
              style: TextStyle(fontSize: 13.5.sp),
              textScaler: TextScaler.noScaling,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // Xác định icon phù hợp với từng loại dữ liệu
  IconData _getIcon(String label) {
    //Ngày cấp
    switch (label) {
      case "Số GCN":
        return Icons.tag;
      case "Ngày ký":
        return Icons.date_range;
      case "Ngày hết hạn":
        return Icons.date_range;
      case "Số GCK":
        return Icons.tag;
      case "Cơ quan ban hành":
        return Icons.apartment;
      case "Tên cơ sở":
        return Icons.business;
      case "Số ĐKKD":
        return Icons.confirmation_number;
      case "Loại hình cơ sở":
        return Icons.category;
      case "Địa chỉ":
        return Icons.location_on;
      default:
        return Icons.info_outline;
    }
  }
}

// Model dữ liệu cho từng dòng thông tin
class LabelValuePair {
  final String label;
  final String value;

  LabelValuePair({required this.label, required this.value});
}
