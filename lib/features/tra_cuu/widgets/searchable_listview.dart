import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class SearchableListView<T> extends StatefulWidget {
  final String title;
  final List<T> items;
  final String Function(T) searchBy;
  final Widget Function(T) itemBuilder;
  final Function()? onFilter;

  const SearchableListView({
    super.key,
    required this.title,
    required this.items,
    required this.searchBy,
    required this.itemBuilder,
    this.onFilter,
  });

  @override
  _SearchableListViewState<T> createState() => _SearchableListViewState<T>();
}

class _SearchableListViewState<T> extends State<SearchableListView<T>> {
  String searchQuery = "";

  @override
  Widget build(BuildContext context) {
    List<T> filteredItems = widget.items
        .where((item) => widget
            .searchBy(item)
            .toLowerCase()
            .contains(searchQuery.toLowerCase()))
        .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 🔍 Thanh tìm kiếm
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 1.h),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  onChanged: (value) {
                    setState(() {
                      searchQuery = value;
                    });
                  },
                  decoration: InputDecoration(
                    hintText: "Nội dung tìm kiếm...",
                    hintStyle: TextStyle(color: Colors.grey.shade600),
                    prefixIcon: Icon(Icons.search, color: Colors.grey.shade600),
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(5),
                        borderSide: BorderSide(color: Colors.grey.shade300),
                        gapPadding: 1),
                    enabledBorder: OutlineInputBorder(
                      gapPadding: 1,
                      borderRadius: BorderRadius.circular(5),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    contentPadding: EdgeInsets.symmetric(vertical: 1.1.h),
                    filled: true,
                    fillColor: Colors.grey.shade100,
                  ),
                ),
              ),
              Gap(1.w),
              // Nút bộ lọc
              IconButton(
                icon: const Icon(Icons.filter_list_outlined),
                color: Colors.grey.shade600,
                onPressed: widget.onFilter,
              ),
            ],
          ),
        ),

        // 📌 Kết quả tìm kiếm
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 1.w),
          child: Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: "Kết quả tìm kiếm: ",
                  style: TextStyle(
                    fontSize: 15.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
                ),
                TextSpan(
                  text: "${filteredItems.length}",
                  style: TextStyle(
                    fontSize: 15.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
          ),
        ),
        Divider(color: Colors.grey.shade300, indent: 2.w, endIndent: 2.w),

        // 📋 Danh sách kết quả
        Expanded(
          child: ListView.builder(
            padding: EdgeInsets.all(8.0),
            itemCount: filteredItems.length,
            itemBuilder: (context, index) {
              return widget.itemBuilder(filteredItems[index]);
            },
          ),
        ),
      ],
    );
  }
}
