// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:responsive_sizer/responsive_sizer.dart';

// class LabelValueRow extends StatelessWidget {
//   final String label;
//   final String value;
//   final double fontSize;
//   final FontWeight labelFontWeight;
//   final FontWeight valueFontWeight;
//   final FontStyle valueFontStyle;
//   final int? maxLine;
//   final bool enableTooltip;

//   const LabelValueRow({
//     super.key,
//     required this.label,
//     required this.value,
//     this.fontSize = 14,
//     this.labelFontWeight = FontWeight.w100,
//     this.valueFontWeight = FontWeight.normal,
//     this.valueFontStyle = FontStyle.normal,
//     this.maxLine,
//     this.enableTooltip = true,
//   });

//   @override
//   Widget build(BuildContext context) {
//     final textTheme = CupertinoTheme.of(context).textTheme;
//     const defaultText = "Đang cập nhật";
//     return Row(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           "$label: ",
//           style: textTheme.textStyle.copyWith(
//             fontSize: fontSize.sp,
//             fontWeight: labelFontWeight,
//             color: CupertinoColors.label,
//           ),
//         ),
//         Expanded(
//           child: enableTooltip
//               ? Tooltip(
//                   message: value.isNotEmpty ? value : defaultText,
//                   child: Text(
//                     value.isNotEmpty ? value : defaultText,
//                     style: textTheme.textStyle.copyWith(
//                       fontSize: fontSize.sp - .5,
//                       fontWeight: valueFontWeight,
//                       fontStyle: valueFontStyle,
//                       color: value.isNotEmpty
//                           ? CupertinoColors.label
//                           : CupertinoColors.label,
//                     ),
//                     maxLines: maxLine ?? 1,
//                     overflow: TextOverflow.ellipsis,
//                   ),
//                 )
//               : Text(
//                   value.isNotEmpty ? value : defaultText,
//                   style: textTheme.textStyle.copyWith(
//                     fontSize: fontSize.sp,
//                     fontWeight: valueFontWeight,
//                     fontStyle: valueFontStyle,
//                     color: value.isNotEmpty
//                         ? CupertinoColors.label
//                         : CupertinoColors.label,
//                   ),
//                   maxLines: maxLine ?? 1,
//                   overflow: TextOverflow.ellipsis,
//                 ),
//         ),
//       ],
//     );
//   }
// }

import 'package:attp_2024/core/configs/contents/app_content.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class LabelValueRow extends StatelessWidget {
  final String label;
  final String value;
  final double fontSize;
  final FontWeight labelFontWeight;
  final FontWeight valueFontWeight;
  final FontStyle valueFontStyle;
  final int? maxLine;
  final bool enableTooltip;

  const LabelValueRow({
    super.key,
    required this.label,
    required this.value,
    this.fontSize = 14,
    this.labelFontWeight = FontWeight.w100,
    this.valueFontWeight = FontWeight.normal,
    this.valueFontStyle = FontStyle.normal,
    this.maxLine,
    this.enableTooltip = true,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = CupertinoTheme.of(context).textTheme;
    const defaultText = AppContent.textDefault;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        /// Hiển thị icon theo từng loại dữ liệu
        Icon(_getIcon(label), size: 14.sp, color: Colors.black54),
        SizedBox(width: 1.w),

        /// Hiển thị nhãn (Label)
        Text(
          "$label: ",
          style: textTheme.textStyle.copyWith(
            fontSize: fontSize.sp,
            fontWeight: labelFontWeight,
            color: Colors.black54,
          ),
        ),

        Expanded(
          child: enableTooltip
              ? Tooltip(
                  message: value.isNotEmpty ? value : defaultText,
                  child: Text(
                    value.isNotEmpty ? value : defaultText,
                    style: textTheme.textStyle.copyWith(
                      fontSize: fontSize.sp - .5,
                      fontWeight: valueFontWeight,
                      fontStyle: valueFontStyle,
                      color: value.isNotEmpty ? Colors.black54 : Colors.black54,
                    ),
                    maxLines: maxLine ?? 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                )
              : Text(
                  value.isNotEmpty ? value : defaultText,
                  style: textTheme.textStyle.copyWith(
                    fontSize: fontSize.sp,
                    fontWeight: valueFontWeight,
                    fontStyle: valueFontStyle,
                    color: value.isNotEmpty ? Colors.black54 : Colors.black54,
                  ),
                  maxLines: maxLine ?? 1,
                  overflow: TextOverflow.ellipsis,
                ),
        ),
      ],
    );
  }

  /// Xác định icon phù hợp với từng loại dữ liệu
  // IconData _getIcon(String label) {
  //   switch (label) {
  //     case "Số GCN":
  //     case "Số GCK":
  //       return Icons.tag;
  //     case "Ngày ký":
  //     case "Ngày hết hạn":
  //     case "Ngày cấp":
  //     case "Ngày đăng ký GPKD":
  //       return Icons.date_range;
  //     case "Cơ quan ban hành":
  //       return Icons.apartment;
  //     case "Tên cơ sở":
  //       return Icons.business;
  //     case "Số ĐKKD":
  //       return Icons.confirmation_number;
  //     case "Loại hình cơ sở":
  //       return Icons.category;
  //     case "Địa chỉ":
  //       return Icons.location_on;
  //     case "Người đại diện pháp luật":
  //       return Icons.person;
  //     case "Số điện thoại":
  //       return Icons.phone;
  //     case "Email":
  //       return Icons.email;
  //     case "Người cấp":
  //     case "Người ký":
  //     case "Tên nhân viên":
  //       return Icons.person;
  //     case "Số CMND":
  //       return Icons.person;
  //     case "Số HĐ":
  //       return Icons.person;
  //     case "Vị trí công việc":
  //       return Icons.person;
  //     case "Cơ quan cấp":
  //       return Icons.person;
  //     case "Chức vụ":
  //       return Icons.person;
  //     case "Loại văn bản":
  //       return Icons.person;
  //     case "Loại HĐ":
  //       return Icons.person;
  //     case "Tên sản phẩm":
  //       return Icons.person;
  //     case "Tên nguyên liệu":
  //       return Icons.person;
  //     case "Nguồn gốc":
  //       return Icons.person;
  //     case "Cách thức đóng gói":
  //       return Icons.person;
  //     default:
  //       return Icons.info_outline;
  //   }
  // }

  // IconData _getIcon(String label) {
  //   switch (label) {
  //     case "Số GCN":
  //     case "Số GCK":
  //       return Icons.confirmation_number; // Biểu tượng mã số
  //     case "Ngày ký":
  //     case "Ngày hết hạn":
  //     case "Ngày cấp":
  //     case "Ngày đăng ký GPKD":
  //       return Icons.event; // Biểu tượng lịch
  //     case "Cơ quan ban hành":
  //     case "Cơ quan cấp":
  //       return Icons.account_balance; // Biểu tượng tổ chức chính phủ
  //     case "Tên cơ sở":
  //       return Icons.store; // Biểu tượng cửa hàng/doanh nghiệp
  //     case "Số ĐKKD":
  //       return Icons.assignment; // Biểu tượng hồ sơ/đăng ký
  //     case "Loại hình cơ sở":
  //       return Icons.business; // Biểu tượng doanh nghiệp
  //     case "Địa chỉ":
  //       return Icons.location_on; // Biểu tượng vị trí
  //     case "Người đại diện pháp luật":
  //     case "Người cấp":
  //     case "Người ký":
  //     case "Tên nhân viên":
  //       return Icons.person; // Biểu tượng người
  //     case "Số điện thoại":
  //       return Icons.phone; // Biểu tượng điện thoại
  //     case "Email":
  //       return Icons.email; // Biểu tượng email
  //     case "Số CMND":
  //       return Icons.badge; // Biểu tượng thẻ căn cước/CMND
  //     case "Số HĐ":
  //       return Icons.receipt_long; // Biểu tượng hợp đồng/hóa đơn
  //     case "Vị trí công việc":
  //       return Icons.work; // Biểu tượng công việc
  //     case "Chức vụ":
  //       return Icons.supervisor_account; // Biểu tượng vị trí quản lý
  //     case "Loại văn bản":
  //       return Icons.description; // Biểu tượng tài liệu/văn bản
  //     case "Loại HĐ":
  //       return Icons.assignment_turned_in; // Biểu tượng hợp đồng đã hoàn tất
  //     case "Tên thiết bị":
  //     case "Tên sản phẩm":
  //       return Icons.production_quantity_limits; // Biểu tượng sản phẩm
  //     case "Tên nguyên liệu":
  //       return Icons.inventory; // Biểu tượng nguyên liệu/tồn kho
  //     case "Nguồn gốc":
  //       return Icons.public; // Biểu tượng nguồn gốc địa lý
  //     case "Cách thức đóng gói":
  //       return Icons.archive; // Biểu tượng đóng gói sản phẩm
  //     case "Số lượng":
  //       return Icons.archive;
  //     case "Nước sản xuất":
  //       return Icons.archive;
  //     case "Tổng công suất":
  //       return Icons.archive;
  //     case "Năm bắt đầu sử dụng":
  //       return Icons.archive;
  //     default:
  //       return Icons.info_outline; // Biểu tượng thông tin mặc định
  //   }
  // }

  IconData _getIcon(String label) {
    switch (label) {
      case "Số GCN":
      case "Số GCK":
        return Icons.confirmation_number; // Số giấy chứng nhận, mã số đăng ký

      case "Ngày ký":
      case "Ngày hết hạn":
      case "Ngày cấp":
      case "Ngày đăng ký GPKD":
        return Icons.event; // Ngày tháng, lịch

      case "Cơ quan ban hành":
      case "Cơ quan cấp":
        return Icons.account_balance; // Cơ quan nhà nước, tổ chức cấp phép

      case "Tên cơ sở":
        return Icons.store; // Tên cửa hàng, doanh nghiệp

      case "Số ĐKKD":
        return Icons.assignment; // Số đăng ký kinh doanh, hồ sơ

      case "Loại hình cơ sở":
        return Icons.apartment; // Loại hình tổ chức/doanh nghiệp

      case "Địa chỉ":
        return Icons.location_on; // Vị trí, địa chỉ

      case "Người đại diện pháp luật":
      case "Người cấp":
      case "Người ký":
      case "Tên nhân viên":
        return Icons.person; // Biểu tượng cá nhân

      case "Số điện thoại":
        return Icons.phone; // Liên hệ qua điện thoại

      case "Email":
        return Icons.email; // Biểu tượng email

      case "Số CMND":
        return Icons.badge; // Biểu tượng căn cước công dân

      case "Số HĐ":
        return Icons.receipt_long; // Biểu tượng hợp đồng/hóa đơn

      case "Vị trí công việc":
        return Icons.work_outline; // Công việc, chức vụ

      case "Chức vụ":
        return Icons.supervisor_account; // Biểu tượng quản lý, lãnh đạo

      case "Loại văn bản":
        return Icons.description; // Văn bản, tài liệu

      case "Loại HĐ":
        return Icons.assignment_turned_in; // Hợp đồng đã hoàn tất

      case "Tên sản phẩm":
      case "Tên thiết bị":
        return Icons.production_quantity_limits; // Sản phẩm, thiết bị

      case "Tên nguyên liệu":
        return Icons.category; // Nguyên liệu sản xuất

      case "Nguồn gốc":
        return Icons.public; // Nguồn gốc xuất xứ

      case "Cách thức đóng gói":
        return Icons.inventory; // Đóng gói, kho hàng

      case "Số lượng":
        return Icons.format_list_numbered; // Biểu tượng số lượng

      case "Nước sản xuất":
        return Icons.flag; // Quốc gia sản xuất

      case "Tổng công suất":
        return Icons.electric_bolt; // Công suất thiết bị, sản xuất

      case "Năm bắt đầu sử dụng":
        return Icons.calendar_today; // Năm bắt đầu sử dụng

      default:
        return Icons.info_outline; // Biểu tượng mặc định
    }
  }
}
