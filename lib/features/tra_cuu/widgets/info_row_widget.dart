import 'package:flutter/material.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';

class InfoRowWidget extends StatelessWidget {
  final String label;
  final String value;
  final IconData? icon;

  const InfoRowWidget({
    super.key,
    required this.label,
    required this.value,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (icon != null)
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: Icon(
                icon,
                size: 20,
                color: AppColors.primary,
              ),
            ),
          SizedBox(
            width: 140, // Đặt chiều rộng cố định cho label
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(height: 1.5),
            ),
          ),
        ],
      ),
    );
  }
}
