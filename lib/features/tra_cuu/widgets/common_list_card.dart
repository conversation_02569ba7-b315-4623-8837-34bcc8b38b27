// // import 'package:cached_network_image/cached_network_image.dart';
// // import 'package:flutter/cupertino.dart';
// // import 'package:flutter/material.dart';
// // import 'package:gap/gap.dart';
// // import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
// // import 'package:attp_2024/core/utils/color_utils.dart';
// // import 'package:attp_2024/features/tra_cuu/widgets/label_value_row.dart';
// // import 'package:responsive_sizer/responsive_sizer.dart';
// // import 'package:shimmer/shimmer.dart';

// // class CommonListCard extends StatelessWidget {
// //   final String? title;
// //   final IconData? titleIcon;
// //   final String? logoUrl;
// //   final List<LabelValuePair> labelValueList;
// //   final String? badgeText;
// //   final String? badgeColor;
// //   final String? secondaryBadgeText;
// //   final String? secondaryBadgeColor;
// //   final VoidCallback? onTap;
// //   final double? height;
// //   final double imageWidth;
// //   final double imageHeight;
// //   final bool showImage;
// //   final BoxFit? fit;
// //   final String? textImage;
// //   final String? textImageColor;
// //   final String defaultImage;
// //   final bool isDocument;

// //   const CommonListCard(
// //       {super.key,
// //       required this.title,
// //       this.titleIcon,
// //       required this.logoUrl,
// //       required this.labelValueList,
// //       this.badgeText,
// //       this.badgeColor,
// //       this.secondaryBadgeText,
// //       this.secondaryBadgeColor,
// //       this.onTap,
// //       this.height,
// //       this.imageWidth = 25,
// //       this.imageHeight = 16.6,
// //       this.showImage = true,
// //       this.fit = BoxFit.contain,
// //       this.textImage,
// //       this.defaultImage = AppImageString.imageNotFount,
// //       this.isDocument = true,
// //       this.textImageColor});

// //   @override
// //   Widget build(BuildContext context) {
// //     return GestureDetector(
// //       onTap: onTap,
// //       child: Card(
// //         elevation: 2,
// //         shape: RoundedRectangleBorder(
// //           borderRadius: BorderRadius.circular(1.0),
// //         ),
// //         margin: EdgeInsets.symmetric(vertical: .5.h),
// //         child: SizedBox(
// //           height: height,
// //           child: Stack(
// //             children: [
// //               if (badgeText != null && badgeColor != null)
// //                 _buildBadge(
// //                   text: badgeText!,
// //                   color: badgeColor!,
// //                   alignment: Alignment.topRight,
// //                   borderRadius: const BorderRadius.only(
// //                     bottomLeft: Radius.circular(5.0),
// //                     topRight: Radius.circular(5.0),
// //                   ),
// //                 ),
// //               if (secondaryBadgeText != null && secondaryBadgeColor != null)
// //                 _buildBadge(
// //                   text: secondaryBadgeText!,
// //                   color: secondaryBadgeColor!,
// //                   alignment: Alignment.bottomLeft,
// //                   borderRadius: const BorderRadius.only(
// //                     bottomRight: Radius.circular(5.0),
// //                     topLeft: Radius.circular(5.0),
// //                   ),
// //                 ),
// //               Padding(
// //                 padding: EdgeInsets.only(
// //                   top:
// //                       badgeText != null || secondaryBadgeText != null ? 30 : 10,
// //                   bottom: 5,
// //                   left: 5,
// //                   right: 5,
// //                 ),
// //                 child: Row(
// //                   crossAxisAlignment: CrossAxisAlignment.center,
// //                   children: [
// //                     if (showImage) _buildImage(),
// //                     const Gap(10.0),
// //                     _buildContent(),
// //                   ],
// //                 ),
// //               ),
// //             ],
// //           ),
// //         ),
// //       ),
// //     );
// //   }

// //   Widget _buildImage() {
// //     return Stack(
// //       children: [
// //         ClipRRect(
// //           borderRadius: BorderRadius.circular(1.0),
// //           child: isDocument
// //               ? Image.asset(
// //                   defaultImage,
// //                   width: imageWidth.w,
// //                   height: imageHeight.h,
// //                   fit: fit ?? BoxFit.fill,
// //                 )
// //               : ImageWithFallback(
// //                   width: imageWidth.w,
// //                   height: imageHeight.h,
// //                   imageUrl: logoUrl,
// //                   fit: fit ?? BoxFit.fill,
// //                   fallbackImage: defaultImage,
// //                 ),
// //         ),
// //         if (textImage != null && textImage!.isNotEmpty)
// //           Positioned(
// //             bottom: 0,
// //             left: 0,
// //             right: 0,
// //             child: Align(
// //               alignment: Alignment.center,
// //               child: Container(
// //                   width: 19.w,
// //                   height: 2.4.h,
// //                   // padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 0.7.h),
// //                   decoration: BoxDecoration(
// //                     color: hexToColor(textImageColor ?? "#000000")
// //                         .withOpacity(0.8),
// //                     borderRadius: const BorderRadius.only(
// //                       topLeft: Radius.circular(0.0),
// //                       bottomRight: Radius.circular(0.0),
// //                     ),
// //                   ),
// //                   child: Align(
// //                     alignment: Alignment.center,
// //                     child: Text(
// //                       textImage!,
// //                       style: TextStyle(
// //                         color: Colors.white,
// //                         fontSize: 12.sp,
// //                         fontWeight: FontWeight.w600,
// //                       ),
// //                     ),
// //                   )),
// //             ),
// //           ),
// //       ],
// //     );
// //   }

// //   Widget _buildContent() {
// //     return Expanded(
// //       child: Column(
// //         crossAxisAlignment: CrossAxisAlignment.start,
// //         children: [
// //           _buildTitleWithIcon(),
// //           Divider(height: 1.5.h, thickness: 0.4),
// //           ...labelValueList.map(
// //             (lv) => LabelValueRow(
// //               label: lv.label,
// //               value: lv.value,
// //               labelFontWeight: FontWeight.bold,
// //             ),
// //           ),
// //         ],
// //       ),
// //     );
// //   }

// //   Widget _buildTitleWithIcon() {
// //     return Row(
// //       children: [
// //         if (titleIcon != null)
// //           Icon(
// //             titleIcon,
// //             size: 15.sp,
// //             color: CupertinoColors.systemGrey,
// //           ),
// //         if (titleIcon != null) const Gap(5.0),
// //         Expanded(
// //           child: Text(
// //             title ?? '',
// //             style: TextStyle(
// //               fontWeight: FontWeight.w600,
// //               fontSize: 15.sp,
// //               color: CupertinoColors.black,
// //             ),
// //           ),
// //         ),
// //       ],
// //     );
// //   }

// //   Widget _buildBadge({
// //     required String text,
// //     required String color,
// //     required Alignment alignment,
// //     BorderRadius? borderRadius,
// //   }) {
// //     return Align(
// //       alignment: alignment,
// //       child: Container(
// //         padding: EdgeInsets.symmetric(horizontal: 1.w, vertical: 1.h),
// //         decoration: BoxDecoration(
// //           color: hexToColor(color),
// //           borderRadius: borderRadius ?? BorderRadius.circular(5.0),
// //         ),
// //         child: Text(
// //           text,
// //           style: TextStyle(
// //             color: Colors.white,
// //             fontSize: 13.sp,
// //             fontWeight: FontWeight.bold,
// //           ),
// //         ),
// //       ),
// //     );
// //   }
// // }

// // class LabelValuePair {
// //   final String label;
// //   final String value;

// //   LabelValuePair({required this.label, required this.value});
// // }

// // class ImageWithFallback extends StatelessWidget {
// //   final String? imageUrl; // URL từ mạng
// //   final String fallbackImage; // Đường dẫn ảnh từ local
// //   final double width;
// //   final double height;
// //   final BoxFit fit;

// //   const ImageWithFallback({
// //     super.key,
// //     required this.imageUrl,
// //     required this.fallbackImage,
// //     required this.width,
// //     required this.height,
// //     this.fit = BoxFit.cover,
// //   });

// //   @override
// //   Widget build(BuildContext context) {
// //     return CachedNetworkImage(
// //       width: width,
// //       height: height,
// //       imageUrl: imageUrl ?? '',
// //       // URL mạng
// //       fit: fit,
// //       placeholder: (context, url) => Shimmer.fromColors(
// //         baseColor: Colors.grey[300]!,
// //         highlightColor: Colors.grey[100]!,
// //         child: Container(
// //           height: height,
// //           width: width,
// //           color: Colors.grey[300],
// //         ),
// //       ),
// //       errorWidget: (context, url, error) => Container(
// //         height: height,
// //         width: width,
// //         decoration: BoxDecoration(
// //           image: DecorationImage(
// //             image: AssetImage(fallbackImage), // Sử dụng ảnh local nếu lỗi
// //             fit: fit,
// //           ),
// //         ),
// //       ),
// //     );
// //   }
// // }
// import 'package:attp_2024/core/configs/theme/app_colors.dart';
// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:flutter/material.dart';
// import 'package:gap/gap.dart';
// import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
// import 'package:attp_2024/features/tra_cuu/widgets/label_value_row.dart';
// import 'package:responsive_sizer/responsive_sizer.dart';
// import 'package:shimmer/shimmer.dart';

// class CommonListCard extends StatelessWidget {
//   final LabelValuePair title;
//   final String? logoUrl;
//   final List<LabelValuePair> labelValueList;
//   final String statusText;
//   final Color statusColor;
//   final String? badgeText;
//   final String? badgeColor;
//   final VoidCallback? onTap;
//   final bool showImage;
//   final String defaultImage;

//   const CommonListCard({
//     super.key,
//     required this.title,
//     required this.statusText,
//     required this.statusColor,
//     required this.labelValueList,
//     required this.logoUrl,
//     this.badgeText,
//     this.badgeColor,
//     this.onTap,
//     this.showImage = true,
//     this.defaultImage = AppImageString.imageNotFount,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return GestureDetector(
//       onTap: onTap,
//       child: Card(
//         elevation: 2,
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(.0),
//         ),
//         margin: EdgeInsets.symmetric(vertical: .5.h, horizontal: 0),
//         child: Stack(
//           children: [
//             Padding(
//               padding: EdgeInsets.only(left: 1.5.w, right: 1.5.w, bottom: 1.h),
//               child: Row(
//                 crossAxisAlignment: CrossAxisAlignment.center,
//                 children: [
//                   if (showImage) _buildImage(),
//                   Gap(3.w),
//                   Expanded(
//                     child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         Gap(2.6.h),
//                         _buildLabelRow(title, isTitle: true),
//                         Divider(
//                             height: 1.5.h,
//                             thickness: 1,
//                             color: AppColors.primary),
//                         ...labelValueList.map(
//                           (lv) => LabelValueRow(
//                             label: lv.label,
//                             value: lv.value,
//                             labelFontWeight: FontWeight.normal,
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//             Positioned(
//               top: 0,
//               right: 0,
//               child: _buildStatusBadge(),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget _buildImage() {
//     return ClipRRect(
//       borderRadius: BorderRadius.circular(0),
//       child: CachedNetworkImage(
//         width: 21.w,
//         height: 13.h,
//         imageUrl: logoUrl ?? '',
//         fit: BoxFit.fill,
//         placeholder: (context, url) => Shimmer.fromColors(
//           baseColor: Colors.grey[300]!,
//           highlightColor: Colors.grey[100]!,
//           child: Container(
//             height: 10.h,
//             width: 20.w,
//             color: Colors.grey[300],
//           ),
//         ),
//         errorWidget: (context, url, error) => Image.asset(
//           defaultImage,
//           width: 20.w,
//           height: 13.h,
//           fit: BoxFit.fill,
//         ),
//       ),
//     );
//   }

//    Widget _buildBadgeBelowImage() {
//     return Container(
//       margin: EdgeInsets.only(top: 0.5.h),
//       padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: .5.h),
//       decoration: BoxDecoration(
//         color: badgeColor != null ? Color(int.parse("0xFF${badgeColor!.replaceFirst("#", "")}")) : Colors.blue,
//         borderRadius: BorderRadius.circular(5),
//       ),
//       child: Text(
//         badgeText!,
//         style: TextStyle(
//           color: Colors.white,
//           fontSize: 12.sp,
//           fontWeight: FontWeight.bold,
//         ),
//       ),
//     );
//   }

//   Widget _buildStatusBadge() {
//     return Container(
//       padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: .5.h),
//       decoration: BoxDecoration(
//         color: statusColor,
//         borderRadius: BorderRadius.circular(0),
//       ),
//       child: Text(
//         statusText,
//         style: TextStyle(
//           color: Colors.white,
//           fontSize: 13.sp,
//           fontWeight: FontWeight.bold,
//         ),
//       ),
//     );
//   }

//   Widget _buildLabelRow(LabelValuePair labelValue, {bool isTitle = false}) {
//     return Padding(
//       padding: EdgeInsets.symmetric(vertical: .3.h),
//       child: Row(
//         children: [
//           Icon(_getIcon(labelValue.label), size: 14.sp, color: Colors.black54),
//           Gap(1.w),
//           Text(
//             "${labelValue.label}: ",
//             style: TextStyle(
//               fontWeight: FontWeight.w500,
//               fontSize: isTitle ? 14.sp : 14.sp,
//             ),
//           ),
//           Expanded(
//             child: Text(
//               labelValue.value,
//               style: TextStyle(fontSize: 13.5.sp),
//               textScaler: TextScaler.noScaling,
//               maxLines: 1,
//               overflow: TextOverflow.ellipsis,
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   IconData _getIcon(String label) {
//     switch (label) {
//       case "Số GCN":
//       case "Số GCK":
//         return Icons.tag;
//       case "Ngày ký":
//       case "Ngày hết hạn":
//         return Icons.date_range;
//       case "Cơ quan ban hành":
//         return Icons.apartment;
//       case "Tên cơ sở":
//         return Icons.business;
//       case "Số ĐKKD":
//         return Icons.confirmation_number;
//       case "Loại hình cơ sở":
//         return Icons.category;
//       case "Địa chỉ":
//         return Icons.location_on;
//       default:
//         return Icons.info_outline;
//     }
//   }
// }

// /// Model dữ liệu cho từng dòng thông tin
// class LabelValuePair {
//   final String label;
//   final String value;

//   LabelValuePair({required this.label, required this.value});
// }

import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/features/tra_cuu/widgets/label_value_row.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:shimmer/shimmer.dart';

class CommonListCard extends StatelessWidget {
  final LabelValuePair title;
  final String? logoUrl;
  final List<LabelValuePair> labelValueList;
  final String statusText;
  final Color statusColor;
  final String? badgeText;
  final String? badgeColor;

  final VoidCallback? onTap;
  final bool showImage;
  final String defaultImage;

  const CommonListCard({
    super.key,
    required this.title,
    required this.statusText,
    required this.statusColor,
    required this.labelValueList,
    required this.logoUrl,
    this.badgeText,
    this.badgeColor,
    this.onTap,
    this.showImage = true,
    this.defaultImage = AppImageString.iconNoData,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        margin: EdgeInsets.symmetric(vertical: .5.h, horizontal: 0),
        child: Stack(
          children: [
            Padding(
              padding: EdgeInsets.only(left: 1.5.w, right: 1.5.w, bottom: 1.h),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  if (showImage) _buildImageWithBadge(), // Gọi hàm mới có badge
                  Gap(3.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Gap(1.w),
                        // _buildLabelRow(title, isTitle: true),
                        ConstrainedBox(
                          constraints: BoxConstraints(
                              maxWidth: 40.w), // Giới hạn độ rộng tối đa
                          child: Text(
                            title.value,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14.sp,
                              color: Colors.black54,
                            ),
                          ),
                        ),
                        Divider(
                            height: 1.5.h,
                            thickness: 1,
                            color: AppColors.primary),
                        ...labelValueList.map(
                          (lv) => LabelValueRow(
                            label: lv.label,
                            value: lv.value,
                            labelFontWeight: FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Positioned(
              top: 0,
              right: 0,
              child: _buildStatusBadge(),
            ),
          ],
        ),
      ),
    );
  }

  /// Hiển thị ảnh với badge bên dưới
  Widget _buildImageWithBadge() {
    return Column(
      children: [
        _buildImage(),
        if (badgeText != null) _buildBadgeBelowImage(),
      ],
    );
  }

  /// Hiển thị ảnh với `CachedNetworkImage`
  Widget _buildImage() {
    return Padding(
      padding: EdgeInsets.only(top: .8.h),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(5),
        child: CachedNetworkImage(
          width: 26.w,
          height: 12.h,
          imageUrl: logoUrl ?? '',
          fit: BoxFit.fill,
          placeholder: (context, url) => Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              height: 10.h,
              width: 20.w,
              color: Colors.grey[300],
            ),
          ),
          errorWidget: (context, url, error) => Image.asset(
            defaultImage,
            width: 13.w,
            height: 10.h,
            fit: BoxFit.fill,
          ),
        ),
      ),
    );
  }

  /// Hiển thị badge **bên dưới hình ảnh**
  Widget _buildBadgeBelowImage() {
    return Container(
      margin: EdgeInsets.only(top: 0.5.h),
      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: .5.h),
      decoration: BoxDecoration(
        color: badgeColor != null
            ? Color(int.parse("0xFF${badgeColor!.replaceFirst("#", "")}"))
            : Colors.blue,
        borderRadius: BorderRadius.circular(2),
      ),
      child: Text(
        badgeText!,
        style: TextStyle(
          color: Colors.white,
          fontSize: 12.sp,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// Hiển thị badge chính (trạng thái)
  Widget _buildStatusBadge() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: .5.h),
      decoration: BoxDecoration(
        color: statusColor,
        borderRadius: const BorderRadius.only(
          topRight: Radius.circular(5),
          bottomLeft: Radius.circular(5),
        ),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: Colors.white,
          fontSize: 13.sp,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// Hiển thị các thông tin của card
  Widget _buildLabelRow(LabelValuePair labelValue, {bool isTitle = false}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: .3.h),
      child: Row(
        children: [
          Icon(_getIcon(labelValue.label),
              size: 14.5.sp, color: Colors.black54),
          Gap(1.w),
          Text(
            "${labelValue.label}: ",
            style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: isTitle ? 14.5.sp : 14.5.sp,
                color: Colors.black54),
          ),
          Expanded(
            child: Text(
              labelValue.value,
              style: TextStyle(fontSize: 14.sp, color: Colors.black54),
              textScaler: TextScaler.noScaling,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// Xác định icon phù hợp với từng loại dữ liệu
  IconData _getIcon(String label) {
    switch (label) {
      case "Số GCN":
      case "Số GCK":
        return Icons.tag;
      case "Ngày ký":
      case "Ngày hết hạn":
        return Icons.date_range;
      case "Cơ quan ban hành":
        return Icons.apartment;
      case "Tên cơ sở":
        return Icons.business;
      case "Số ĐKKD":
        return Icons.confirmation_number;
      case "Loại hình cơ sở":
        return Icons.category;
      case "Địa chỉ":
        return Icons.location_on;
      default:
        return Icons.info_outline;
    }
  }
}

/// Model dữ liệu cho từng dòng thông tin
class LabelValuePair {
  final String label;
  final String value;

  LabelValuePair({required this.label, required this.value});
}
