import 'package:flutter/cupertino.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

// Widget _buildInfoRow(String label, String value,
//     {IconData? icon, CupertinoThemeData? themeData}) {
//   final textTheme = themeData?.textTheme ?? CupertinoThemeData().textTheme;

//   return Padding(
//     padding: const EdgeInsets.symmetric(vertical: 4.0),
//     child: Row(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         if (icon != null)
//           Padding(
//             padding: const EdgeInsets.only(right: 8.0),
//             child: Icon(
//               icon,
//               size: 20,
//               color: CupertinoColors.activeBlue, // Màu nhấn theo iOS
//             ),
//           ),
//         SizedBox(
//           width: 140, // Chiều rộng cố định cho nhãn
//           child: Text(
//             label,
//             style: textTheme.textStyle.copyWith(
//               fontWeight: FontWeight.bold,
//               fontSize: 16, // <PERSON><PERSON>ch thước chữ iOS
//               color: CupertinoColors.label, // Màu nhãn chính
//             ),
//           ),
//         ),
//         Expanded(
//           child: Text(
//             value.isNotEmpty
//                 ? value
//                 : "Chưa cập nhật", // Xử lý trường hợp giá trị rỗng
//             style: textTheme.textStyle.copyWith(
//               fontSize: 15.sp, // Văn bản nội dung
//               height: 1.5, // Giãn dòng
//               color: CupertinoColors.secondaryLabel, // Màu phụ theo iOS
//             ),
//           ),
//         ),
//       ],
//     ),
//   );
// }


class InfoRowWidget extends StatelessWidget {
  final String label;
  final String value;
  final IconData? icon;
  final Color? colorTitle;
  final Color? colorSubtitle;

  const InfoRowWidget({
    Key? key,
    required this.label,
    required this.value,
    this.icon,
    this.colorTitle,
    this.colorSubtitle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CupertinoListTile(
      padding: const EdgeInsets.all(10),
      leading: Icon(
        icon,
        color: CupertinoColors.systemGrey,
        size: 18.sp,
      ),
      leadingToTitle: 8.w,
      leadingSize: 10.sp,
      title: Text(
        label,
        style: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 16.sp,
          color: colorTitle ?? CupertinoColors.label,
        ),
      ),
      subtitle: Text(
        value.isNotEmpty ? value : "Đang cập nhật",
        style: TextStyle(
          fontSize: 16.sp,
          color: colorSubtitle ?? CupertinoColors.secondaryLabel,
        ),
        maxLines: 5,
      ),
    );
  }
}
