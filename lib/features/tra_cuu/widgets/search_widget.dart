import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class CustomSearchBar extends StatefulWidget {
  final TextEditingController controller;
  final Function(String) onChanged;
  final VoidCallback onFilterPressed;
  final String hintText;
  final int resultCount;

  const CustomSearchBar({
    super.key,
    required this.controller,
    required this.onChanged,
    required this.onFilterPressed,
    this.hintText = "Tìm kiếm...",
    this.resultCount = 0,
  });

  @override
  _CustomSearchBarState createState() => _CustomSearchBarState();
}

class _CustomSearchBarState extends State<CustomSearchBar> {
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  /// Giữ vị trí con trỏ sau khi cập nhật nội dung
  void _preserveCursorPosition(String newText) {
    final cursorPosition = widget.controller.selection.baseOffset;
    widget.controller.value = TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(
        offset: cursorPosition > newText.length ? newText.length : cursorPosition,
      ),
    );
  }

  /// Xử lý thay đổi nội dung mà không làm mất focus hoặc vị trí con trỏ
  void _handleTextChange(String value) {
    widget.onChanged(value);
    Future.delayed(Duration.zero, () {
      if (mounted) {
        _preserveCursorPosition(value);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 1.h),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 5.h,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(5),
                  ),
                  child: TextField(
                    controller: widget.controller,
                    focusNode: _focusNode,
                    style: TextStyle(fontSize: 16.sp),
                    textAlignVertical: TextAlignVertical.center,
                    decoration: InputDecoration(
                      hintText: widget.hintText,
                      hintStyle: TextStyle(fontSize: 16.sp, color: Colors.grey[500]),
                      prefixIcon: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 0),
                        child: Icon(Icons.search, size: 18.sp, color: Colors.grey[600]),
                      ),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 11),
                      suffixIcon: widget.controller.text.isNotEmpty
                          ? IconButton(
                              icon: Icon(Icons.clear, size: 16.sp, color: Colors.grey[600]),
                              onPressed: () {
                                setState(() {
                                  widget.controller.clear();
                                  _handleTextChange("");
                                });
                              },
                            )
                          : null,
                    ),
                    onChanged: _handleTextChange,
                  ),
                ),
              ),
              Gap(1.w),
              Container(
                height: 38.5,
                width: 38.5,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(5),
                ),
                child: IconButton(
                  icon: Icon(Icons.tune, size: 18.sp, color: Colors.grey[700]),
                  padding: const EdgeInsets.all(4),
                  constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                  onPressed: widget.onFilterPressed,
                ),
              ),
            ],
          ),
          Gap(1.h),
          Align(
            alignment: Alignment.centerLeft,
            child: Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text: 'Kết quả: ',
                    style: TextStyle(
                      fontSize: 15.sp,
                      color: Colors.grey[600],
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                  TextSpan(
                    text: '${widget.resultCount}',
                    style: TextStyle(
                      fontSize: 15.sp,
                      color: Colors.blue,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}