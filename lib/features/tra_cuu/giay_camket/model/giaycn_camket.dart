class GiayChungNhanCamKet {
  final String? soGCN;
  final String? ngayCap;
  final String? ngayHetHan;
  final String? nguoiCap;
  final String? chucVu;
  final String? coQuan;
  final String? dinhKem;
  final String? soHoSo;
  final String? ngayNop;
  final String? chucVuNguoiXuLy;
  final String? nguoiXuLy;
  final String? tenCoSoSXKD;
  final String? soGPKD;
  final String? ngayCapGPKD;
  final String? coQuanCapGPKD;
  final String? soDienThoai;
  final String? emailCS;
  final String? tenLoaiHinhCoSo;
  final String? trangThaiBH;
  final String? colorTrangThaiBH;
  final String? diaChiCs;
  final String? hoVaTenDaiDien;
  final String? chucVuDaiDien;
  final String? diDongDaiDien;
  final String? soCMNDDaiDien;
  final String? capGCNID;
  final String? trangThaiBanHanh;
  final String? coSoSXKDID;
  final String? mauGCNID;
  final String? thuocLoai;
  final int? totalResults;

  GiayChungNhanCamKet({
    this.soGCN,
    this.ngayCap,
    this.ngayHetHan,
    this.nguoiCap,
    this.chucVu,
    this.coQuan,
    this.dinhKem,
    this.soHoSo,
    this.ngayNop,
    this.chucVuNguoiXuLy,
    this.nguoiXuLy,
    this.tenCoSoSXKD,
    this.soGPKD,
    this.ngayCapGPKD,
    this.coQuanCapGPKD,
    this.soDienThoai,
    this.emailCS,
    this.tenLoaiHinhCoSo,
    this.trangThaiBH,
    this.colorTrangThaiBH,
    this.diaChiCs,
    this.hoVaTenDaiDien,
    this.chucVuDaiDien,
    this.diDongDaiDien,
    this.soCMNDDaiDien,
    this.capGCNID,
    this.trangThaiBanHanh,
    this.coSoSXKDID,
    this.mauGCNID,
    this.thuocLoai,
    this.totalResults,
  });

  factory GiayChungNhanCamKet.fromJson(Map<String, dynamic> json) {
    return GiayChungNhanCamKet(
      soGCN: json['SoGCN'] as String?,
      ngayCap: json['NgayCap'] as String?,
      ngayHetHan: json['NgayHetHan'] as String?,
      nguoiCap: json['NguoiCap'] as String?,
      chucVu: json['ChucVu'] as String?,
      coQuan: json['CoQuan'] as String?,
      dinhKem: json['DinhKem'] as String?,
      soHoSo: json['SoHoSo'] as String?,
      ngayNop: json['NgayNop'] as String?,
      chucVuNguoiXuLy: json['ChucVu_NguoiXuLy'] as String?,
      nguoiXuLy: json['NguoiXuLy'] as String?,
      tenCoSoSXKD: json['TenCoSoSXKD'] as String?,
      soGPKD: json['SoGPKD'] as String?,
      ngayCapGPKD: json['NgayCapGPKD'] as String?,
      coQuanCapGPKD: json['CoQuanCapGPKD'] as String?,
      soDienThoai: json['SoDienThoai'] as String?,
      emailCS: json['EmailCS'] as String?,
      tenLoaiHinhCoSo: json['TenLoaiHinhCoSo'] as String?,
      trangThaiBH: json['TrangThaiBH'] as String?,
      colorTrangThaiBH: json['Color_TrangThaiBH'] as String?,
      diaChiCs: json['DiaChiCS'] as String?,
      hoVaTenDaiDien: json['HoVaTen_DaiDien'] as String?,
      chucVuDaiDien: json['ChucVu_DaiDien'] as String?,
      diDongDaiDien: json['DiDong_DaiDien'] as String?,
      soCMNDDaiDien: json['SoCMND_DaiDien'] as String?,
      capGCNID: json['CapGCNID'] as String?,
      trangThaiBanHanh: json['TrangThai_BanHanh'] as String?,
      coSoSXKDID: json['CoSoSXKDID'] as String?,
      mauGCNID: json['MauGCNID'] as String?,
      thuocLoai: json['ThuocLoai'] as String?,
      totalResults: json['totalResults'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'SoGCN': soGCN,
      'NgayCap': ngayCap,
      'NgayHetHan': ngayHetHan,
      'NguoiCap': nguoiCap,
      'ChucVu': chucVu,
      'CoQuan': coQuan,
      'DinhKem': dinhKem,
      'SoHoSo': soHoSo,
      'NgayNop': ngayNop,
      'ChucVu_NguoiXuLy': chucVuNguoiXuLy,
      'NguoiXuLy': nguoiXuLy,
      'TenCoSoSXKD': tenCoSoSXKD,
      'SoGPKD': soGPKD,
      'NgayCapGPKD': ngayCapGPKD,
      'CoQuanCapGPKD': coQuanCapGPKD,
      'SoDienThoai': soDienThoai,
      'EmailCS': emailCS,
      'TenLoaiHinhCoSo': tenLoaiHinhCoSo,
      'TrangThaiBH': trangThaiBH,
      'Color_TrangThaiBH': colorTrangThaiBH,
      'DiaChiCS': diaChiCs,
      'HoVaTen_DaiDien': hoVaTenDaiDien,
      'ChucVu_DaiDien': chucVuDaiDien,
      'DiDong_DaiDien': diDongDaiDien,
      'SoCMND_DaiDien': soCMNDDaiDien,
      'CapGCNID': capGCNID,
      'TrangThai_BanHanh': trangThaiBanHanh,
      'CoSoSXKDID': coSoSXKDID,
      'MauGCNID': mauGCNID,
      'ThuocLoai': thuocLoai,
      'totalResults': totalResults,
    };
  }
}
