import 'package:get/get.dart';
import 'package:attp_2024/features/tra_cuu/giay_camket/presentation/controller/giay_camket_detail_controller.dart';

import '../../../../core/data/api/configs/dio_configs.dart';
import '../../../../core/data/api/services/proc/proc_service.dart';


class GiayCamKetDetailBinding extends Bindings {
  @override
  void dependencies() {
    Get.put(GiayCamKetDetailController());
    Get.lazyPut(() => ProcService(Get.find<DioService>()));
  }

}
