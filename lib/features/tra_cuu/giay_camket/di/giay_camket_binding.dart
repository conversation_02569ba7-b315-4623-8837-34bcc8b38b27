import 'package:get/get.dart';

import '../../../../core/data/api/configs/dio_configs.dart';
import '../../../../core/data/api/services/proc/proc_service.dart';
import '../presentation/controller/giay_camket_controller.dart';


class GiayCamKetBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<GiayCamKetPageController>(
      () => GiayCamKetPageController(),
    );
    Get.lazyPut(() => ProcService(Get.find<DioService>()));
  }
}
