import 'package:attp_2024/core/utils/color_utils.dart';
import 'package:attp_2024/core/utils/convert_text.dart';
import 'package:attp_2024/features/tra_cuu/giay_camket/presentation/widgets/search.dart';
import 'package:attp_2024/features/tra_cuu/widgets/custom_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/routes/routes.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:attp_2024/features/tra_cuu/giay_camket/model/giaycn_camket.dart';
import 'package:attp_2024/features/tra_cuu/giay_camket/presentation/controller/giay_camket_controller.dart';
import '../../../../../core/configs/contents/app_content.dart';

class GiayCamKetPage extends GetView<GiayCamKetPageController> {
  const GiayCamKetPage({super.key});
  Widget buildShimmerEffect() {
    return ListView.builder(
      padding: const EdgeInsets.all(8.0),
      itemCount: 5,
      itemBuilder: (context, index) {
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(5.0),
          ),
          margin: const EdgeInsets.symmetric(vertical: 8.0),
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildShimmerBox(width: 80, height: 80),
                const Gap(10.0),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildShimmerBox(width: double.infinity, height: 16.0),
                      const Gap(8.0),
                      _buildShimmerBox(width: 150.0, height: 16.0),
                      const Gap(8.0),
                      _buildShimmerBox(width: double.infinity, height: 16.0),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildShimmerBox({required double width, required double height}) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.0),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final ScrollController scrollController = ScrollController();
    final ValueNotifier<bool> isVisible = ValueNotifier(true);

    scrollController.addListener(() {
      if (scrollController.position.userScrollDirection ==
          ScrollDirection.reverse) {
        isVisible.value = false;
      } else if (scrollController.position.userScrollDirection ==
          ScrollDirection.forward) {
        isVisible.value = true;
      }

      // Thêm xử lý để tải thêm dữ liệu khi cuộn đến cuối danh sách
      if (scrollController.position.pixels >=
              scrollController.position.maxScrollExtent - 100 &&
          !controller.isLoadingMore.value &&
          controller.hasMoreData.value) {
        controller.loadMore();
      }
    });

    return Scaffold(
      appBar: const AppBarWidget(
        title: "Tra cứu giấy cam kết",
        centerTitle: true,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SearchWidget(),
          Expanded(
            child: RefreshIndicator(
              onRefresh: () async {
                controller.fetchSearchResultsWithFilters();
              },
              child: Obx(() {
                if (controller.isLoading.value) {
                  return const ShimmerLoading();
                }
                if (controller.filteredResults.isEmpty) {
                  return Center(
                    child: Image.asset(
                      AppImageString.iDataNotFound,
                      width: 100,
                      height: 100,
                    ),
                  );
                }
                return Column(
                  children: [
                    Expanded(
                      child: ListView.builder(
                        controller: scrollController,
                        padding: const EdgeInsets.all(8.0),
                        itemCount: controller.filteredResults.length,
                        itemBuilder: (context, index) {
                          final item = controller.filteredResults[index];
                          return _buildListItem(item);
                        },
                      ),
                    ),
                    // Hiển thị indicator tải thêm
                    Obx(() => controller.isLoadingMore.value
                        ? const Padding(
                            padding: EdgeInsets.symmetric(vertical: 8.0),
                            child: Center(
                              child: CircularProgressIndicator(),
                            ),
                          )
                        : const SizedBox.shrink()),
                    // Hiển thị nút tải thêm nếu cần
                  ],
                );
              }),
            ),
          ),
        ],
      ),
    );
  }
}

Widget _buildListItem(GiayChungNhanCamKet item) {
  return CustomCard(
    title: LabelValuePair(
        label: "Số GCK", value: item.soGCN ?? AppContent.textDefault),
    statusText: item.trangThaiBH ?? AppContent.textDefault,
    statusColor: hexToColor(item.colorTrangThaiBH),
    labelValueList: [
      LabelValuePair(
          label: "Ngày ký", value: item.ngayCap ?? AppContent.textDefault),
      LabelValuePair(
          label: "Ngày hết hạn",
          value: item.ngayHetHan ?? AppContent.textDefault),
      LabelValuePair(
          label: "Tên cơ sở",
          value: getText(item.tenCoSoSXKD.toString(), before: false) ??
              AppContent.textDefault),
      LabelValuePair(
          label: "Số ĐKKD", value: item.soGPKD ?? AppContent.textDefault),
      LabelValuePair(
          label: "Loại hình cơ sở",
          value: item.tenLoaiHinhCoSo ?? AppContent.textDefault),
      LabelValuePair(
          label: "Địa chỉ", value: item.diaChiCs ?? AppContent.textDefault),
    ],
    onTap: () {
      Get.toNamed(Routes.giayCamKetDetail, arguments: item);
    },
  );
}

class ShimmerLoading extends StatelessWidget {
  const ShimmerLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.all(8.0),
      itemCount: 5,
      itemBuilder: (context, index) {
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(5.0),
          ),
          margin: const EdgeInsets.symmetric(vertical: 8.0),
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildShimmerBox(width: 80, height: 80),
                const Gap(10.0),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildShimmerBox(width: double.infinity, height: 16.0),
                      const Gap(8.0),
                      _buildShimmerBox(width: 150.0, height: 16.0),
                      const Gap(8.0),
                      _buildShimmerBox(width: double.infinity, height: 16.0),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildShimmerBox({required double width, required double height}) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.0),
        ),
      ),
    );
  }
}
