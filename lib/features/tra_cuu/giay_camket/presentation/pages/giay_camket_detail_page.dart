import 'package:attp_2024/core/configs/contents/app_content.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/ui/widgets/webview/webview_page.dart';
import 'package:attp_2024/features/tra_cuu/giay_camket/model/giaycn_camket.dart';
import 'package:attp_2024/features/tra_cuu/giay_camket/presentation/controller/giay_camket_detail_controller.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:fluttertoast/fluttertoast.dart';

class GiayCamKetDetailPage extends GetView<GiayCamKetDetailController> {
  GiayCamKetDetailPage({super.key});

  final item = Get.arguments as GiayChungNhanCamKet?;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppBarWidget(
        title: "Thông tin giấy cam kết",
        centerTitle: true,
      ),
      body: item != null ? _buildBody(item!) : _buildNoData(),
    );
  }

  // Build body with data
  Widget _buildBody(GiayChungNhanCamKet item) {
    const defaultText = "Đang cập nhật";
    String getText(String input, {String separator = '-', bool before = true}) {
      if (input.isEmpty || !input.contains(separator)) return input.trim();

      List<String> parts = input.split(separator);
      return before ? parts.first.trim() : parts.last.trim();
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Thông tin cơ sở sản xuất kinh doanh",
            style: TextStyle(
                fontSize: AppDimens.mediumText,
                fontWeight: FontWeight.bold,
                color: AppColors.primary),
          ),
          Gap(1.h),
          _buildInfoRow(
              "Mã cơ sở:",
              getText(
                    item.tenCoSoSXKD.toString(),
                  ) ??
                  defaultText,
              icon: Icons.tag),
          _buildInfoRow(
              "Tên cơ sở sản xuất kinh doanh:",
              getText(item.tenCoSoSXKD.toString(), before: false) ??
                  defaultText,
              icon: Icons.business),
          _buildInfoRow("Chủ cơ sở:", item.hoVaTenDaiDien ?? defaultText,
              icon: Icons.person),
          _buildInfoRow("Trạng thái:", item.trangThaiBH ?? defaultText,
              icon: Icons.stacked_bar_chart),
          _buildInfoRow("Số đăng ký kinh doanh:", item.soGPKD ?? defaultText,
              icon: Icons.tag),
          _buildInfoRow("Ngày cấp GPKD:", item.ngayCapGPKD ?? defaultText,
              icon: Icons.calendar_today),
          _buildInfoRow("Cơ quan cấp GPKD:", item.coQuanCapGPKD ?? defaultText,
              icon: Icons.apartment),
          _buildInfoRow("Số điện thoại:", item.soDienThoai ?? defaultText,
              icon: Icons.phone),
          _buildInfoRow("Email:", item.emailCS ?? defaultText,
              icon: Icons.email),
          _buildInfoRow("Loại hình cơ sở:", item.tenLoaiHinhCoSo ?? defaultText,
              icon: Icons.stacked_bar_chart),
          const Divider(),
          Text(
            "Thông tin giấy cam kết ATTP",
            style: TextStyle(
                fontSize: AppDimens.mediumText,
                fontWeight: FontWeight.bold,
                color: AppColors.primary),
          ),
          Gap(1.h),
          _buildInfoRow("Số giấy cam kết:", item.soGCN ?? defaultText,
              icon: Icons.tag),
          _buildInfoRow("Ngày cấp:", item.ngayCap ?? defaultText,
              icon: Icons.calendar_today),
          _buildInfoRow("Ngày hết hạn:", item.ngayHetHan ?? defaultText,
              icon: Icons.calendar_today),
          _buildInfoRow("Người ký:", item.nguoiCap ?? defaultText,
              icon: Icons.person),
          _buildInfoRow("Chức vụ:", item.chucVu ?? defaultText,
              icon: Icons.badge),
          _buildInfoRow("Cơ quan cấp:", item.coQuan ?? defaultText,
              icon: Icons.apartment),
          _buildInfoRow(
              "Hình thức:",
              '${item.trangThaiBH} của giấy chứng nhận số ${item.soGCN} ngày ${item.ngayCap}' ??
                  defaultText,
              icon: Icons.apartment),
          TextButton.icon(
            onPressed: () {
              print(item.dinhKem);
              String baseUrl = controller.userAccessModel?.siteURL ?? "";
              String path = item.dinhKem ?? "";

              if (item.dinhKem == null || item.dinhKem == "") {
                Fluttertoast.showToast(
                  msg: "Không có file đính kèm!",
                  toastLength: Toast.LENGTH_SHORT,
                  gravity: ToastGravity.BOTTOM,
                  backgroundColor: Colors.yellow,
                  textColor: Colors.white,
                  fontSize: 14.0,
                );
                return;
              }

              String finalUrl = "$baseUrl$path";
              if (finalUrl.endsWith('*')) {
                finalUrl = finalUrl.substring(0, finalUrl.length - 1);
              }

              Get.to(() => WebViewPage(
                    title: AppContent.appTitleWebview,
                    initialUrl: finalUrl,
                  ));
            },
            label: const Text(
              "Đính kèm",
            ),
            icon: Icon(
              Icons.attach_file_rounded,
              size: 18.sp,
              color: AppColors.primary,
            ),
            style: TextButton.styleFrom(
              padding: EdgeInsets.zero,
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ),
        ],
      ),
    );
  }

  // Build message for no data
  Widget _buildNoData() {
    return const Center(
      child: Text(
        "Không có dữ liệu để hiển thị",
        style: TextStyle(
          fontSize: 16,
          color: Colors.red,
        ),
      ),
    );
  }

  // Helper to build rows with icon
  static Widget _buildInfoRow(String label, String value, {IconData? icon}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (icon != null)
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: Icon(
                icon,
                size: 20,
                color: AppColors.primary,
              ),
            ),
          SizedBox(
            width: 140, // Set fixed width for label
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(height: 1.5),
            ),
          ),
        ],
      ),
    );
  }
}
