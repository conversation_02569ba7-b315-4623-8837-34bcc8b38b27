import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/cssxkd_model.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/cssxkd_nhanvien_model.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/cssxkd_qlcl.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/cssxkd_sanpham_model.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/cssxkd_tailieu_model.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/cssxkd_trangtb_model.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/giay_chungnhan_model.dart';
import 'package:attp_2024/features/tra_cuu/giay_camket/model/giaycn_camket.dart';
import '../../../../../core/data/models/user_access_model.dart';
import '../../../../../core/services/user_use_case.dart';
import '../service/coso_ddk_service.dart';

class GiayCamKetDetailController extends GetxController
    with GetSingleTickerProviderStateMixin {
  final CoSoDuDieuKienService _service = CoSoDuDieuKienService();
  final log = Logger();

  var isLoading = false.obs;
  var sanPhamList = <CoSoSXKDSanPham>[].obs;
  var nhanVienList = <CoSoSXKDNhanVien>[].obs;
  var trangThietBiList = <CoSoSXKDTTB>[].obs;
  var tailieuList = <CoSoSXKDTL>[].obs;
  var gcnDbClList = <CoSoSXKDQLCL>[].obs;
  var csSxkdDetail = <CSSXKD>[].obs;
  var gcnATTPList = <GiayChungNhanATTP>[].obs;

  late TabController tabController;
  final PageController pageController = PageController();

  @override
  void onInit() {
    super.onInit();
    final GiayChungNhanCamKet? item = Get.arguments;
    tabController = TabController(length: 2, vsync: this);
    tabController.addListener(() {
      if (tabController.indexIsChanging) {
        pageController.jumpToPage(tabController.index);
      }
    });
    loadInfoProfile();
  }

  UserAccessModel? userAccessModel;
  Future<void> loadInfoProfile() async {
    userAccessModel = await UserUseCase.getUser();
    update(["bodyID"]);
  }
}
