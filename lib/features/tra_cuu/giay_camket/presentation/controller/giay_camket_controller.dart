// ignore_for_file: constant_identifier_names

import 'dart:async';
import 'package:attp_2024/core/data/models/user_access_model.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:attp_2024/core/configs/contanst/proc_constants.dart';
import 'package:attp_2024/core/data/api/services/proc/proc_service.dart';
import 'package:attp_2024/features/tra_cuu/giay_camket/model/giaycn_camket.dart';
import 'package:attp_2024/core/services/user_use_case.dart';

class GiayCamKetPageController extends GetxController {
  final ProcService _procService = Get.find<ProcService>();
  var isLoading = false.obs;
  var searchResults = <GiayChungNhanCamKet>[].obs;
  var filteredResults = <GiayChungNhanCamKet>[].obs;
  var searchQuery = "".obs;
  final log = Logger();
  var idDefault = "00000000-0000-0000-0000-000000000000";
  var province = ''.obs;
  var district = ''.obs;
  var commune = ''.obs;
  var village = ''.obs;

  var provinces = [].obs;
  var districts = [].obs;
  var communes = [].obs;
  var villages = [].obs;
  var loaiHinhCSs = [].obs;
  var tinhTrangHDs = [].obs;
  var trangThaiXls = [].obs;
  var tinhTrangCapGCN = [].obs;
  var tinhTrangKyGCK = [].obs;

  var provinceFilter = ''.obs;
  var districtFilter = ''.obs;
  var communeFilter = ''.obs;
  var villageFilter = ''.obs;
  var oeFilter = ''.obs;
  var ftFilter = ''.obs;

  var loaihinhCsId = ''.obs;
  var tinhtrangCapGCNId = ''.obs;
  var tinhtrangKyGCK = ''.obs;
  var trangthaiHDId = ''.obs;
  var ketquaXlId = ''.obs;
  var gckId = ''.obs;

  var txtSearchFilter = ''.obs;

  var isLoadingProvinces = false.obs;
  var isLoadingDistricts = false.obs;
  var isLoadingCommunes = false.obs;
  var isLoadingVillages = false.obs;
  var isLoadingTraCuu = false.obs;
  var isLoadingLoaiHinhCS = false.obs;
  var isLoadingTinhTrangHD = false.obs;
  var isTrangThaiXepLoai = false.obs;
  var isTinhTrangCapGCN = false.obs;
  var isTinhTrangKyGCK = false.obs;

  // Phân trang
  var currentPage = 1.obs;
  var pageSize = 10.obs;
  var totalItems = 0.obs;
  var hasMoreData = true.obs;
  var isLoadingMore = false.obs;

  Rx<DateTime> startDate =
      DateTime(DateTime.now().year, 1, 1).obs; // Đầu năm nay
  Rx<DateTime> endDate = DateTime.now().obs; // Hôm nay

  var donViId = '';
  var userId = '';
  var userGroup = ''.obs;
  UserAccessModel? userAccessModel;
  var isLoadingdefaultTinhHuyenXa = false.obs;
  var defaultTinhHuyenXa = [].obs;

  Future<void> onInit() async {
    super.onInit();
    fetchAllProvinces();
    await loadInfoProfile();
    await fetchDonViByID();
    fetchAllLoaiHinhCS();
    fetchAllTinhTrangHD(type: 'HoatDong');
    fetchSearchResults("");
    fetchAllTrangThaiXl();
    fetchAllTinhTrangCapGCN();
    // fetchAllDefaultTinhHuyenXa(donViID: donViId);
  }

  Future<void> loadInfoProfile() async {
    userAccessModel = await UserUseCase.getUser();
    userGroup.value = userAccessModel?.userGroupCode ?? '';
    donViId = userAccessModel?.donViID ?? '';
    userId = userAccessModel?.userID ?? '';
  }

  void clearProvinceFilters() {
    provinceFilter.value = '';
    districtFilter.value = '';
    communeFilter.value = '';
    villageFilter.value = '';
    districts.clear();
    communes.clear();
    villages.clear();
  }

  void clearDistrictFilters() {
    districtFilter.value = '';
    communeFilter.value = '';
    villageFilter.value = '';
    communes.clear();
    villages.clear();
  }

  void clearCommuneFilters() {
    communeFilter.value = '';
    villageFilter.value = '';
    villages.clear();
  }

  // Reset phân trang
  void resetPagination() {
    currentPage.value = 1;
    hasMoreData.value = true;
    searchResults.clear();
    filteredResults.clear();
  }

  // Tải thêm dữ liệu
  Future<void> loadMore() async {
    if (!hasMoreData.value || isLoadingMore.value) return;

    log.i("Đang tải thêm dữ liệu, trang: ${currentPage.value + 1}");
    currentPage.value++;
    isLoadingMore.value = true;

    try {
      await fetchSearchResultsWithFilters(isLoadMore: true);
    } catch (e) {
      log.e("Lỗi khi tải thêm dữ liệu: $e");
      // Nếu gặp lỗi, quay lại trang trước đó
      currentPage.value--;
    } finally {
      isLoadingMore.value = false;
    }
  }

  // Kiểm tra xem còn dữ liệu để tải không dựa vào kết quả trả về
  void checkHasMoreData(List<GiayChungNhanCamKet> results) {
    hasMoreData.value = results.length >= pageSize.value;
    log.i(
        "Còn dữ liệu để tải: ${hasMoreData.value}, số lượng kết quả: ${results.length}, pageSize: ${pageSize.value}");
  }

  Future<void> fetchAllLoaiHinhCS() async {
    isLoadingLoaiHinhCS.value = true;
    final List<Map<String, dynamic>> body = [];
    try {
      List<dynamic> response = await _procService.callProc(
          ProcConstants.getAllComboLoaiHinhCS, body);
      List<Map<String, dynamic>> mappedResponse =
          response.cast<Map<String, dynamic>>();

      loaiHinhCSs.assignAll(
        mappedResponse.map((Map<String, dynamic> loaiHinhCS) {
          return {
            "value": loaiHinhCS['LoaiHinhCoSoID'],
            "display": loaiHinhCS['TenLoaiHinhCoSo'],
          };
        }).toList(),
      );
    } finally {
      isLoadingLoaiHinhCS.value = false;
    }
  }

  Future<void> fetchAllProvinces() async {
    isLoadingProvinces.value = true;
    final List<Map<String, dynamic>> body = [];
    try {
      List<dynamic> provincesResponse =
          await _procService.callProc(ProcConstants.getAllDiaBanHCTinh, body);
      List<Map<String, dynamic>> mappedTinhResponse =
          provincesResponse.cast<Map<String, dynamic>>();
      provinces.assignAll(
        mappedTinhResponse.map((Map<String, dynamic> tinhs) {
          return {
            "value": tinhs['DiaBanHCID'],
            "display": tinhs['TenDiaBan'],
          };
        }).toList(),
      );
    } finally {
      isLoadingProvinces.value = false;
    }
  }

  Future<void> fetchAllDistricts({required String tinhID}) async {
    isLoadingDistricts.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "TinhID", "type": "guid", "value": tinhID},
    ];
    try {
      List<dynamic> districtsResponse =
          await _procService.callProc(ProcConstants.getAllDiaBanHCHuyen, body);
      List<Map<String, dynamic>> mappedHuyenResponse =
          districtsResponse.cast<Map<String, dynamic>>();

      districts.assignAll(
        mappedHuyenResponse.map((Map<String, dynamic> huyens) {
          return {
            "value": huyens['DiaBanHCID'],
            "display": huyens['TenDiaBan'],
          };
        }).toList(),
      );
    } finally {
      isLoadingDistricts.value = false;
    }
  }

  Future<void> fetchAllCommunes({required String huyenID}) async {
    isLoadingCommunes.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "HuyenID", "type": "guid", "value": huyenID},
    ];
    try {
      List<dynamic> communesResponse =
          await _procService.callProc(ProcConstants.getAllDiaBanHCXa, body);
      List<Map<String, dynamic>> mappedXaResponse =
          communesResponse.cast<Map<String, dynamic>>();

      communes.assignAll(
        mappedXaResponse.map((Map<String, dynamic> xas) {
          return {
            "value": xas['DiaBanHCID'],
            "display": xas['TenDiaBan'],
          };
        }).toList(),
      );
    } finally {
      isLoadingCommunes.value = false;
    }
  }

  Future<void> fetchAllVillages({required String xaID}) async {
    isLoadingVillages.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "xaID", "type": "guid", "value": xaID},
    ];
    try {
      List<dynamic> villagesResponse =
          await _procService.callProc(ProcConstants.getAllDiaBanHCThon, body);
      List<Map<String, dynamic>> mappedThonResponse =
          villagesResponse.cast<Map<String, dynamic>>();

      villages.assignAll(
        mappedThonResponse.map((Map<String, dynamic> thons) {
          return {
            "value": thons['DiaBanHCID'],
            "display": thons['TenDiaBan'],
          };
        }).toList(),
      );
    } finally {
      isLoadingVillages.value = false;
    }
  }

  Future<void> fetchDonViByID() async {
    isLoadingdefaultTinhHuyenXa.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "ID", "type": "guid", "value": donViId}
    ];
    try {
      List<dynamic> res =
          await _procService.callProc('Proc_Mobile_GetDonViByID', body);
      List<Map<String, dynamic>> mappedResponse =
          res.cast<Map<String, dynamic>>();
      defaultTinhHuyenXa.assignAll(
        mappedResponse.map((Map<String, dynamic> item) {
          return {
            "TinhID_": item['TinhID_'],
            "HuyenID_": item['HuyenID_'],
            "XaID_": item['XaID_'],
            "ThonID_": item['ThonID_'],
          };
        }).toList(),
      );
      log.i(defaultTinhHuyenXa.toString());
      setDefaultTinhHuyenXa();
    } finally {
      isLoadingdefaultTinhHuyenXa.value = false;
    }
  }

  void setDefaultTinhHuyenXa() {
    fetchAllProvinces();
    fetchAllDistricts(tinhID: defaultTinhHuyenXa.first['TinhID_']);
    fetchAllCommunes(huyenID: defaultTinhHuyenXa.first['HuyenID_']);
    fetchAllVillages(xaID: defaultTinhHuyenXa.first['XaID_']);
    if (defaultTinhHuyenXa.isNotEmpty &&
        defaultTinhHuyenXa.first['TinhID_'] !=
            '00000000-0000-0000-0000-000000000000') {
      provinceFilter.value = defaultTinhHuyenXa.first['TinhID_'];
    }
    if (defaultTinhHuyenXa.isNotEmpty &&
        defaultTinhHuyenXa.first['HuyenID_'] !=
            '00000000-0000-0000-0000-000000000000') {
      districtFilter.value = defaultTinhHuyenXa.first['HuyenID_'];
    }
    if (defaultTinhHuyenXa.isNotEmpty &&
        defaultTinhHuyenXa.first['XaID_'] !=
            '00000000-0000-0000-0000-000000000000') {
      communeFilter.value = defaultTinhHuyenXa.first['XaID_'];
    }
    if (defaultTinhHuyenXa.isNotEmpty &&
        defaultTinhHuyenXa.first['ThonID_'] !=
            '00000000-0000-0000-0000-000000000000') {
      villageFilter.value = defaultTinhHuyenXa.first['ThonID_'];
    }
  }

  Future<void> fetchAllKetQuaXL({required String xaID}) async {
    isLoadingVillages.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "xaID", "type": "guid", "value": xaID},
    ];
    try {
      List<dynamic> villagesResponse =
          await _procService.callProc(ProcConstants.getAllDiaBanHCThon, body);
      List<Map<String, dynamic>> mappedThonResponse =
          villagesResponse.cast<Map<String, dynamic>>();

      villages.assignAll(
        mappedThonResponse.map((Map<String, dynamic> thons) {
          return {
            "value": thons['DiaBanHCID'],
            "display": thons['TenDiaBan'],
          };
        }).toList(),
      );
    } finally {
      isLoadingVillages.value = false;
    }
  }

  Future<void> fetchSearchResults(String query) async {
    resetPagination();
    isLoading.value = true;
    try {
      // print(_buildRequestParams(query).toString());
      final response = await _procService.callProc(
          "Proc_Mobile_TraCuu_ThongTinGCNGCK_ATTP_PT",
          _buildRequestParams(query));
      searchResults.value = response.isNotEmpty
          ? response
              .map<GiayChungNhanCamKet>((item) =>
                  GiayChungNhanCamKet.fromJson(item as Map<String, dynamic>))
              .toList()
          : [];
      filteredResults.value = searchResults;

      // Kiểm tra nếu còn dữ liệu để tải
      checkHasMoreData(searchResults);
    } catch (e) {
      log.e("Error fetching search results: $e");
      searchResults.clear();
      filteredResults.clear();
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> fetchAllTrangThaiXl() async {
    isTrangThaiXepLoai.value = true;
    try {
      List<dynamic> response =
          await _procService.callProc(ProcConstants.getAllTrangThaiXepLoai, []);
      List<Map<String, dynamic>> mappedResponse =
          response.cast<Map<String, dynamic>>();

      trangThaiXls.assignAll(
        mappedResponse.map((Map<String, dynamic> trangthaiXepLoai) {
          return {
            "value": trangthaiXepLoai['MaTrangThai'],
            "display": trangthaiXepLoai['TenTrangThai'],
          };
        }).toList(),
      );
    } finally {
      isTrangThaiXepLoai.value = false;
    }
  }

  Future<void> fetchSearchResultsWithFilters({bool isLoadMore = false}) async {
    if (!isLoadMore) {
      resetPagination();
      isLoading.value = true;
    }

    try {
      final List<Map<String, dynamic>> body = [
        {"Type": "DateTime", "Name": "TuNgay", "Value": null},
        {"Type": "DateTime", "Name": "DenNgay", "Value": null},
        {
          "Type": "Guid",
          "Name": "TinhID",
          "Value": provinceFilter.value.isEmpty
              ? "00000000-0000-0000-0000-000000000000"
              : provinceFilter.value
        },
        {
          "Type": "Guid",
          "Name": "HuyenID",
          "Value": districtFilter.value.isEmpty
              ? "00000000-0000-0000-0000-000000000000"
              : districtFilter.value
        },
        {
          "Type": "Guid",
          "Name": "XaID",
          "Value": communeFilter.value.isEmpty
              ? "00000000-0000-0000-0000-000000000000"
              : communeFilter.value
        },
        {
          "Type": "Guid",
          "Name": "ThonID",
          "Value": villageFilter.value.isEmpty
              ? "00000000-0000-0000-0000-000000000000"
              : villageFilter.value
        },
        {
          "Type": "Guid",
          "Name": "TinhTrang_GCN",
          "Value": tinhtrangCapGCNId.value.isEmpty
              ? "00000000-0000-0000-0000-000000000000"
              : tinhtrangCapGCNId.value
        },
        {
          "Type": "Guid",
          "Name": "LoaiHinhCoSoID",
          "Value": loaihinhCsId.value.isEmpty
              ? "00000000-0000-0000-0000-000000000000"
              : loaihinhCsId.value
        },
        {"Type": "String", "Name": "TuKhoa", "Value": ""},
        {"Type": "String", "Name": "ThuocLoai", "Value": "02"},
        {
          "Type": "Int",
          "Name": "PageNumber",
          "Value": currentPage.value.toString()
        },
        {"Type": "Int", "Name": "PageSize", "Value": pageSize.value.toString()}
      ];

      log.t("Body request: $body");
      log.i("Đang tải trang ${currentPage.value}, pageSize: ${pageSize.value}");

      final response = await _procService.callProc(
          "Proc_Mobile_TraCuu_ThongTinGCNGCK_ATTP_PT", body);

      log.i("Kết quả nhận được: ${response.length} bản ghi");

      final newResults = response.isNotEmpty
          ? response
              .map<GiayChungNhanCamKet>((item) =>
                  GiayChungNhanCamKet.fromJson(item as Map<String, dynamic>))
              .toList()
          : <GiayChungNhanCamKet>[];

      if (isLoadMore) {
        // Thêm kết quả mới vào danh sách hiện tại
        searchResults.addAll(newResults);
        filteredResults.value = searchResults;
      } else {
        // Thay thế toàn bộ danh sách
        searchResults.value = newResults;
        filteredResults.value = searchResults;
      }

      // Kiểm tra nếu còn dữ liệu để tải
      checkHasMoreData(newResults);
    } catch (e) {
      log.e("Error fetching search results: $e");
      if (!isLoadMore) {
        searchResults.clear();
        filteredResults.clear();
      }
    } finally {
      if (!isLoadMore) {
        isLoading.value = false;
      }
      isLoadingMore.value = false;
    }
  }

  Future<void> fetchAllTinhTrangHD({required String type}) async {
    isLoadingTinhTrangHD.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "GroupType", "type": "string", "value": type},
    ];
    try {
      List<dynamic> response =
          await _procService.callProc("Proc_Mobile_Get_TrangThai", body);
      List<Map<String, dynamic>> mappedResponse =
          response.cast<Map<String, dynamic>>();

      tinhTrangHDs.assignAll(
        mappedResponse.map((Map<String, dynamic> tinhTrangHD) {
          return {
            "value": tinhTrangHD['MaTrangThai'],
            "display": tinhTrangHD['TenTrangThai'],
          };
        }).toList(),
      );
    } finally {
      isLoadingTinhTrangHD.value = false;
    }
  }

  Future<void> fetchAllTinhTrangCapGCN() async {
    isTinhTrangCapGCN.value = true;
    try {
      List<dynamic> response =
          await _procService.callProc(ProcConstants.getAllTinhTrangKyGCK, []);
      List<Map<String, dynamic>> mappedResponse =
          response.cast<Map<String, dynamic>>();

      tinhTrangCapGCN.assignAll(
        mappedResponse.map((Map<String, dynamic> tinhTrangCapGCN) {
          return {
            "value": tinhTrangCapGCN['TrangThaiID'],
            "display": tinhTrangCapGCN['TenTrangThai'],
          };
        }).toList(),
      );
    } finally {
      isLoadingTinhTrangHD.value = false;
    }
  }

  List<Map<String, dynamic>> _buildRequestParams(String query) {
    return [
      {"Type": "DateTime", "Name": "TuNgay", "Value": null},
      {"Type": "DateTime", "Name": "DenNgay", "Value": null},
      {
        "Type": "Guid",
        "Name": "TinhID",
        "Value": provinceFilter.value.isEmpty
            ? "00000000-0000-0000-0000-000000000000"
            : provinceFilter.value
      },
      {
        "Type": "Guid",
        "Name": "HuyenID",
        "Value": districtFilter.value.isEmpty
            ? "00000000-0000-0000-0000-000000000000"
            : districtFilter.value
      },
      {
        "Type": "Guid",
        "Name": "XaID",
        "Value": communeFilter.value.isEmpty
            ? "00000000-0000-0000-0000-000000000000"
            : communeFilter.value
      },
      {
        "Type": "Guid",
        "Name": "ThonID",
        "Value": villageFilter.value.isEmpty
            ? "00000000-0000-0000-0000-000000000000"
            : villageFilter.value
      },
      {
        "Type": "Guid",
        "Name": "TinhTrang_GCN",
        "Value": "00000000-0000-0000-0000-000000000000"
      },
      {
        "Type": "Guid",
        "Name": "LoaiHinhCoSoID",
        "Value": "00000000-0000-0000-0000-000000000000"
      },
      {"Type": "String", "Name": "TuKhoa", "Value": query},
      {"Type": "String", "Name": "ThuocLoai", "Value": "02"},
      {
        "Type": "Int",
        "Name": "PageNumber",
        "Value": currentPage.value.toString()
      },
      {"Type": "Int", "Name": "PageSize", "Value": pageSize.value.toString()}
    ];
  }
}
