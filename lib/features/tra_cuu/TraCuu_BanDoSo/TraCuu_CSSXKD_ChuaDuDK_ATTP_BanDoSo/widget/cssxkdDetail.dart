import 'dart:developer';

import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/features/phan_anh_cssxkd/presentation/controller/phan_anh_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:attp_2024/core/ui/widgets/customCachedImage/customCachedImage.dart';
import '../../../../../core/configs/theme/app_colors.dart';
import '../../../../../core/routes/routes.dart';
import '../presentation/controller/TraCuu_CSSXKD_ChuaDuDK_ATTP_BanDoSo_Controller.dart';

class CssxkdDetailModal
    extends GetView<TracuuCssxkdChuadudkAttpBandosoController> {
  final Map<String, dynamic> info;

  const CssxkdDetailModal({super.key, required this.info});

  static void show(BuildContext context, Map<String, dynamic> info) {
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (context) {
        return SizedBox(
          height: MediaQuery.of(context).size.height * 0.69,
          child: CssxkdDetailModal(info: info),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Divider(
            color: Colors.grey.withOpacity(0.3),
            thickness: 3.5,
            indent: 150,
            endIndent: 150,
          ),
          Row(
            children: [
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '${info['MaCoSoSXKD']} - ${info['TenCoSoSXKD']}',
                      style: const TextStyle(
                        fontSize: AppDimens.textSize16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryFocus,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  IconButton(
                    icon: const Icon(
                      Icons.clear,
                      color: Colors.red,
                    ),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          ClipRRect(
            borderRadius: BorderRadius.circular(8.0),
            child: CustomCachedImage(
              width: 400,
              height: 150,
              imageUrl:
                  '${controller.userAccessModel?.siteURL}\\${info['HinhAnh']?.replaceAll('*', '') ?? ''}',
              defaultImage: 'https://via.placeholder.com/150',
            ),
          ),
          const SizedBox(height: 16),
          Text.rich(
            TextSpan(
              children: [
                const WidgetSpan(
                  child:
                      Icon(Icons.location_on, size: 16, color: AppColors.red2),
                ),
                const WidgetSpan(
                  child: SizedBox(width: 4),
                ),
                TextSpan(
                  text: info['DiaChiCS'],
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Text.rich(
            TextSpan(
              children: [
                const WidgetSpan(
                  child: Icon(
                    Icons.account_tree_outlined,
                    size: 16,
                    color: AppColors.customColor3,
                  ),
                ),
                const WidgetSpan(
                  child: SizedBox(width: 4),
                ),
                TextSpan(
                  text: info['LoaiHinhCoSo'],
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Text.rich(
            TextSpan(
              text: 'Tình trạng hoạt động: ',
              style: const TextStyle(fontSize: 16),
              children: [
                TextSpan(
                  text: info['TrangThai'],
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Color(int.parse(
                        info['MauSac_TrangThai'].replaceAll('#', '0xFF'))),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Flexible(
                flex: 3, // 3 phần
                child: ElevatedButton(
                  onPressed: () {
                    Get.find<PhanAnhController>().updateArguments(info);
                    Get.offNamed(Routes.phanAnh);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.warningDark,
                    foregroundColor: AppColors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(5.0),
                    ),
                  ),
                  child: const Text(
                    'Phản ánh',
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Flexible(
                flex: 7, // 7 phần
                child: ElevatedButton(
                  onPressed: () {
                    Get.toNamed(
                      Routes.coSoDuDieuKienDetail,
                      arguments: info['CoSoSXKDID'],
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.iconColors,
                    foregroundColor: AppColors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(5.0),
                    ),
                  ),
                  child: const Text(
                    'Xem chi tiết thông tin cơ sở',
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
