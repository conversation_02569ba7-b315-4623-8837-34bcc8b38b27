import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:get/get.dart';

import '../presentation/controller/TraCuu_CSSXKD_ChuaDuDK_ATTP_BanDoSo_Controller.dart';

class Mapscreen extends GetView<TracuuCssxkdChuadudkAttpBandosoController> {
  const Mapscreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return GoogleMap(
        mapType: MapType.normal,
        initialCameraPosition: const CameraPosition(
          target: LatLng(9.766749, 105.608056),
          zoom: 11.0,
        ),
        onMapCreated: controller.onMapCreated,
        markers: controller.markers.toSet(),
      );
    });
  }
}