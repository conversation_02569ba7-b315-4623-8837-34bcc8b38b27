import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import '../../../../../core/configs/dimens/app_dimens.dart';
import '../../../../../core/configs/theme/app_colors.dart';
import '../../../../../core/ui/widgets/CustomDatePicker/CustomDatePicker.dart';
import '../../../../../core/ui/widgets/button/button_widget.dart';
import '../../../../../core/ui/widgets/custom_combo/combo.dart';
import '../presentation/controller/TraCuu_CSSXKD_ChuaDuDK_ATTP_BanDoSo_Controller.dart';

class FilterModal extends GetView<TracuuCssxkdChuadudkAttpBandosoController> {
  const FilterModal({super.key});

  static void show(BuildContext context) {
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (context) {
        return SizedBox(
            height: 80.h,
            // height: MediaQuery.of(context).size.height * 0.66,
            child: const FilterModal());
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Container(
        padding: const EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Divider(
              color: Colors.grey.withOpacity(0.3),
              thickness: 3.5,
              indent: 150,
              endIndent: 150,
            ),
            Row(
              children: [
                const Spacer(),
                const Spacer(),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Tra cứu nâng cao',
                      style: TextStyle(
                        fontSize: AppDimens.largeText,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryFocus,
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconButton(
                      icon: const Icon(
                        Icons.clear,
                        color: Colors.red,
                      ),
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: CustomDatePicker(
                          isRequired: true,
                          title: 'Từ ngày',
                          placeholder: 'dd-MM-yyyy',
                          date: DateTime(2025, 1, 1),
                          onChange: (DateTime? newDate) {
                            print("startDate: ${controller.startDate.value}");
                            controller.startDate.value = newDate!;
                          },
                        ),
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: CustomDatePicker(
                          isRequired: true,
                          title: 'Đến ngày',
                          placeholder: 'dd-MM-yyyy',
                          date: DateTime(2025, 12, 31),
                          onChange: (DateTime? newDate) {
                            print("endDate: ${controller.endDate.value}");
                            controller.endDate.value = newDate!;
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  Row(
                    children: [
                      Expanded(
                        child: Obx(() => CustomCombobox(
                              title: "Tỉnh/Thành Phố",
                              weight: 45.w,
                              isRequired: true,
                              onChange: (selectedProvince) {
                                if (selectedProvince != null) {
                                  controller.provinceFilter.value =
                                      selectedProvince.id.toString();
                                  controller.fetchAllDistricts(
                                      tinhID: selectedProvince.id.toString());
                                }
                              },
                              delete: () {
                                controller.districtFilter.value = '';
                                controller.communeFilter.value = '';
                                controller.villageFilter.value = '';
                                controller.districts.clear();
                                controller.communes.clear();
                                controller.villages.clear();
                              },
                              dropDownList: controller.provinces
                                  .map((e) => DropdownModel(
                                        id: e['value'],
                                        display: e['display'],
                                      ))
                                  .toList(),
                              defaultSelectedItem: controller
                                      .provinceFilter.value.isNotEmpty
                                  ? DropdownModel(
                                      id: controller.provinceFilter.value,
                                      display: controller.provinces.firstWhere(
                                        (item) =>
                                            item['value'] ==
                                            controller.provinceFilter.value,
                                        orElse: () => {'display': ''},
                                      )['display'],
                                    )
                                  : null,
                            )),
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: Obx(() => CustomCombobox(
                              title: "Quận/Huyện",
                              weight: 45.w,
                              onChange: (selectedDistrict) {
                                if (selectedDistrict != null) {
                                  controller.districtFilter.value =
                                      selectedDistrict.id.toString();
                                  controller.fetchAllCommunes(
                                      huyenID: selectedDistrict.id.toString());
                                }
                              },
                              delete: () {
                                controller.districtFilter.value = '';
                                controller.communeFilter.value = '';
                                controller.villageFilter.value = '';
                                controller.communes.clear();
                                controller.villages.clear();
                              },
                              dropDownList: controller.districts
                                  .map((e) => DropdownModel(
                                        id: e['value'],
                                        display: e['display'],
                                      ))
                                  .toList(),
                              defaultSelectedItem: controller
                                      .districtFilter.value.isNotEmpty
                                  ? DropdownModel(
                                      id: controller.districtFilter.value,
                                      display: controller.districts.firstWhere(
                                        (item) =>
                                            item['value'] ==
                                            controller.districtFilter.value,
                                        orElse: () => {'display': ''},
                                      )['display'],
                                    )
                                  : null,
                            )),
                      ),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Obx(() => CustomCombobox(
                              title: "Xã/Phường",
                              weight: 45.w,
                              onChange: (selectedCommune) {
                                if (selectedCommune != null) {
                                  controller.communeFilter.value =
                                      selectedCommune.id.toString();
                                  controller.fetchAllVillages(
                                      xaID: selectedCommune.id.toString());
                                }
                              },
                              delete: () {
                                controller.communeFilter.value = '';
                                controller.villageFilter.value = '';
                                controller.villages.clear();
                              },
                              dropDownList: controller.communes
                                  .map((e) => DropdownModel(
                                        id: e['value'],
                                        display: e['display'],
                                      ))
                                  .toList(),
                              defaultSelectedItem: controller
                                      .communeFilter.value.isNotEmpty
                                  ? DropdownModel(
                                      id: controller.communeFilter.value,
                                      display: controller.communes.firstWhere(
                                        (item) =>
                                            item['value'] ==
                                            controller.communeFilter.value,
                                        orElse: () => {'display': ''},
                                      )['display'],
                                    )
                                  : null,
                            )),
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: Obx(() => CustomCombobox(
                              title: "Thôn/Xóm",
                              weight: 45.w,
                              onChange: (selectedVillage) {
                                controller.villageFilter.value =
                                    selectedVillage!.id.toString();
                                print(
                                    "HUHU: ${controller.villageFilter.value}");
                              },
                              dropDownList: controller.villages
                                  .map((e) => DropdownModel(
                                        id: e['value'],
                                        display: e['display'],
                                      ))
                                  .toList(),
                            )),
                      ),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Obx(() => CustomCombobox(
                              title: "Loại hình cơ sở",
                              weight: 95.w,
                              onChange: (selectedLoaiHinhCS) {
                                controller.oeFilter.value =
                                    selectedLoaiHinhCS!.id.toString();
                                print("HUHU: ${controller.oeFilter.value}");
                              },
                              delete: () {
                                controller.villageFilter.value = '';
                              },
                              dropDownList: controller.loaiHinhCSs
                                  .map((e) => DropdownModel(
                                        id: e['value'],
                                        display: e['display'],
                                      ))
                                  .toList(),
                              defaultSelectedItem: controller
                                      .villageFilter.value.isNotEmpty
                                  ? DropdownModel(
                                      id: controller.villageFilter.value,
                                      display: controller.villages.firstWhere(
                                        (item) =>
                                            item['value'] ==
                                            controller.villageFilter.value,
                                        orElse: () => {'display': ''},
                                      )['display'],
                                    )
                                  : null,
                            )),
                      ),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Obx(() => CustomCombobox(
                              title: "Tình trạng hoạt động",
                              weight: 95.w,
                              onChange: (selectedLoaiHinhHD) {
                                controller.ftFilter.value =
                                    selectedLoaiHinhHD!.id.toString();
                                print("HUHU: ${controller.ftFilter.value}");
                              },
                              dropDownList: controller.tinhTrangHDs
                                  .map((e) => DropdownModel(
                                        id: e['value'],
                                        display: e['display'],
                                      ))
                                  .toList(),
                            )),
                      ),
                    ],
                  ),
                  const SizedBox(height: 5),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // ButtonWidget(
                      //   text: 'Đóng',
                      //   ontap: () {
                      //     Navigator.of(context).pop();
                      //     //controller.clearSearchValue();
                      //   },
                      //   width: 100,
                      //   backgroundColor: Colors.grey[300],
                      //   textColor: Colors.red,
                      //   leadingIcon: const Icon(
                      //     Icons.clear,
                      //     size: 20,
                      //     color: Colors.red,
                      //   ),
                      //   borderRadius: 12,
                      //   padding: const EdgeInsets.symmetric(
                      //       horizontal: 12.0, vertical: 8.0),
                      //   textSize: 14.0,
                      // ),
                      // const SizedBox(width: 8),
                      // ButtonWidget(
                      //   text: 'Tìm kiếm',
                      //   ontap: () {
                      //     // Todo: Implement search functionality
                      //     controller.traCuu();
                      //     Navigator.of(context).pop();
                      //   },
                      //   width: 131,
                      //   backgroundColor: Colors.green,
                      //   textColor: AppColors.white,
                      //   isBorder: true,
                      //   borderColor: Colors.grey[200],
                      //   leadingIcon: const Icon(
                      //     Icons.search,
                      //     size: 20,
                      //     color: Colors.white,
                      //   ),
                      //   borderRadius: 12,
                      //   padding: const EdgeInsets.symmetric(
                      //       horizontal: 12.0, vertical: 8.0),
                      //   textSize: 14.0,
                      // ),
                      Obx(() => ButtonWidget(
                        text: 'Áp dụng',
                        ontap: () {
                          // Todo: Implement search functionality
                          controller.traCuu();
                          Navigator.of(context).pop();
                        },
                        width: MediaQuery.of(context).size.width * 0.90,
                        backgroundColor: Colors.green,
                        textColor: AppColors.white,
                        isBorder: true,
                        borderColor: Colors.grey[100],
                        borderRadius: 6,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12.0, vertical: 10.0),
                        textSize: AppDimens.textSize16,
                        enabled: controller.provinceFilter.value.isNotEmpty,
                      )),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
