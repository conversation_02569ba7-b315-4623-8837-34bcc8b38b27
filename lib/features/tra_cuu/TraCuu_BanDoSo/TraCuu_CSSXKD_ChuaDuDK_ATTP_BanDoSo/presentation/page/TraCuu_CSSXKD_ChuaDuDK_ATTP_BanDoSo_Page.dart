import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:get/get.dart';

import '../../widget/MapScreen.dart';
import '../../widget/cssxkdDetail.dart';
import '../../widget/filter_modal.dart';
import '../../widget/SearchDelegate.dart';
import '../controller/TraCuu_CSSXKD_ChuaDuDK_ATTP_BanDoSo_Controller.dart';


class TracuuCssxkdChuadudkAttpBandosoPage extends GetView<TracuuCssxkdChuadudkAttpBandosoController> {
  const TracuuCssxkdChuadudkAttpBandosoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [

          const Mapscreen(),

            Positioned(
            top: 35,
            left: 10,
            right: 10,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(32),
                boxShadow: const [
                  BoxShadow(
                    color: Colors.black26,
                    blurRadius: 4,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: TextField(
                readOnly: true,
                onTap: () {
                  showSearch(
                    context: context,
                    delegate: MapSearchDelegate(controller),
                  );
                },
                decoration: InputDecoration(
                  hintText: 'Tìm kiếm ...',
                  prefixIcon: IconButton(
                    icon: const Icon(Icons.arrow_back),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                  suffixIcon: IconButton(
                    icon: const Icon(Icons.filter_alt_outlined),
                    onPressed: () {
                      FilterModal.show(context);
                    },
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
