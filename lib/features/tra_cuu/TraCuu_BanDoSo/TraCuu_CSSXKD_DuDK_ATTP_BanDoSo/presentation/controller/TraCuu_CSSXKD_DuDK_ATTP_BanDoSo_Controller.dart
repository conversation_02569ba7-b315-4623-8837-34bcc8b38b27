import 'package:attp_2024/features/phan_anh_cssxkd/presentation/controller/phan_anh_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl/intl.dart';
import 'package:attp_2024/core/configs/contanst/proc_constants.dart';

import '../../../../../../core/data/api/configs/dio_configs.dart';
import '../../../../../../core/data/api/services/proc/proc_service.dart';
import '../../../../../../core/data/models/user_access_model.dart';
import '../../../../../../core/services/user_use_case.dart';
import '../../widget/cssxkdDetail.dart';

class TracuuCssxkdDudkAttpBandosoController extends GetxController {

  PhanAnhController phanAnhController = PhanAnhController();
  final markers = <Marker>{}.obs;
  late GoogleMapController mapController;
  final selectedLocationInfo = ''.obs;
  final Rx<LatLng?> selectedLocation = Rx<LatLng?>(null);
  final Rx<Marker?> selectedMarker = Rx<Marker?>(null);

  BitmapDescriptor markerIcon = BitmapDescriptor.defaultMarker;

  final ProcService _procService = Get.find<ProcService>();

  var province = ''.obs;
  var district = ''.obs;
  var commune = ''.obs;
  var village = ''.obs;

  var provinces = [].obs;
  var districts = [].obs;
  var communes = [].obs;
  var villages = [].obs;
  var loaiHinhCSs = [].obs;
  var tinhTrangHDs = [].obs;
  var cssxkdList = [].obs;

  var provinceFilter = ''.obs;
  var districtFilter = ''.obs;
  var communeFilter = ''.obs;
  var villageFilter = ''.obs;
  var oeFilter = ''.obs;
  var ftFilter = ''.obs;
  var txtSearchFilter = ''.obs;

  Rx<DateTime> startDate = DateTime(2025, 1, 1).obs;
  Rx<DateTime> endDate = DateTime(2025, 12, 31).obs;

  var isLoadingProvinces = false.obs;
  var isLoadingDistricts = false.obs;
  var isLoadingCommunes = false.obs;
  var isLoadingVillages = false.obs;
  var isLoadingTraCuu = false.obs;
  var isLoadingLoaiHinhCS = false.obs;
  var isLoadingTinhTrangHD = false.obs;




  @override
  Future<void> onInit() async {
    super.onInit();
    // customIconMarker();
    fetchAllProvinces();
    fetchAllLoaiHinhCS();
    fetchAllTinhTrangHD(type: "HoatDong");
    loadInfoProfile();
    traCuu();
  }

  void customIconMarker() {
    BitmapDescriptor.asset(
        const ImageConfiguration(), "assets/images/cssx.png")
        .then(
          (icon) {
        markerIcon = icon;
      },
    );
  }

  void onMapCreated(GoogleMapController controller) {
    mapController = controller;
    //_addMarkers(Get.context!);
    //_addInitialMarker(Get.context!);
  }

  void onMapTapped(LatLng position) {
    // Lưu vị trí được chọn
    selectedLocationInfo.value = '';
    selectedLocation.value = position;

    final newMarker = Marker(
      markerId: const MarkerId('user_selected_marker'),
      position: position,
      infoWindow: InfoWindow(
        title: 'Vị trí đã chọn',
        snippet: '${position.latitude}, ${position.longitude}',
      ),
      icon: markerIcon,
    );

    // Thêm marker mới vào danh sách
    markers.add(newMarker);

    markers.refresh();

    print('Selected Location: $position');
  }

void onMarkerTapped(BuildContext context, Map<String, dynamic> info) {
    selectedLocationInfo.value = info['CoSoSXKDID'];
    CssxkdDetailModal.show(context, info);
    print("onMarkerTapped");
}


  void _addMarkers(BuildContext context) async {
    markers.addAll([
      // Marker(
      //   markerId: const MarkerId('nhacuaDung'),
      //   position: const LatLng(10.245851, 105.972414),
      //   infoWindow: const InfoWindow(title: 'Nhà của Dũng'),
      //   icon: markerIcon,
      //   onTap: () => onMarkerTapped(context, {
      //     'CoSoSXKDID': 'c1df525e-ee06-4e6e-9448-3180ccd24dca',
      //     'MaCoSoSXKD': 'CSKD/09999',
      //     'TenCoSoSXKD': 'Nhà của Dũng',
      //     'DiaChiCS': 'P1, Thành Phố Vĩnh Long, Tỉnh Vĩnh Long.',
      //     'LoaiHinhCoSo': 'Loại hình cơ sở',
      //     'TrangThai': 'Đang hoạt động',
      //   }),
      // ),
      Marker(
        markerId: const MarkerId('tinhHG'),
        position: const LatLng(9.766749, 105.608056),
        infoWindow: const InfoWindow(
            title: 'tỉnh Hậu Giang',
            snippet: 'Xem thêm'
        ),
        icon: markerIcon,
        onTap: () => onMarkerTapped(context, {
          'CoSoSXKDID': 'f80882a3-0bac-4872-a747-185d24a878c6',
          'MaCoSoSXKD': 'CSKD/000000',
          'TenCoSoSXKD': 'tỉnh Hậu Giang',
          'DiaChiCS': 'Hậu Giang, Vietnam',
          'LoaiHinhCoSo': 'Hậu Giang, Vietnam',
          'TrangThai': 'Đang hoạt động',
          'MauSac_TrangThai': '#2B7DBC',
        }),
      ),
    ]);
  }

  void _addInitialMarker(BuildContext context) async {
    markers.add(
        Marker(
      markerId: const MarkerId('cty'),
      position: const LatLng(10.243131, 105.950910),
      infoWindow: const InfoWindow(
          title: 'CTY TNHH Phát Triển Phần Mềm Nhất Tâm - NTSOFT',
          snippet: 'Xem thêm'
      ),
      icon: markerIcon,
      onTap: () => onMarkerTapped(context, 'CTY TNHH Phát Triển Phần Mềm Nhất Tâm - NTSOFT' as Map<String, dynamic>),
    ));
  }

  void _onMarkerTapped(String locationName) {
    Get.snackbar('Marker Tapped', 'You tapped on $locationName');
  }

  Future<void> _goToTheLake() async {
    mapController.animateCamera(
        CameraUpdate.newLatLng(const LatLng(10.243131, 105.950910)));
  }

  Future<void> fetchAllProvinces() async {
    isLoadingProvinces.value = true;
    final List<Map<String, dynamic>> body = [];
    try {
      List<dynamic> provincesResponse =
      await _procService.callProc("Proc_Mobile_GetAll_DiaBanHCTinh", body);
      List<Map<String, dynamic>> mappedTinhResponse =
      provincesResponse.cast<Map<String, dynamic>>();

      provinces.assignAll(
        mappedTinhResponse.map((Map<String, dynamic> tinhs) {
          return {
            "value": tinhs['DiaBanHCID'],
            "display": tinhs['TenDiaBan'],
          };
        }).toList(),
      );
      print('Tinh $provinces');
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isLoadingProvinces.value = false;
    }
  }

  Future<void> fetchAllDistricts({required String tinhID}) async {
    isLoadingDistricts.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "TinhID", "type": "guid", "value": tinhID},
    ];
    try {
      List<dynamic> districtsResponse =
      await _procService.callProc("Proc_Mobile_GetAll_DiaBanHCHuyen", body);
      List<Map<String, dynamic>> mappedHuyenResponse =
      districtsResponse.cast<Map<String, dynamic>>();

      districts.assignAll(
        mappedHuyenResponse.map((Map<String, dynamic> huyens) {
          return {
            "value": huyens['DiaBanHCID'],
            "display": huyens['TenDiaBan'],
          };
        }).toList(),
      );
      print('Huyen $districts');
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isLoadingDistricts.value = false;
    }
  }

  Future<void> fetchAllCommunes({required String huyenID}) async {
    isLoadingCommunes.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "HuyenID", "type": "guid", "value": huyenID},
    ];
    try {
      List<dynamic> communesResponse =
      await _procService.callProc("Proc_Mobile_GetAll_DiaBanHCXa", body);
      List<Map<String, dynamic>> mappedXaResponse =
      communesResponse.cast<Map<String, dynamic>>();

      communes.assignAll(
        mappedXaResponse.map((Map<String, dynamic> xas) {
          return {
            "value": xas['DiaBanHCID'],
            "display": xas['TenDiaBan'],
          };
        }).toList(),
      );
      print('Xa $communes');
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isLoadingCommunes.value = false;
    }
  }

  Future<void> fetchAllVillages({required String xaID}) async {
    isLoadingVillages.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "xaID", "type": "guid", "value": xaID},
    ];
    try {
      List<dynamic> villagesResponse =
      await _procService.callProc("Proc_Mobile_GetAll_DiaBanHCThon", body);
      List<Map<String, dynamic>> mappedThonResponse =
      villagesResponse.cast<Map<String, dynamic>>();

      villages.assignAll(
        mappedThonResponse.map((Map<String, dynamic> thons) {
          return {
            "value": thons['DiaBanHCID'],
            "display": thons['TenDiaBan'],
          };
        }).toList(),
      );
      print('Thon $villages');
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isLoadingVillages.value = false;
    }
  }

  Future<void> traCuu() async {
    isLoadingTraCuu.value = true;
    markers.clear();
    final List<Map<String, dynamic>> body = [
      {"Type": "DateTime", "Name": "TuNgay", "Value": DateFormat('yyyy-MM-dd').format(startDate.value)},
      {"Type": "DateTime", "Name": "DenNgay", "Value": DateFormat('yyyy-MM-dd').format(endDate.value)},
      {"Type": "guid", "Name": "DiaBanHCID_Tinh", "Value": provinceFilter.value},
      {"Type": "guid", "Name": "DiaBanHCID_Huyen", "Value": districtFilter.value},
      {"Type": "guid", "Name": "DiaBanHCID_Xa", "Value": communeFilter.value},
      {"Type": "guid", "Name": "DiaBanHCID_Thon", "Value": villageFilter.value},
      {"Type": "string", "Name": "ThuocLoai", "Value": "CapGCN"},
      {"Type": "guid", "Name": "TinhTrangID", "Value": oeFilter.value},
      {"Type": "guid", "Name": "LoaiHinhCoSoID", "Value": ftFilter.value},
      {"Type": "string", "Name": "txtTimKiem", "Value": txtSearchFilter.value},
    ];
    try {
      List<dynamic> response = await _procService.callProc(ProcConstants.traCuuBanDoSo, body);
      cssxkdList.assignAll(response);
      print("TraCuu: $cssxkdList");

      for (var item in cssxkdList) {
        final double latitude = double.parse(item['ToaDoX']);
        final double longitude = double.parse(item['ToaDoY']);
        final newMarker = Marker(
          markerId: MarkerId(item['CoSoSXKDID']),
          position: LatLng(latitude, longitude),
          infoWindow: InfoWindow(
            title: item['TenCoSoSXKD'],
            snippet: item['DiaChiCS'],
          ),
          icon: markerIcon,
          onTap: () => onMarkerTapped(Get.context!, {
            'CoSoSXKDID': item['CoSoSXKDID'],
            'MaCoSoSXKD': item['MaCoSoSXKD'],
            'TenCoSoSXKD': item['TenCoSoSXKD'],
            'NguoiDaiDien': item['NguoiDaiDien'],
            'HinhAnh': item['HinhAnh'],
            'LoGo': item['LoGo'],
            'DiaChiCS': item['DiaChiCS'],
            'LoaiHinhCoSo': item['LoaiHinhCoSo'],
            'TrangThai': item['TrangThai'],
            'MauSac_TrangThai': item['MauSac_TrangThai'],
          }),
        );
        markers.add(newMarker);
      }

      if (markers.isNotEmpty) {
        LatLngBounds bounds = LatLngBounds(
          southwest: LatLng(
            markers.map((marker) => marker.position.latitude).reduce((a, b) => a < b ? a : b),
            markers.map((marker) => marker.position.longitude).reduce((a, b) => a < b ? a : b),
          ),
          northeast: LatLng(
            markers.map((marker) => marker.position.latitude).reduce((a, b) => a > b ? a : b),
            markers.map((marker) => marker.position.longitude).reduce((a, b) => a > b ? a : b),
          ),
        );

        await mapController.animateCamera(CameraUpdate.newLatLngBounds(bounds, 50));
      }

    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isLoadingTraCuu.value = false;
    }
  }

  Future<void> onFloatingButtonPressed() async {

  }

  Future<void> fetchAllLoaiHinhCS() async {
    isLoadingLoaiHinhCS.value = true;
    final List<Map<String, dynamic>> body = [];
    try {
      List<dynamic> response =
      await _procService.callProc("Proc_Mobile_GetAll_LoaiHinhCoSo", body);
      List<Map<String, dynamic>> mappedResponse =
      response.cast<Map<String, dynamic>>();

      loaiHinhCSs.assignAll(
        mappedResponse.map((Map<String, dynamic> loaiHinhCS) {
          return {
            "value": loaiHinhCS['LoaiHinhCoSoID'],
            "display": loaiHinhCS['TenLoaiHinhCoSo'],
          };
        }).toList(),
      );
      print('fetchAllLoaiHinhCS $loaiHinhCSs');
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isLoadingLoaiHinhCS.value = false;
    }
  }

  Future<void> fetchAllTinhTrangHD({required String type}) async {
    isLoadingTinhTrangHD.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "GroupType", "type": "string", "value": type},
    ];
    try {
      List<dynamic> response =
      await _procService.callProc("Proc_Mobile_Get_TrangThai", body);
      List<Map<String, dynamic>> mappedResponse =
      response.cast<Map<String, dynamic>>();

      tinhTrangHDs.assignAll(
        mappedResponse.map((Map<String, dynamic> tinhTrangHD) {
          return {
            "value": tinhTrangHD['TrangThaiID'],
            "display": tinhTrangHD['TenTrangThai'],
          };
        }).toList(),
      );
      print('fetchAllTinhTrangHD $tinhTrangHDs');
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isLoadingTinhTrangHD.value = false;
    }
  }

  UserAccessModel? userAccessModel;

  Future<void> loadInfoProfile() async {
    userAccessModel = await UserUseCase.getUser();
    update(["bodyID"]);
  }

}
