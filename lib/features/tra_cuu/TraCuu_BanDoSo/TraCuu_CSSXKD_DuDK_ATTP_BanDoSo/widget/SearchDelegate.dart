import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../presentation/controller/TraCuu_CSSXKD_DuDK_ATTP_BanDoSo_Controller.dart';

class MapSearchDelegate extends SearchDelegate {
  final TracuuCssxkdDudkAttpBandosoController controller;

  MapSearchDelegate(this.controller);

  @override
  List<Widget>? buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
        },
      ),
    ];
  }

  @override
  Widget? buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, null);
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    if (query.isEmpty) {
      return Container();
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.txtSearchFilter.value = query;
      controller.traCuu();
    });

    final result = controller.cssxkdList.where(
      (item) => item['TenCoSoSXKD'].toLowerCase().contains(query.toLowerCase()),
    );

    return ListView(
      children: result.map((item) {
        return ListTile(
          title: Text(item['TenCoSoSXKD']),
          onTap: () {
            final position = LatLng(
                double.parse(item['ToaDoX']), double.parse(item['ToaDoY']));
            controller.mapController.animateCamera(
              CameraUpdate.newCameraPosition(
                CameraPosition(target: position, zoom: 15),
              ),
            );
            controller.mapController.showMarkerInfoWindow(MarkerId(item['CoSoSXKDID']));
            close(context, null);
          },
        );
      }).toList(),
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    if (query.isEmpty) {
      return Container();
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.txtSearchFilter.value = query;
      controller.traCuu();
    });

    final suggestions = controller.cssxkdList.where(
      (item) => item['TenCoSoSXKD'].toLowerCase().contains(query.toLowerCase()),
    );

    return ListView(
      children: suggestions.map((item) {
        return ListTile(
          title: Text(item['TenCoSoSXKD']),
          onTap: () {
            final position = LatLng(
                double.parse(item['ToaDoX']), double.parse(item['ToaDoY']));
            controller.mapController.animateCamera(
              CameraUpdate.newCameraPosition(
                CameraPosition(target: position, zoom: 15),
              ),
            );
            controller.mapController.showMarkerInfoWindow(MarkerId(item['CoSoSXKDID']));
            close(context, null);
          },
        );
      }).toList(),
    );
  }
}
