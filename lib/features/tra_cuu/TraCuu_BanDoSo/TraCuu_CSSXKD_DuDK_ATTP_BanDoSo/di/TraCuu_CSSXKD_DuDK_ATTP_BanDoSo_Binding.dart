import 'package:attp_2024/features/phan_anh_cssxkd/presentation/controller/phan_anh_controller.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/bindings_interface.dart';

import '../presentation/controller/TraCuu_CSSXKD_DuDK_ATTP_BanDoSo_Controller.dart';

class TracuuCssxkdDudkAttpBandosoBinding extends Bindings {
  //final _procService = GetIt.I<ProcService>();
  @override
  void dependencies() {
    Get.lazyPut(() => PhanAnhController());
    Get.lazyPut(() => TracuuCssxkdDudkAttpBandosoController());
  }
}
