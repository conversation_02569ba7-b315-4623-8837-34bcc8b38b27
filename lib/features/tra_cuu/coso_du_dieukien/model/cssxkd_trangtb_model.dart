class CoSoSXKDTTB {
  final String? coSoSXKDTTBID;
  final String? coSoSXKDID;
  final String? maCoSoSXKDTTB;
  final String? tenTTB;
  final double? soLuong;
  final String? nuocSXID;
  final String? tongCongSuat;
  final int? namBatDauSD;
  final String? ghiChu;
  final bool? trangThai;
  final String? userID;
  final String? donViID;
  final String? ngayTao;
  final String? ngayCapNhat;
  final String? tenNuocSanXuat;

  CoSoSXKDTTB({
    this.coSoSXKDTTBID,
    this.coSoSXKDID,
    this.maCoSoSXKDTTB,
    this.tenTTB,
    this.soLuong,
    this.nuocSXID,
    this.tongCongSuat,
    this.namBatDauSD,
    this.ghiChu,
    this.trangThai,
    this.userID,
    this.donViID,
    this.ngayTao,
    this.ngayCapNhat,
    this.tenNuocSanXuat,
  });

  factory CoSoSXKDTTB.fromJson(Map<String, dynamic> json) {
    return CoSoSXKDTTB(
      coSoSXKDTTBID: json['CoSoSXKDTTBID'] as String?,
      coSoSXKDID: json['CoSoSXKDID'] as String?,
      maCoSoSXKDTTB: json['MaCoSoSXKDTTB'] as String?,
      tenTTB: json['TenTTB'] as String?,
      soLuong: (json['SoLuong'] as num?)?.toDouble(),
      nuocSXID: json['NuocSXID'] as String?,
      tongCongSuat: json['TongCongSuat'] as String?,
      namBatDauSD: json['NamBatDauSD'] != null
          ? int.tryParse(json['NamBatDauSD'].toString())
          : null, // Chuyển đổi kiểu dữ liệu sang int
      ghiChu: json['GhiChu'] as String?,
      trangThai: json['TrangThai'] as bool?,
      userID: json['UserID'] as String?,
      donViID: json['DonViID'] as String?,
      ngayTao: json['NgayTao'] as String?,
      ngayCapNhat: json['NgayCapNhat'] as String?,
      tenNuocSanXuat: json['TenNuocSanXuat'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'CoSoSXKDTTBID': coSoSXKDTTBID,
      'CoSoSXKDID': coSoSXKDID,
      'MaCoSoSXKDTTB': maCoSoSXKDTTB,
      'TenTTB': tenTTB,
      'SoLuong': soLuong,
      'NuocSXID': nuocSXID,
      'TongCongSuat': tongCongSuat,
      'NamBatDauSD': namBatDauSD,
      'GhiChu': ghiChu,
      'TrangThai': trangThai,
      'UserID': userID,
      'DonViID': donViID,
      'NgayTao': ngayTao,
      'NgayCapNhat': ngayCapNhat,
      'TenNuocSanXuat': tenNuocSanXuat,
    };
  }
}
