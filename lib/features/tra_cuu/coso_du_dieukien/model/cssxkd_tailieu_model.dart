class CoSoSXKDTL {
  final String? soVanBan;
  final String? ngayKy;
  final String? trichYeuNoiDung;
  final String? coQuanBanHanh;
  final String? nguoiKy;
  final String? chucVu;
  final String? loaiTaiLieu;
  final String? taiLieuID;
  final String? bangThamChieu;
  final String? dinhKem;

  CoSoSXKDTL({
    this.soVanBan,
    this.ngayKy,
    this.trichYeuNoiDung,
    this.coQuanBanHanh,
    this.nguoiKy,
    this.chucVu,
    this.loaiTaiLieu,
    this.taiLieuID,
    this.bangThamChieu,
    this.dinhKem,
  });

  // <PERSON><PERSON><PERSON> từ JSON sang model
  factory CoSoSXKDTL.fromJson(Map<String, dynamic> json) {
    return CoSoSXKDTL(
      soVanBan: json['SoVanBan'] as String?,
      ngayKy: json['NgayKy'] as String?,
      trichYeuNoiDung: json['TrichYeuNoiDung'] as String?,
      coQuanBanHanh: json['CoQuanBanHanh'] as String?,
      nguoiKy: json['NguoiKy'] as String?,
      chucVu: json['ChucVu'] as String?,
      loaiTaiLieu: json['LoaiTaiLieu'] as String?,
      taiLieuID: json['TaiLieuID'] as String?,
      bangThamChieu: json['BangThamChieu'] as String?,
      dinhKem: json['DinhKem'] as String?,
    );
  }

  // Hàm từ model sang JSON
  Map<String, dynamic> toJson() {
    return {
      'SoVanBan': soVanBan,
      'NgayKy': ngayKy,
      'TrichYeuNoiDung': trichYeuNoiDung,
      'CoQuanBanHanh': coQuanBanHanh,
      'NguoiKy': nguoiKy,
      'ChucVu': chucVu,
      'LoaiTaiLieu': loaiTaiLieu,
      'TaiLieuID': taiLieuID,
      'BangThamChieu': bangThamChieu,
      'DinhKem': dinhKem,
    };
  }
}
