// ignore_for_file: non_constant_identifier_names

class CoSoSXKDSanPham {
  String? coSoSXKDSanPhamID;
  String? tenSanPham;
  String? loaiSanPham;
  String? ngaySX;
  String? hanSuDung;
  bool? trangThai;
  String? dinhKem;
  String? TenNguyenLieuChinh;
  String? CachThucDongGoi;
  String? TenNguonGoc;
  String? MaCoSoSXKDSanPham;
  String? TenNhomSPSXKD;
  String? DanhSachNhomSanPham;

  CoSoSXKDSanPham(
      {this.coSoSXKDSanPhamID,
      this.tenSanPham,
      this.loaiSanPham,
      this.ngaySX,
      this.hanSuDung,
      this.trangThai,
      this.dinhKem,
      this.TenNguyenLieuChinh,
      this.CachThucDongGoi,
      this.MaCoSoSXKDSanPham,
      this.TenNhomSPSXKD,
      this.DanhSachNhomSanPham,
      this.TenNguonGoc});

  factory CoSoSXKDSanPham.fromJson(Map<String, dynamic> json) {
    return CoSoSXKDSanPham(
      coSoSXKDSanPhamID: json['CoSoSXKDSanPhamID'],
      tenSanPham: json['TenCoSoSXKDSanPham'],
      loaiSanPham: json['TenNhomSPSXKD'],
      ngaySX: json['NgaySX'],
      hanSuDung: json['HanSuDung'],
      trangThai: json['TrangThai'],
      dinhKem: json['DinhKem'],
      TenNguyenLieuChinh: json['TenNguyenLieuChinh'],
      CachThucDongGoi: json['CachThucDongGoi'],
      TenNguonGoc: json['TenNguonGoc'],
      MaCoSoSXKDSanPham: json['MaCoSoSXKDSanPham'],
      TenNhomSPSXKD: json['TenNhomSPSXKD'],
      DanhSachNhomSanPham: json['DanhSachNhomSanPham'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'CoSoSXKDSanPhamID': coSoSXKDSanPhamID,
      'TenSanPham': tenSanPham,
      'LoaiSanPham': loaiSanPham,
      'NgaySX': ngaySX,
      'HanSuDung': hanSuDung,
      'TrangThai': trangThai,
      'DinhKem': dinhKem,
      'TenNguyenLieuChinh': TenNguyenLieuChinh,
      'CachThucDongGoi': CachThucDongGoi,
      'TenNguonGoc': TenNguonGoc,
      'MaCoSoSXKDSanPham': MaCoSoSXKDSanPham,
      'TenNhomSPSXKD': TenNhomSPSXKD,
      'DanhSachNhomSanPham': DanhSachNhomSanPham,
    };
  }
}
