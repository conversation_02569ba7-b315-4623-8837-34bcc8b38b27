class CoSoSXKDQLCL {
  final String? maSo;
  final String? ngayKy;
  final String? ngayHetHan;
  final String? nguoiKy;
  final String? chucVu;
  final String? coQuanBanHanh;
  final DateTime? ngayHetHan1;
  final String? noiDung;
  final String? coSoSXKDQLCLID;
  final String? dinhKem;

  CoSoSXKDQLCL({
    this.maSo,
    this.ngayKy,
    this.ngayHetHan,
    this.nguoiKy,
    this.chucVu,
    this.coQuanBanHanh,
    this.ngayHetHan1,
    this.noiDung,
    this.coSoSXKDQLCLID,
    this.dinhKem,
  });

  // <PERSON>àm từ JSON sang model
  factory CoSoSXKDQLCL.fromJson(Map<String, dynamic> json) {
    return CoSoSXKDQLCL(
      maSo: json['MaSo'] as String?,
      ngayKy: json['NgayKy'] as String?,
      ngayHetHan: json['NgayHetHan'] as String?,
      nguoiKy: json['NguoiKy'] as String?,
      chucVu: json['ChucVu'] as String?,
      coQuanBanHanh: json['CoQuanBanHanh'] as String?,
      ngayHetHan1: json['NgayHetHan1'] != null
          ? DateTime.parse(json['NgayHetHan1'] as String)
          : null,
      noiDung: json['NoiDung'] as String?,
      coSoSXKDQLCLID: json['CoSoSXKDQLCLID'] as String?,
      dinhKem: json['DinhKem'] as String?,
    );
  }

  // Hàm từ model sang JSON
  Map<String, dynamic> toJson() {
    return {
      'MaSo': maSo,
      'NgayKy': ngayKy,
      'NgayHetHan': ngayHetHan,
      'NguoiKy': nguoiKy,
      'ChucVu': chucVu,
      'CoQuanBanHanh': coQuanBanHanh,
      'NgayHetHan1': ngayHetHan1?.toIso8601String(),
      'NoiDung': noiDung,
      'CoSoSXKDQLCLID': coSoSXKDQLCLID,
      'DinhKem': dinhKem,
    };
  }
}
