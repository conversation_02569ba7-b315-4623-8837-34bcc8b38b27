class GiayChungNhanATTP {
  final String capGCNID;
  final String coSoSXKDID;
  final String mauGCNID;
  final String soGCN;
  final String ngayCap;
  final String nguoiCap;
  final String chucVu;
  final String coQuanCap;
  final String ngayHetHan;
  final String tinhTrang;

  GiayChungNhanATTP({
    required this.capGCNID,
    required this.coSoSXKDID,
    required this.mauGCNID,
    required this.soGCN,
    required this.ngayCap,
    required this.nguoiCap,
    required this.chucVu,
    required this.coQuanCap,
    required this.ngayHetHan,
    required this.tinhTrang,
  });

  // From JSON
  factory GiayChungNhanATTP.fromJson(Map<String, dynamic> json) {
    return GiayChungNhanATTP(
      capGCNID: json['CapGCNID'] ?? '',
      coSoSXKDID: json['CoSoSXKDID'] ?? '',
      mauGCNID: json['MauGCNID'] ?? '',
      soGCN: json['SoGCN'] ?? '',
      ngayCap: json['NgayCap'] ?? '',
      nguoiCap: json['NguoiCap'] ?? '',
      chucVu: json['ChucVu'] ?? '',
      coQuanCap: json['CoQuanCap'] ?? '',
      ngayHetHan: json['NgayHetHan'] ?? '',
      tinhTrang: json['TinhTrang'] ?? '',
    );
  }
}
