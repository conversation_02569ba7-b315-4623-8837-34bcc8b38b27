import 'package:get/get.dart';
import 'package:attp_2024/core/helper/api_helper.dart';

const String defaultImage = "https://via.placeholder.com/150";

class CoSoDuDieuKien {
  final String? soGCN;
  final String? ngayCap;
  final String? nguoiCap;
  final String? chucVu;
  final String? coQuanCap;
  final String? ngayHetHan;
  final String? dinhKem;
  final String? soHoSo;
  final String? ngayNopHoSo;
  final String? chucVuNguoiXuLy;
  final String? nguoiXuLy;
  final String? tenCoSoSXKD;
  final String? maCoSoSXKD;
  final String? soGPKD;
  final String? ngayCapGPKD;
  final String? coQuanCapGPKD;
  final String? soDienThoai;
  final String? email;
  final String? diaChiCS;
  final String? tenLoaiHinhCoSo;
  final String? hoVaTenDaiDien;
  final String? chucVuDaiDien;
  final String? soCMND;
  final String? imageUrl;
  final String? trangThaiBH;
  final String? colorTrangThaiBH;
  final String? trangThai_GCN;
  final String? colorXepLoai;
  final String? trangThai_XepLoai;
  final String? trangThaiHD;
  final String? colorHD;
  final String? colorGCN;
  final String? tenCoSo;
  final String? loGo;
  final String? coSoSXKDID;
  final int? totalResults;

  CoSoDuDieuKien(
      {this.soGCN,
      this.ngayCap,
      this.nguoiCap,
      this.chucVu,
      this.coQuanCap,
      this.ngayHetHan,
      this.dinhKem,
      this.soHoSo,
      this.ngayNopHoSo,
      this.chucVuNguoiXuLy,
      this.nguoiXuLy,
      this.tenCoSoSXKD,
      this.maCoSoSXKD,
      this.soGPKD,
      this.ngayCapGPKD,
      this.coQuanCapGPKD,
      this.soDienThoai,
      this.email,
      this.diaChiCS,
      this.tenLoaiHinhCoSo,
      this.hoVaTenDaiDien,
      this.chucVuDaiDien,
      this.soCMND,
      this.imageUrl = defaultImage,
      this.trangThaiBH,
      this.colorTrangThaiBH,
      this.trangThai_GCN,
      this.colorXepLoai = '#000000',
      this.trangThaiHD,
      this.colorHD,
      this.colorGCN,
      this.tenCoSo,
      this.loGo = defaultImage,
      this.coSoSXKDID,
      this.trangThai_XepLoai,
      this.totalResults});

  factory CoSoDuDieuKien.fromJson(Map<String, dynamic> json) {
    String normalizeColor(String? color) {
      return color?.replaceAll("#", "") ?? "#D63939";
    }

    return CoSoDuDieuKien(
        soGCN: json['SoGCN'],
        ngayCap: json['NgayCap'],
        nguoiCap: json['NguoiCap'],
        chucVu: json['ChucVu'],
        coQuanCap: json['CoQuan'],
        ngayHetHan: json['NgayHetHan'],
        dinhKem: json['DinhKem'],
        soHoSo: json['SoHoSo'],
        ngayNopHoSo: json['NgayNop'],
        chucVuNguoiXuLy: json['ChucVu_NguoiXuLy'],
        nguoiXuLy: json['NguoiXuLy'],
        tenCoSoSXKD: json['TenCoSoSXKD'],
        tenCoSo: json['TenCoSo'],
        maCoSoSXKD: json['MaCoSoSXKD'],
        soGPKD: json['SoGPKD'],
        ngayCapGPKD: json['NgayCapGPKD'],
        coQuanCapGPKD: json['CoQuanCapGPKD'],
        soDienThoai: json['SoDienThoai'],
        email: json['EmailCS'],
        diaChiCS: json['DiaChiCS'],
        tenLoaiHinhCoSo: json['LoaiHinhCoSo'],
        hoVaTenDaiDien: json['HoVaTen'],
        chucVuDaiDien: json['ChucVu_DaiDien'],
        soCMND: json['SoCMND'],
        imageUrl: json['ImageUrl'] ?? defaultImage,
        trangThaiBH: json['TrangThaiBH'],
        colorTrangThaiBH: (json['Color_TrangThaiBH']),
        // colorTrangThaiBH: (normalizeColor(json['Color_TrangThaiBH'])),
        trangThai_GCN: json['TrangThai_GCN'],
        colorXepLoai: (json['ColorXepLoai']),
        trangThai_XepLoai: json['TrangThai_XepLoai'],
        totalResults: json['TotalResults'],
        colorHD: (json['ColorHD']),
        colorGCN: (json['ColorGCN']),
        trangThaiHD: json['TrangThaiHD'] ?? '',
        coSoSXKDID: json['CoSoSXKDID'],
        loGo: json['LoGo'].replaceAll("*", "") ?? defaultImage);
  }

  Map<String, dynamic> toJson() {
    return {
      'SoGCN': soGCN,
      'NgayCap': ngayCap,
      'NguoiCap': nguoiCap,
      'ChucVu': chucVu,
      'CoQuan': coQuanCap,
      'NgayHetHan': ngayHetHan,
      'DinhKem': dinhKem,
      'SoHoSo': soHoSo,
      'NgayNop': ngayNopHoSo,
      'ChucVu_NguoiXuLy': chucVuNguoiXuLy,
      'NguoiXuLy': nguoiXuLy,
      'TenCoSoSXKD': tenCoSoSXKD,
      'TenCoSo': tenCoSo,
      'MaCoSoSXKD': maCoSoSXKD,
      'SoGPKD': soGPKD,
      'NgayCapGPKD': ngayCapGPKD,
      'CoQuanCapGPKD': coQuanCapGPKD,
      'SoDienThoai': soDienThoai,
      'EmailCS': email,
      'DiaChiCS': diaChiCS,
      'TenLoaiHinhCoSo': tenLoaiHinhCoSo,
      'HoVaTen_DaiDien': hoVaTenDaiDien,
      'ChucVu_DaiDien': chucVuDaiDien,
      'SoCMND': soCMND,
      'ImageUrl': imageUrl,
      'TrangThaiBH': trangThaiBH,
      'Color_TrangThaiBH': colorTrangThaiBH,
      'TrangThai_GCN': trangThai_GCN,
      'ColorXepLoai': colorXepLoai,
      'TrangThai_XepLoai': trangThai_XepLoai,
      'TrangThaiHD': trangThaiHD,
      'Color_TrangThaiHD': colorHD,
      'ColorGCN': colorGCN,
      'LoGo': loGo,
      'CoSoSXKDID': coSoSXKDID,
      'TotalResults': totalResults,
    };
  }
}
