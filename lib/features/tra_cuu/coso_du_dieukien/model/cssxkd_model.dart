
import 'package:intl/intl.dart';

class CSSXKD {
  final String? lblSoGiayPhepDKKDTTC;
  final DateTime? lblNgayCapDKKDTTC;
  final String? lblCoQuanCapDKKDTTC;
  final String? lblSoDienThoaiTTC;
  final String? lblSoFaxTTC;
  final String? lblEmailTTC;
  final String? lblWebsiteTTC;
  final String? lblTinhTTC;
  final String? lblHuyenTTC;
  final String? lblXaTTC;
  final String? lblThonTTC;
  final String? lblDiaChiTTC;
  final String? lblLoaiHinhCoSoTTC;
  final String? lblNamHoatDongTTC;
  final String? lblTrangThaiHDTTC;
  final String? lblMauSacTTC;
  final String? lblHoVaTenNDD;
  final String? lblChucVuNDD;
  final String? lblGioiTinhNDD;
  final String? lblCCCDNDD;
  final DateTime? lblNgayCapNDD;
  final String? lblNoiCapNDD;
  final String? lblDiaChiNDD;
  final String? lblDanTocNDD;
  final String? lblQuocTichNDD;
  final String? lblSoDienThoaiNDD;
  final String? lblEmailNDD;
  final String? imgHinhAnhNDD;
  final String? imgLoGoNDD;
  final String? toaDoX;
  final String? toaDoY;
  final List<String>? matHangSXID;
  final String? matHangSX;
  final String? lblMaCoSo_TTC;
  final String? lblTenCoSo_TTC;
  final DateTime? lblCCCD_NDD;
  final String? lblNoiCap_NDD;

  CSSXKD({
    this.lblSoGiayPhepDKKDTTC,
    this.lblNgayCapDKKDTTC,
    this.lblCoQuanCapDKKDTTC,
    this.lblSoDienThoaiTTC,
    this.lblSoFaxTTC,
    this.lblEmailTTC,
    this.lblWebsiteTTC,
    this.lblTinhTTC,
    this.lblHuyenTTC,
    this.lblXaTTC,
    this.lblThonTTC,
    this.lblDiaChiTTC,
    this.lblLoaiHinhCoSoTTC,
    this.lblNamHoatDongTTC,
    this.lblTrangThaiHDTTC,
    this.lblMauSacTTC,
    this.lblHoVaTenNDD,
    this.lblChucVuNDD,
    this.lblGioiTinhNDD,
    this.lblCCCDNDD,
    this.lblNgayCapNDD,
    this.lblNoiCapNDD,
    this.lblDiaChiNDD,
    this.lblDanTocNDD,
    this.lblQuocTichNDD,
    this.lblSoDienThoaiNDD,
    this.lblEmailNDD,
    this.imgHinhAnhNDD,
    this.imgLoGoNDD,
    this.toaDoX,
    this.toaDoY,
    this.matHangSXID,
    this.matHangSX,
    this.lblMaCoSo_TTC,
    this.lblTenCoSo_TTC,
    this.lblCCCD_NDD,
    this.lblNoiCap_NDD,
  });

  // Parsing from JSON
  factory CSSXKD.fromJson(Map<String, dynamic> json) {
    DateTime? parseDate(String? dateString) {
      if (dateString == null || dateString.isEmpty) return null;
      try {
        return DateFormat('dd/MM/yyyy').parse(dateString);
      } catch (e) {
        return null; // Return null if parsing fails
      }
    }

    List<String>? parseMatHangSXID(String? idString) {
      if (idString == null || idString.isEmpty) return null;
      try {
        return List<String>.from(
          idString.replaceAll('[', '').replaceAll(']', '').split(',').map(
                (e) => e.trim().replaceAll('"', ''),
              ),
        );
      } catch (e) {
        return null; // Return null if parsing fails
      }
    }

    return CSSXKD(
      lblSoGiayPhepDKKDTTC: json['lblSoGiayPhepDKKD_TTC'] as String?,
      lblNgayCapDKKDTTC: parseDate(json['lblNgayCapDKKD_TTC'] as String?),
      lblCoQuanCapDKKDTTC: json['lblCoQuanCapDKKD_TTC'] as String?,
      lblSoDienThoaiTTC: json['lblSoDienThoai_TTC'] as String?,
      lblSoFaxTTC: json['lblSoFax_TTC'] as String?,
      lblEmailTTC: json['lblEmail_TTC'] as String?,
      lblWebsiteTTC: json['lblWebsite_TTC'] as String?,
      lblTinhTTC: json['lblTinh_TTC'] as String?,
      lblHuyenTTC: json['lblHuyen_TTC'] as String?,
      lblXaTTC: json['lblXa_TTC'] as String?,
      lblThonTTC: json['lblThon_TTC'] as String?,
      lblDiaChiTTC: json['lblDiaChi_TTC'] as String?,
      lblLoaiHinhCoSoTTC: json['lblLoaiHinhCoSo_TTC'] as String?,
      lblNamHoatDongTTC: json['lblNamHoatDong_TTC'] as String?,
      lblTrangThaiHDTTC: json['lblTrangThaiHD_TTC'] as String?,
      lblMauSacTTC: json['lblMauSac_TTC'] as String?,
      lblHoVaTenNDD: json['lblHoVaTen_NDD'] as String?,
      lblChucVuNDD: json['lblChucVu_NDD'] as String?,
      lblGioiTinhNDD: json['lblGioiTinh_NDD'] as String?,
      lblCCCDNDD: json['lblCCCD_NDD'] as String?,
      lblNgayCapNDD: parseDate(json['lblNgayCap_NDD'] as String?),
      lblNoiCapNDD: json['lblNoiCap_NDD'] as String?,
      lblDiaChiNDD: json['lblDiaChi_NDD'] as String?,
      lblDanTocNDD: json['lblDanToc_NDD'] as String?,
      lblQuocTichNDD: json['lblQuocTich_NDD'] as String?,
      lblSoDienThoaiNDD: json['lblSoDienThoai_NDD'] as String?,
      lblEmailNDD: json['lblEmail_NDD'] as String?,
      imgHinhAnhNDD: json['imgHinhAnh_NDD'] as String?,
      imgLoGoNDD: json['imgLoGo_NDD'] as String?,
      toaDoX: json['ToaDoX'] as String?,
      toaDoY: json['ToaDoY'] as String?,
      matHangSXID: parseMatHangSXID(json['MatHangSXID'] as String?),
      matHangSX: json['MatHangSX'] as String?,
      lblMaCoSo_TTC: json['lblMaCoSo_TTC'] as String?,
      lblTenCoSo_TTC: json['lblTenCoSo_TTC'] as String?,
      lblCCCD_NDD: parseDate(json['lblCCCD_NDD'] as String?),
      lblNoiCap_NDD: json['lblNoiCap_NDD'] as String?,
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    String? formatDate(DateTime? date) {
      if (date == null) return null;
      return DateFormat('dd/MM/yyyy').format(date);
    }

    String? formatMatHangSXID(List<String>? ids) {
      if (ids == null || ids.isEmpty) return null;
      return '[${ids.map((e) => '"$e"').join(', ')}]';
    }

    return {
      'lblSoGiayPhepDKKD_TTC': lblSoGiayPhepDKKDTTC,
      'lblNgayCapDKKD_TTC': formatDate(lblNgayCapDKKDTTC),
      'lblCoQuanCapDKKD_TTC': lblCoQuanCapDKKDTTC,
      'lblSoDienThoai_TTC': lblSoDienThoaiTTC,
      'lblSoFax_TTC': lblSoFaxTTC,
      'lblEmail_TTC': lblEmailTTC,
      'lblWebsite_TTC': lblWebsiteTTC,
      'lblTinh_TTC': lblTinhTTC,
      'lblHuyen_TTC': lblHuyenTTC,
      'lblXa_TTC': lblXaTTC,
      'lblThon_TTC': lblThonTTC,
      'lblDiaChi_TTC': lblDiaChiTTC,
      'lblLoaiHinhCoSo_TTC': lblLoaiHinhCoSoTTC,
      'lblNamHoatDong_TTC': lblNamHoatDongTTC,
      'lblTrangThaiHD_TTC': lblTrangThaiHDTTC,
      'lblMauSac_TTC': lblMauSacTTC,
      'lblHoVaTen_NDD': lblHoVaTenNDD,
      'lblChucVu_NDD': lblChucVuNDD,
      'lblGioiTinh_NDD': lblGioiTinhNDD,
      'lblCCCD_NDD': lblCCCDNDD,
      'lblNgayCap_NDD': formatDate(lblNgayCapNDD),
      'lblNoiCap_NDD': lblNoiCapNDD,
      'lblDiaChi_NDD': lblDiaChiNDD,
      'lblDanToc_NDD': lblDanTocNDD,
      'lblQuocTich_NDD': lblQuocTichNDD,
      'lblSoDienThoai_NDD': lblSoDienThoaiNDD,
      'lblEmail_NDD': lblEmailNDD,
      'imgHinhAnh_NDD': imgHinhAnhNDD,
      'imgLoGo_NDD': imgLoGoNDD,
      'ToaDoX': toaDoX,
      'ToaDoY': toaDoY,
      'MatHangSXID': formatMatHangSXID(matHangSXID),
      'MatHangSX': matHangSX,
      'lblMaCoSo_TTC': lblMaCoSo_TTC,
      'lblTenCoSo_TTC': lblTenCoSo_TTC,
      // 'lblNgayCap_NDD': formatDate(lblNgayCap_NDD),
      'lblNoiCap_NDD': lblNoiCap_NDD,
    };
  }
}
