import 'package:get/get.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/presentation/controller/coso_dudk_detail_controller.dart';

import '../../../../core/data/api/configs/dio_configs.dart';
import '../../../../core/data/api/services/proc/proc_service.dart';

class CoSoDuDieuKienDetailBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<CoSoDuDieuKienDetailController>(
      () => CoSoDuDieuKienDetailController(),
    );
    Get.lazyPut(() => ProcService(Get.find<DioService>()));
  }
}
