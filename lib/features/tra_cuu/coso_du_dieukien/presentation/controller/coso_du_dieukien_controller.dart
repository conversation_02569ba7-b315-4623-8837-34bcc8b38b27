// ignore_for_file: constant_identifier_names

import 'package:attp_2024/core/data/models/user_access_model.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:flutter/widgets.dart';
import 'package:attp_2024/core/configs/contanst/proc_constants.dart';
import 'package:attp_2024/core/data/api/services/proc/proc_service.dart';
import 'package:attp_2024/core/services/user_use_case.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/co_so_du_dieu_kien_model.dart';

class CoSoDuDieuKienController extends GetxController {
  final ProcService _procService = Get.find<ProcService>();
  UserAccessModel? userAccessModel;
  var isLoading = false.obs;
  var searchResults = <CoSoDuDieuKien>[].obs;
  var filteredResults = <CoSoDuDieuKien>[].obs;
  var searchQuery = "".obs;
  final log = Logger();

  var province = ''.obs;
  var district = ''.obs;
  var commune = ''.obs;
  var village = ''.obs;

  var provinces = [].obs;
  var districts = [].obs;
  var communes = [].obs;
  var villages = [].obs;
  var loaiHinhCSs = [].obs;
  var tinhTrangHDs = [].obs;
  var trangThaiXls = [].obs;
  var tinhTrangCapGCN = [].obs;

  var provinceFilter = ''.obs;
  var districtFilter = ''.obs;
  var communeFilter = ''.obs;
  var villageFilter = ''.obs;
  var oeFilter = ''.obs;
  var ftFilter = ''.obs;

  var loaihinhCsId = ''.obs;
  var tinhtrangCapGCNId = ''.obs;
  var trangthaiHDId = ''.obs;
  var ketquaXlId = ''.obs;

  var txtSearchFilter = ''.obs;

  var isLoadingProvinces = false.obs;
  var isLoadingDistricts = false.obs;
  var isLoadingCommunes = false.obs;
  var isLoadingVillages = false.obs;
  var isLoadingTraCuu = false.obs;
  var isLoadingLoaiHinhCS = false.obs;
  var isLoadingTinhTrangHD = false.obs;
  var isTrangThaiXepLoai = false.obs;
  var isTinhTrangCapGCN = false.obs;

  var donViId = '';
  var userId = '';
  var userGroup = ''.obs;

  var isLoadingdefaultTinhHuyenXa = false.obs;
  var defaultTinhHuyenXa = [].obs;

  // Thêm biến cho phân trang
  var currentPage = 1.obs;
  var pageSize = 10.obs;
  var hasMoreData = true.obs;
  var isLoadingMore = false.obs;
  final scrollController = ScrollController();

  @override
  Future<void> onInit() async {
    super.onInit();
    fetchAllProvinces();
    await loadInfoProfile();
    await fetchDonViByID();
    fetchAllLoaiHinhCS();
    fetchAllTinhTrangHD(type: 'HoatDong');
    fetchSearchResults("");
    fetchAllTrangThaiXl();
    fetchAllTinhTrangCapGCN();
    scrollController.addListener(_scrollListener);
  }

  @override
  void onClose() {
    scrollController.dispose();
    super.onClose();
  }

  void _scrollListener() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent) {
      if (!isLoadingMore.value && hasMoreData.value) {
        loadMoreData();
      }
    }
  }

  Future<void> loadMoreData() async {
    if (isLoadingMore.value) return;
    isLoadingMore.value = true;

    try {
      currentPage.value++;
      final response = await _procService.callProc(
          ProcConstants.getAllCoSoSXKD, _buildRequestParams(""));

      if (response.isEmpty) {
        hasMoreData.value = false;
      } else {
        final newItems = response
            .map<CoSoDuDieuKien>(
                (item) => CoSoDuDieuKien.fromJson(item as Map<String, dynamic>))
            .toList();

        // Check for duplicates before adding new items
        final existingIds =
            searchResults.map((item) => item.coSoSXKDID).toSet();
        final uniqueNewItems = newItems
            .where((item) => !existingIds.contains(item.coSoSXKDID))
            .toList();

        if (uniqueNewItems.isEmpty) {
          hasMoreData.value = false;
        } else {
          searchResults.addAll(uniqueNewItems);
          filteredResults.addAll(uniqueNewItems);
        }
      }
    } catch (e) {
      log.e("Error loading more data: $e");
      currentPage.value--; // Revert page increment on error
    } finally {
      isLoadingMore.value = false;
    }
  }

  Future<void> refreshList() async {
    currentPage.value = 1;
    hasMoreData.value = true;
    searchResults.clear();
    filteredResults.clear();
    await fetchSearchResults("");
  }

  Future<void> loadInfoProfile() async {
    userAccessModel = await UserUseCase.getUser();
    donViId = userAccessModel?.donViID ?? '';
    userId = userAccessModel?.userID ?? '';
    userGroup.value = userAccessModel?.userGroupCode ?? '';
    log.i(userGroup);
    // var test = userAccessModel?.siteURL;
    // print(test);
  }

  //  Future<void> loadInfoProfile() async {
  //   userAccessModel = await UserUseCase.getUser();
  //   logger.t(userAccessModel?.token.toString());
  //   update(["bodyID"]);
  // }

  void clearProvinceFilters() {
    provinceFilter.value = '';
    districtFilter.value = '';
    communeFilter.value = '';
    villageFilter.value = '';
    districts.clear();
    communes.clear();
    villages.clear();
  }

  void clearDistrictFilters() {
    districtFilter.value = '';
    communeFilter.value = '';
    villageFilter.value = '';
    communes.clear();
    villages.clear();
  }

  void clearCommuneFilters() {
    communeFilter.value = '';
    villageFilter.value = '';
    villages.clear();
  }

  Future<void> fetchAllLoaiHinhCS() async {
    isLoadingLoaiHinhCS.value = true;
    final List<Map<String, dynamic>> body = [];
    try {
      List<dynamic> response = await _procService.callProc(
          ProcConstants.getAllComboLoaiHinhCS, body);
      List<Map<String, dynamic>> mappedResponse =
          response.cast<Map<String, dynamic>>();
      loaiHinhCSs.assignAll(
        mappedResponse.map((Map<String, dynamic> loaiHinhCS) {
          return {
            "value": loaiHinhCS['LoaiHinhCoSoID'],
            "display": loaiHinhCS['TenLoaiHinhCoSo'],
          };
        }).toList(),
      );
    } finally {
      isLoadingLoaiHinhCS.value = false;
    }
  }

  Future<void> fetchAllProvinces() async {
    isLoadingProvinces.value = true;
    final List<Map<String, dynamic>> body = [];
    try {
      List<dynamic> provincesResponse =
          await _procService.callProc(ProcConstants.getAllDiaBanHCTinh, body);
      List<Map<String, dynamic>> mappedTinhResponse =
          provincesResponse.cast<Map<String, dynamic>>();

      provinces.assignAll(
        mappedTinhResponse.map((Map<String, dynamic> tinhs) {
          return {
            "value": tinhs['DiaBanHCID'],
            "display": tinhs['TenDiaBan'],
          };
        }).toList(),
      );
    } finally {
      isLoadingProvinces.value = false;
    }
  }

  Future<void> fetchAllDistricts({required String tinhID}) async {
    isLoadingDistricts.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "TinhID", "type": "guid", "value": tinhID},
    ];
    try {
      List<dynamic> districtsResponse =
          await _procService.callProc(ProcConstants.getAllDiaBanHCHuyen, body);
      List<Map<String, dynamic>> mappedHuyenResponse =
          districtsResponse.cast<Map<String, dynamic>>();

      districts.assignAll(
        mappedHuyenResponse.map((Map<String, dynamic> huyens) {
          return {
            "value": huyens['DiaBanHCID'],
            "display": huyens['TenDiaBan'],
          };
        }).toList(),
      );
    } finally {
      isLoadingDistricts.value = false;
    }
  }

  Future<void> fetchAllCommunes({required String huyenID}) async {
    isLoadingCommunes.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "HuyenID", "type": "guid", "value": huyenID},
    ];
    try {
      List<dynamic> communesResponse =
          await _procService.callProc(ProcConstants.getAllDiaBanHCXa, body);
      List<Map<String, dynamic>> mappedXaResponse =
          communesResponse.cast<Map<String, dynamic>>();

      communes.assignAll(
        mappedXaResponse.map((Map<String, dynamic> xas) {
          return {
            "value": xas['DiaBanHCID'],
            "display": xas['TenDiaBan'],
          };
        }).toList(),
      );
    } finally {
      isLoadingCommunes.value = false;
    }
  }

  Future<void> fetchAllVillages({required String xaID}) async {
    isLoadingVillages.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "xaID", "type": "guid", "value": xaID},
    ];
    try {
      List<dynamic> villagesResponse =
          await _procService.callProc(ProcConstants.getAllDiaBanHCThon, body);
      List<Map<String, dynamic>> mappedThonResponse =
          villagesResponse.cast<Map<String, dynamic>>();

      villages.assignAll(
        mappedThonResponse.map((Map<String, dynamic> thons) {
          return {
            "value": thons['DiaBanHCID'],
            "display": thons['TenDiaBan'],
          };
        }).toList(),
      );
    } finally {
      isLoadingVillages.value = false;
    }
  }

  // Future<void> fetchAllDefaultTinhHuyenXa({required String donViID}) async {
  //   isLoadingdefaultTinhHuyenXa.value = true;
  //   final List<Map<String, dynamic>> body = [
  //     {"name": "DonViID", "type": "guid", "value": donViID}
  //   ];
  //   try {
  //     List<dynamic> res = await _procService.callProc(
  //         'Proc_Mobile_Get_ThietLapHeThong_ByDonViID', body);
  //     List<Map<String, dynamic>> mappedResponse =
  //         res.cast<Map<String, dynamic>>();
  //     defaultTinhHuyenXa.assignAll(
  //       mappedResponse.map((Map<String, dynamic> item) {
  //         return {
  //           "DiaBanHCID_Tinh": item['DiaBanHCID_Tinh'],
  //           "DiaBanHCID_Huyen": item['DiaBanHCID_Huyen'],
  //           "DiaBanHCID_Xa": item['DiaBanHCID_Xa'],
  //           "DiaBanHCID_Thon": item['DiaBanHCID_Thon'],
  //         };
  //       }).toList(),
  //     );
  //     log.i(defaultTinhHuyenXa.toString());
  //     setDefaultTinhHuyenXa();
  //   } finally {
  //     isLoadingdefaultTinhHuyenXa.value = false;
  //   }
  // }

  Future<void> fetchDonViByID() async {
    isLoadingdefaultTinhHuyenXa.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "ID", "type": "guid", "value": donViId}
    ];
    try {
      List<dynamic> res =
          await _procService.callProc('Proc_Mobile_GetDonViByID', body);
      List<Map<String, dynamic>> mappedResponse =
          res.cast<Map<String, dynamic>>();
      defaultTinhHuyenXa.assignAll(
        mappedResponse.map((Map<String, dynamic> item) {
          return {
            "TinhID_": item['TinhID_'],
            "HuyenID_": item['HuyenID_'],
            "XaID_": item['XaID_'],
            "ThonID_": item['ThonID_'],
          };
        }).toList(),
      );
      log.i(defaultTinhHuyenXa.toString());
      setDefaultTinhHuyenXa();
    } finally {
      isLoadingdefaultTinhHuyenXa.value = false;
    }
  }

  void setDefaultTinhHuyenXa() {
    fetchAllProvinces();
    fetchAllDistricts(tinhID: defaultTinhHuyenXa.first['TinhID_']);
    fetchAllCommunes(huyenID: defaultTinhHuyenXa.first['HuyenID_']);
    fetchAllVillages(xaID: defaultTinhHuyenXa.first['XaID_']);
    if (defaultTinhHuyenXa.isNotEmpty &&
        defaultTinhHuyenXa.first['TinhID_'] !=
            '00000000-0000-0000-0000-000000000000') {
      provinceFilter.value = defaultTinhHuyenXa.first['TinhID_'];
    }
    if (defaultTinhHuyenXa.isNotEmpty &&
        defaultTinhHuyenXa.first['HuyenID_'] !=
            '00000000-0000-0000-0000-000000000000') {
      districtFilter.value = defaultTinhHuyenXa.first['HuyenID_'];
    }
    if (defaultTinhHuyenXa.isNotEmpty &&
        defaultTinhHuyenXa.first['XaID_'] !=
            '00000000-0000-0000-0000-000000000000') {
      communeFilter.value = defaultTinhHuyenXa.first['XaID_'];
    }
    if (defaultTinhHuyenXa.isNotEmpty &&
        defaultTinhHuyenXa.first['ThonID_'] !=
            '00000000-0000-0000-0000-000000000000') {
      villageFilter.value = defaultTinhHuyenXa.first['ThonID_'];
    }
  }

  Future<void> fetchAllKetQuaXL({required String xaID}) async {
    isLoadingVillages.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "xaID", "type": "guid", "value": xaID},
    ];
    try {
      List<dynamic> villagesResponse =
          await _procService.callProc(ProcConstants.getAllDiaBanHCThon, body);
      List<Map<String, dynamic>> mappedThonResponse =
          villagesResponse.cast<Map<String, dynamic>>();

      villages.assignAll(
        mappedThonResponse.map((Map<String, dynamic> thons) {
          return {
            "value": thons['DiaBanHCID'],
            "display": thons['TenDiaBan'],
          };
        }).toList(),
      );
    } finally {
      isLoadingVillages.value = false;
    }
  }

  Future<void> fetchSearchResults(String query) async {
    currentPage.value = 1;
    hasMoreData.value = true;
    isLoading.value = true;
    // Clear existing data before fetching new data
    searchResults.clear();
    filteredResults.clear();
    try {
      final response = await _procService.callProc(
          ProcConstants.getAllCoSoSXKD, _buildRequestParams(query));
      searchResults.value = response.isNotEmpty
          ? response
              .map<CoSoDuDieuKien>((item) =>
                  CoSoDuDieuKien.fromJson(item as Map<String, dynamic>))
              .toList()
          : [];

      filteredResults.value =
          List.from(searchResults); // Create a new list instead of reference

      if (response.isEmpty || response.length < pageSize.value) {
        hasMoreData.value = false;
      }
    } catch (e) {
      log.e("Error fetching search results: $e");
      searchResults.clear();
      filteredResults.clear();
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> fetchAllTrangThaiXl() async {
    isTrangThaiXepLoai.value = true;
    try {
      List<dynamic> response =
          await _procService.callProc(ProcConstants.getAllTrangThaiXepLoai, []);
      List<Map<String, dynamic>> mappedResponse =
          response.cast<Map<String, dynamic>>();

      trangThaiXls.assignAll(
        mappedResponse.map((Map<String, dynamic> trangthaiXepLoai) {
          return {
            "value": trangthaiXepLoai['MaTrangThai'],
            "display": trangthaiXepLoai['TenTrangThai'],
          };
        }).toList(),
      );

      print('fetchAllTrangThaiXl $trangThaiXls');
    } catch (e) {
      print("ERROR OCCURRED: $e");
    } finally {
      isTrangThaiXepLoai.value = false;
    }
  }

  Future<void> fetchSearchResultsWithFilters() async {
    currentPage.value = 1;
    hasMoreData.value = true;
    isLoading.value = true;

    // Xóa dữ liệu cũ
    searchResults.clear();
    filteredResults.clear();

    try {
      final response = await _procService.callProc(
          ProcConstants.getAllCoSoSXKD, _buildRequestParams(""));

      if (response.isNotEmpty) {
        final items = response
            .map<CoSoDuDieuKien>(
                (item) => CoSoDuDieuKien.fromJson(item as Map<String, dynamic>))
            .toList();

        // Tạo danh sách mới thay vì tham chiếu
        searchResults.value = List.from(items);
        filteredResults.value = List.from(items);

        if (items.length < pageSize.value) {
          hasMoreData.value = false;
        }
      } else {
        hasMoreData.value = false;
        searchResults.value = [];
        filteredResults.value = [];
      }
    } catch (e) {
      log.e("Error fetching search results: $e");
      searchResults.clear();
      filteredResults.clear();
      hasMoreData.value = false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> fetchAllTinhTrangHD({required String type}) async {
    isLoadingTinhTrangHD.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "GroupType", "type": "string", "value": type},
    ];
    try {
      List<dynamic> response =
          await _procService.callProc("Proc_Mobile_Get_TrangThai", body);
      List<Map<String, dynamic>> mappedResponse =
          response.cast<Map<String, dynamic>>();

      tinhTrangHDs.assignAll(
        mappedResponse.map((Map<String, dynamic> tinhTrangHD) {
          return {
            "value": tinhTrangHD['MaTrangThai'],
            "display": tinhTrangHD['TenTrangThai'],
          };
        }).toList(),
      );
    } finally {
      isLoadingTinhTrangHD.value = false;
    }
  }

  Future<void> fetchAllTinhTrangCapGCN() async {
    isTinhTrangCapGCN.value = true;
    try {
      List<dynamic> response =
          await _procService.callProc(ProcConstants.getAllTinhTrangCapGCN, []);
      List<Map<String, dynamic>> mappedResponse =
          response.cast<Map<String, dynamic>>();

      tinhTrangCapGCN.assignAll(
        mappedResponse.map((Map<String, dynamic> tinhTrangCapGCN) {
          return {
            "value": tinhTrangCapGCN['MaTrangThai'],
            "display": tinhTrangCapGCN['TenTrangThai'],
          };
        }).toList(),
      );
    } finally {
      isLoadingTinhTrangHD.value = false;
    }
  }

  List<Map<String, dynamic>> _buildRequestParams(String query) {
    return [
      {
        "Type": "guid",
        "Name": "TinhID",
        "Value": provinceFilter.value.isEmpty
            ? "00000000-0000-0000-0000-000000000000"
            : provinceFilter.value
      },
      {
        "Type": "guid",
        "Name": "HuyenID",
        "Value": districtFilter.value.isEmpty
            ? "00000000-0000-0000-0000-000000000000"
            : districtFilter.value
      },
      {
        "Type": "guid",
        "Name": "XaID",
        "Value": communeFilter.value.isEmpty
            ? "00000000-0000-0000-0000-000000000000"
            : communeFilter.value
      },
      {
        "Type": "guid",
        "Name": "ThonID",
        "Value": villageFilter.value.isEmpty
            ? "00000000-0000-0000-0000-000000000000"
            : villageFilter.value
      },
      {
        "Type": "guid",
        "Name": "LoaiHinhCoSoID",
        "Value": loaihinhCsId.value.isEmpty
            ? "00000000-0000-0000-0000-000000000000"
            : loaihinhCsId.value
      },
      {
        "Type": "string",
        "Name": "TrangThai_GCN",
        "Value":
            tinhtrangCapGCNId.value.isEmpty ? null : tinhtrangCapGCNId.value
      },
      {
        "Type": "string",
        "Name": "TrangThai_HD",
        "Value": trangthaiHDId.isEmpty ? null : trangthaiHDId.value
      },
      {
        "Type": "string",
        "Name": "TrangThai_XepLoai",
        "Value": ketquaXlId.isEmpty ? null : ketquaXlId.value
      },
      {"Type": "string", "Name": "SapXep", "Value": "TenCoSoSXKD"},
      {"Type": "string", "Name": "TuKhoa", "Value": query},
      {"Type": "guid", "Name": "DonViID", "Value": donViId},
      {"Type": "guid", "Name": "UserID", "Value": userId},
      {"Type": "string", "Name": "ThuocLoai", "Value": "CapGCN"},
      {"Type": "int", "Name": "PageNumber", "Value": currentPage.value},
      {"Type": "int", "Name": "PageSize", "Value": pageSize.value}
    ];
  }
}
