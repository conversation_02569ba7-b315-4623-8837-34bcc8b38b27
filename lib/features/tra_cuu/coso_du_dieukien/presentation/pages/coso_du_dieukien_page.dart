import 'package:attp_2024/core/configs/contents/app_content.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/presentation/controller/coso_du_dieukien_controller.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/presentation/widgets/search.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/routes/routes.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:attp_2024/features/tra_cuu/coso_du_dieukien/model/co_so_du_dieu_kien_model.dart';
import 'package:attp_2024/features/tra_cuu/widgets/common_list_card.dart';

// import '../widgets/common_list_card.dart';

class CoSoDuDieuKienPage extends GetView<CoSoDuDieuKienController> {
  const CoSoDuDieuKienPage({super.key});

  Widget buildShimmerEffect() {
    return ListView.builder(
      padding: const EdgeInsets.all(8.0),
      itemCount: 5,
      itemBuilder: (context, index) {
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(5.0),
          ),
          margin: const EdgeInsets.symmetric(vertical: 8.0),
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildShimmerBox(width: 80, height: 80),
                const Gap(10.0),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildShimmerBox(width: double.infinity, height: 16.0),
                      const Gap(8.0),
                      _buildShimmerBox(width: 150.0, height: 16.0),
                      const Gap(8.0),
                      _buildShimmerBox(width: double.infinity, height: 16.0),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildShimmerBox({required double width, required double height}) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.0),
        ),
      ),
    );
  }

  Widget _buildListItem(CoSoDuDieuKien item) {
    return CommonListCard(
      title: LabelValuePair(
          label: "Tên cơ sở", value: item.tenCoSoSXKD.toString()),
      statusText: item.trangThai_XepLoai.toString() != 'null'
          ? item.trangThai_XepLoai.toString()
          : 'Chưa thẩm đi',
      statusColor: item.colorXepLoai != null
          ? Color(int.parse(item.colorXepLoai!.replaceFirst("#", "0xff")))
          : const Color(0xff1e00ff),
      logoUrl: item.loGo != null && item.loGo!.isNotEmpty
          ? "${controller.userAccessModel?.siteURL}${item.loGo}"
          : AppImageString.imageNotFount,
      badgeText: item.trangThaiHD,
      badgeColor: item.colorHD,
      labelValueList: [
        LabelValuePair(
          label: "Người đại diện pháp luật",
          value: item.hoVaTenDaiDien ?? AppContent.textDefault,
        ),
        LabelValuePair(
          label: "Số điện thoại",
          value: item.soDienThoai ?? AppContent.textDefault,
        ),
        LabelValuePair(
          label: "Email",
          value: item.email ?? AppContent.textDefault,
        ),
        LabelValuePair(
          label: "Số GPKD",
          value: item.soGPKD ?? AppContent.textDefault,
        ),
        LabelValuePair(
          label: "Ngày đăng ký GPKD",
          value: item.ngayCapGPKD ?? AppContent.textDefault,
        ),
        LabelValuePair(
          label: "Loại hình cơ sở",
          value: item.tenLoaiHinhCoSo ?? AppContent.textDefault,
        ),
        LabelValuePair(
          label: "Địa chỉ",
          value: item.diaChiCS ?? AppContent.textDefault,
        ),
      ],
      onTap: () {
        Get.toNamed(
          Routes.coSoDuDieuKienDetail,
          arguments: item.coSoSXKDID,
        );
      },
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      alignment: Alignment.center,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 8),
          Text(
            'Đang tải thêm dữ liệu...',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final ScrollController scrollController = ScrollController();
    final ValueNotifier<bool> isVisible = ValueNotifier(true);

    scrollController.addListener(() {
      if (scrollController.position.userScrollDirection ==
          ScrollDirection.reverse) {
        isVisible.value = false;
      } else if (scrollController.position.userScrollDirection ==
          ScrollDirection.forward) {
        isVisible.value = true;
      }
    });

    return Scaffold(
      appBar: const AppBarWidget(
        title: "Tra cứu cơ sở thuộc diện cấp GCN",
        centerTitle: true,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SearchWidget(),
          Expanded(
            child: RefreshIndicator(
              onRefresh: () async {
                controller.fetchSearchResults("");
              },
              child: Obx(() {
                if (controller.isLoading.value) {
                  return buildShimmerEffect();
                }
                if (controller.filteredResults.isEmpty) {
                  return Center(
                    child: Image.asset(
                      AppImageString.iDataNotFound,
                      width: 100,
                      height: 100,
                    ),
                  );
                }
                return ListView.builder(
                  controller: controller.scrollController,
                  padding: const EdgeInsets.all(8.0),
                  itemCount: controller.filteredResults.length +
                      (controller.isLoadingMore.value ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == controller.filteredResults.length) {
                      return _buildLoadingIndicator();
                    }
                    final item = controller.filteredResults[index];
                    return _buildListItem(item);
                  },
                );
              }),
            ),
          ),
        ],
      ),
    );
  }
}
