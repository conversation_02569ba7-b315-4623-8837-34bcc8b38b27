import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import '../../../../../../core/configs/contents/app_content.dart';
import '../../controller/coso_dudk_detail_controller.dart';
import '../common_list_card.dart';

class DanhSachTaiLieuList extends StatelessWidget {
  const DanhSachTaiLieuList({
    super.key,
    required this.controller,
  });

  final CoSoDuDieuKienDetailController controller;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.tailieuList.isEmpty) {
        return SizedBox(
          height: 90.h,
          child: Center(
            child: Image.asset(
              AppImageString.iDataNotFound,
              width: 100,
              height: 100,
            ),
            // child:
            // Text(
            //   "",
            //   style: TextStyle(fontSize: 16, color: AppColors.primary),
            // ),
          ),
        );
      }
      return SizedBox(
        height: 90.h,
        child: ListView.builder(
          physics: const BouncingScrollPhysics(),
          padding: const EdgeInsets.all(8.0),
          itemCount: controller.tailieuList.length,
          itemBuilder: (context, index) {
            final nhanVien = controller.tailieuList[index];
            return CommonListCard(
              showImage: false,
              title: nhanVien.soVanBan ?? AppContent.textDefault,
              labelValueList: [
                LabelValuePair(
                    label: "Ngày ký", value: nhanVien.ngayKy ?? AppContent.textDefault),
                LabelValuePair(
                    label: "Trích yếu",
                    value: nhanVien.trichYeuNoiDung ?? AppContent.textDefault),
                LabelValuePair(
                    label: "Cơ quan ban hành",
                    value: nhanVien.coQuanBanHanh ?? AppContent.textDefault),
                LabelValuePair(
                    label: "Người ký", value: nhanVien.nguoiKy ?? AppContent.textDefault),
                LabelValuePair(
                    label: "Chức vụ", value: nhanVien.chucVu ?? AppContent.textDefault),
                LabelValuePair(
                    label: "Loại văn bản",
                    value: nhanVien.loaiTaiLieu ?? AppContent.textDefault),
              ],

              logoUrl: '',
            );
          },
        ),
      );
    });
  }
}
