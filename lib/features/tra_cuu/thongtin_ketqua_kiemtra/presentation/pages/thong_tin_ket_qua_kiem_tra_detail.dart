import 'dart:developer';

import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/model/thong_tin_ket_qua_kiem_tra_model.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/presentation/controller/thong_tin_ket_qua_kiem_tra_controller.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/presentation/widgets/ket_qua_kiem_tra/danh_gia_viec_thuc_hien_cam_ket.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/presentation/widgets/ket_qua_kiem_tra/ketluankiemtra.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/presentation/widgets/ket_qua_kiem_tra/nhan_xet_va_kien_nghi_cua_doan_kiem_tra.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/presentation/widgets/ket_qua_kiem_tra/thong_tin_chung.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/presentation/widgets/ket_qua_kiem_tra/y_kien_dai_dien_co_so.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/presentation/widgets/ket_qua_tham_dinh/lay_mau_phan_tich.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/presentation/widgets/ket_qua_tham_dinh/nhom_tieu_chi_danh_gia.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/presentation/widgets/ket_qua_tham_dinh/nhom_tieu_chi_khong_danh_gia.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/presentation/widgets/ket_qua_tham_dinh/thong_tin_chung.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/presentation/widgets/ket_qua_tham_dinh/y_kien.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';

class ThongTinKetQuaKiemTraDetailPage
    extends GetView<ThongTinKetQuaKiemTraController> {
  const ThongTinKetQuaKiemTraDetailPage({super.key});
  @override
  Widget build(BuildContext context) {
    final ThongTinKetQuaKiemTraModel item = Get.arguments;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.tabController.animateTo(0);
      controller.pageController.jumpToPage(0);
    });

    log(controller.tieuChiDanhGia.toString(), name: "tieuChiDanhGia");
    log(controller.tieuChiKhongDanhGia.toString(), name: "tieuChi0DanhGia");
    return item.ThuocLoai.toString() == "CapGCN"
        ? Scaffold(
            appBar: const AppBarWidget(
              title: "Thông tin kết quả thẩm định",
              centerTitle: true,
            ),
            body: Column(
              children: [
                Container(
                    color: Colors.white,
                    child: TabBar(
                      isScrollable: true,
                      controller: controller.tabController,
                      tabAlignment: TabAlignment.start,
                      indicatorSize: TabBarIndicatorSize.tab,
                      labelColor: AppColors.primary,
                      unselectedLabelColor: Colors.grey,
                      indicator: BoxDecoration(
                        color: const Color(0xFFEAFFF9),
                        border: Border(
                          bottom:
                              BorderSide(color: AppColors.primary, width: 2),
                        ),
                      ),
                      tabs: const [
                        Tab(text: "Thông tin chung"),
                        Tab(text: "Nhóm tiêu chí đánh giá"),
                        Tab(text: "Nhóm tiêu chí không đánh giá"),
                        Tab(text: "Lấy mẫu phân tích"),
                        Tab(text: "Ý kiến"),
                      ],
                      onTap: (index) {
                        controller.pageController
                            .jumpToPage(index); // This is also not observable
                      },
                    )),
                Expanded(
                  child: PageView(
                    controller: controller.pageController,
                    onPageChanged: (index) {
                      controller.tabController.animateTo(index);
                    },
                    children: [
                      ThongTinChung(item: item),
                      NhomTieuChiDanhGia(),
                      NhomTieuChiKhongDanhGia(),
                      LayMauPhanTich(item: item),
                      YKien(item: item),
                    ],
                  ),
                ),
              ],
            ),
          )
        : Scaffold(
            appBar: const AppBarWidget(
              title: "Thông tin kết quả kiểm tra",
              centerTitle: true,
            ),
            body: Column(
              children: [
                Container(
                    color: Colors.white,
                    child: TabBar(
                      isScrollable: true,
                      controller: controller.tabController,
                      tabAlignment: TabAlignment.start,
                      indicatorSize: TabBarIndicatorSize.tab,
                      labelColor: AppColors.primary,
                      unselectedLabelColor: Colors.grey,
                      indicator: BoxDecoration(
                        color: const Color(0xFFEAFFF9),
                        border: Border(
                          bottom:
                              BorderSide(color: AppColors.primary, width: 2),
                        ),
                      ),
                      tabs: const [
                        Tab(text: "Thông tin chung"),
                        Tab(text: "Đánh giá việc thực hiện cam kết"),
                        Tab(text: "Nhận xét và kiến nghị của đoàn kiểm tra"),
                        Tab(text: "Ý kiến của đại diện cơ sở"),
                        Tab(text: "Kết luận kiểm tra"),
                      ],
                      onTap: (index) {
                        controller.pageController
                            .jumpToPage(index); // This is also not observable
                      },
                    )),
                Expanded(
                  child: PageView(
                    controller: controller.pageController,
                    onPageChanged: (index) {
                      controller.tabController.animateTo(index);
                    },
                    children: [
                      KetQuaThongTinChung(item: item),
                      DanhGiaViecThucHienCamKet(item: item),
                      NhanXetCuaDoanKiemTra(item: item),
                      YKienDaiDienCoSo(item: item),
                      KetLuanKiemTra(item: item),
                    ],
                  ),
                ),
              ],
            ),
          );
  }
}
