import 'package:attp_2024/core/configs/contents/app_content.dart';
import 'package:attp_2024/core/ui/snackbar/snackbar_until.dart';
import 'package:attp_2024/core/ui/widgets/webview/webview_page.dart';
import 'package:attp_2024/core/utils/color_utils.dart';
import 'package:attp_2024/core/utils/convert_text.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/presentation/widgets/row_text.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/presentation/widgets/search.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/routes/routes.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/model/thong_tin_ket_qua_kiem_tra_model.dart';
import '../controller/thong_tin_ket_qua_kiem_tra_controller.dart';

class ThongTinKetQuaKiemTraPage
    extends GetView<ThongTinKetQuaKiemTraController> {
  const ThongTinKetQuaKiemTraPage({super.key});

  Widget buildShimmerEffect() {
    return ListView.builder(
      padding: const EdgeInsets.all(8.0),
      itemCount: 5,
      itemBuilder: (context, index) {
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(5.0),
          ),
          margin: const EdgeInsets.symmetric(vertical: 8.0),
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildShimmerBox(width: 80, height: 80),
                const Gap(10.0),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildShimmerBox(width: double.infinity, height: 16.0),
                      const Gap(8.0),
                      _buildShimmerBox(width: 150.0, height: 16.0),
                      const Gap(8.0),
                      _buildShimmerBox(width: double.infinity, height: 16.0),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildShimmerBox({required double width, required double height}) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.0),
        ),
      ),
    );
  }

  Widget _buildListItem(ThongTinKetQuaKiemTraModel item) {
    return GestureDetector(
      onTap: () async {
        await controller.fetchChiTieuDanhGia(item.BienBanThamDinhATTPID!);
        await controller.fetchChiTieuKhongDanhGia(item.BienBanThamDinhATTPID!);
        Get.toNamed(
          Routes.thongTinketQuaKiemTraDetail,
          arguments: item,
        );
      },
      child: Stack(
        children: [
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.0),
            ),
            margin: const EdgeInsets.symmetric(vertical: 8.0),
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  rowText(
                      title: 'Số biên bản: ',
                      value: item.SoBienBan ?? controller.defaultValue),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      rowText(
                          title: 'Ngày lập: ',
                          value: item.NgayLap ?? controller.defaultValue),
                      rowText(
                          title: 'Ngày KT/TĐ: ',
                          value: item.NgayThamDinh ?? controller.defaultValue),
                    ],
                  ),
                  const SizedBox(height: 4),
                  rowText(
                    title: 'Hình thức KT/TĐ: ',
                    value: item.HinhThucThamDinh ?? controller.defaultValue,
                  ),
                  Row(
                    children: [
                      Text('Đính kèm: ', style: TextStyle(color: Colors.black)),
                      GestureDetector(
                        onTap: () {
                          if (item.DinhKem == null || item.DinhKem!.isEmpty) {
                            SnackbarUtil.showWarning("Không có đính kèm !",
                                alignment: 'bottom');
                          } else {
                            Get.to(() => WebViewPage(
                                title: AppContent.appTitleWebview,
                                initialUrl:
                                    convertAttachItemStringToListWithoutSwungDash(
                                        '${controller.userAccessModel?.siteURL}',
                                        item.DinhKem!)[0]));
                          }
                        },
                        child: Text(
                          '\u{1F4CE} Xem đính kèm',
                          style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: AppColors.primary),
                        ),
                      ),
                    ],
                  ),
                  const Divider(color: AppColors.ConHieuLucTren6Thang),
                  const SizedBox(height: 4),
                  rowText(
                      title: 'Cơ sở: ',
                      value: item.CoSoThamDinh ?? controller.defaultValue),
                  rowText(
                      title: 'Loại hình: ',
                      value: item.LoaiHinhCoSo ?? controller.defaultValue),
                  const SizedBox(height: 4),
                  rowText(
                      title: 'Địa chỉ: ',
                      value: item.DiaChi ?? controller.defaultValue),
                ],
              ),
            ),
          ),
          Positioned(
            top: 8,
            right: 0,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: hexToColor(item.MauSac),
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(4),
                  bottomLeft: Radius.circular(4),
                ),
              ),
              child: Text(
                item.XepLoai ?? 'Đảm bảo ATTP',
                style:
                    TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final ScrollController scrollController = ScrollController();
    final ValueNotifier<bool> isVisible = ValueNotifier(true);

    scrollController.addListener(() {
      if (scrollController.position.userScrollDirection ==
          ScrollDirection.reverse) {
        isVisible.value = false;
      } else if (scrollController.position.userScrollDirection ==
          ScrollDirection.forward) {
        isVisible.value = true;
      }

      // Thêm logic cho infinity scroll
      if (scrollController.position.pixels >=
          scrollController.position.maxScrollExtent - 200) {
        controller.loadMore(controller.searchQuery.value);
      }
    });

    return Scaffold(
      appBar: const AppBarWidget(
        title: "Tra cứu thông tin kết quả kiểm tra",
        centerTitle: true,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SearchWidget(),
          Expanded(
            child: RefreshIndicator(
              onRefresh: () async {
                controller.fetchSearchResults("");
              },
              child: Obx(() {
                if (controller.isLoading.value) {
                  return const ShimmerLoading();
                }
                if (controller.filteredResults.isEmpty) {
                  return Center(
                    child: Image.asset(
                      AppImageString.iDataNotFound,
                      width: 100,
                      height: 100,
                    ),
                  );
                }
                return ListView.builder(
                  controller: scrollController,
                  padding: const EdgeInsets.all(8.0),
                  itemCount: controller.filteredResults.length +
                      1, // +1 cho loading indicator
                  itemBuilder: (context, index) {
                    if (index == controller.filteredResults.length) {
                      return Obx(() {
                        if (controller.isLoadingMore.value) {
                          return const Padding(
                            padding: EdgeInsets.all(8.0),
                            child: Center(
                              child: CircularProgressIndicator(),
                            ),
                          );
                        }
                        return const SizedBox.shrink();
                      });
                    }
                    final item = controller.filteredResults[index];
                    return _buildListItem(item);
                  },
                );
              }),
            ),
          ),
        ],
      ),
    );
  }
}

class ShimmerLoading extends StatelessWidget {
  const ShimmerLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.all(8.0),
      itemCount: 5,
      itemBuilder: (context, index) {
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(5.0),
          ),
          margin: const EdgeInsets.symmetric(vertical: 8.0),
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildShimmerBox(width: 80, height: 80),
                const Gap(10.0),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildShimmerBox(width: double.infinity, height: 16.0),
                      const Gap(8.0),
                      _buildShimmerBox(width: 150.0, height: 16.0),
                      const Gap(8.0),
                      _buildShimmerBox(width: double.infinity, height: 16.0),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildShimmerBox({required double width, required double height}) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.0),
        ),
      ),
    );
  }
}
