import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/customCachedImage/customCachedImage.dart';
import 'package:attp_2024/core/utils/color_utils.dart';
import 'package:attp_2024/features/tra_cuu/widgets/label_value_row.dart';

class CommonListCard extends StatelessWidget {
  final String? title;
  final IconData? titleIcon;
  final String? logoUrl;
  final List<LabelValuePair> labelValueList;
  final String? badgeText;
  final String? badgeColor;
  final String? secondaryBadgeText;
  final String? secondaryBadgeColor;
  final VoidCallback? onTap;
  final double? height;
  final double imageWidth;
  final double imageHeight;
  final bool showImage;

  const CommonListCard({
    super.key,
    required this.title,
    this.titleIcon,
    required this.logoUrl,
    required this.labelValueList,
    this.badgeText,
    this.badgeColor,
    this.secondaryBadgeText,
    this.secondaryBadgeColor,
    this.onTap,
    this.height,
    this.imageWidth = 100,
    this.imageHeight = 130,
    this.showImage = true,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(5.0),
        ),
        margin: const EdgeInsets.symmetric(vertical: 8.0),
        child: SizedBox(
          height: height,
          child: Stack(
            children: [
              if (badgeText != null && badgeColor != null)
                _buildBadge(
                  text: badgeText!,
                  color: badgeColor!,
                  alignment: Alignment.topRight,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(5.0),
                    topRight: Radius.circular(5.0),
                  ),
                ),
              if (secondaryBadgeText != null && secondaryBadgeColor != null)
                _buildBadge(
                  text: secondaryBadgeText!,
                  color: secondaryBadgeColor!,
                  alignment: Alignment.bottomLeft,
                  borderRadius: const BorderRadius.only(
                    bottomRight: Radius.circular(5.0),
                    topLeft: Radius.circular(5.0),
                  ),
                ),
              Padding(
                padding: EdgeInsets.only(
                  top:
                      badgeText != null || secondaryBadgeText != null ? 30 : 10,
                  bottom: 5,
                  left: 5,
                  right: 5,
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    if (showImage) _buildImage(),
                    const Gap(14.0),
                    _buildContent(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImage() {
    return Column(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(8.0),
          child: CustomCachedImage(
            width: imageWidth,
            height: imageHeight,
            imageUrl: logoUrl ?? '',
            defaultImage: 'https://via.placeholder.com/150',
          ),
        ),
      ],
    );
  }

  Widget _buildContent() {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTitleWithIcon(),
          const Divider(height: 12.0, thickness: 0.4),
          ...labelValueList.map(
            (lv) => LabelValueRow(
              label: lv.label,
              value: lv.value,
              labelFontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTitleWithIcon() {
    return Row(
      children: [
        if (titleIcon != null)
          Icon(
            titleIcon,
            size: 18,
            color: AppColors.primary,
          ),
        if (titleIcon != null) const Gap(5.0),
        Expanded(
          child: Text(
            title ?? '',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBadge({
    required String text,
    required String color,
    required Alignment alignment,
    BorderRadius? borderRadius,
  }) {
    return Align(
      alignment: alignment,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
        decoration: BoxDecoration(
          color: hexToColor(color),
          borderRadius: borderRadius ?? BorderRadius.circular(5.0),
        ),
        child: Text(
          text,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}

class LabelValuePair {
  final String label;
  final String value;

  LabelValuePair({required this.label, required this.value});
}
