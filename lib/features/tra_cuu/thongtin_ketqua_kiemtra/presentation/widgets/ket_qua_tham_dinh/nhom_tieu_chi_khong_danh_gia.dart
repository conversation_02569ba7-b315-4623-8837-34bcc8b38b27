import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/presentation/controller/thong_tin_ket_qua_kiem_tra_controller.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';

class NhomTieuChiKhongDanhGia extends GetView<ThongTinKetQuaKiemTraController> {
  const NhomTieuChiKhongDanhGia({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading.value) {
        return _buildShimmerDetails();
      }
      return SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Thông tin biên bản",
                style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary),
              ),
              const Gap(10),
              _buildDataTable()
            ],
          ),
        ),
      );
    });
  }

  Widget _buildDataTable() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Table(
        border: TableBorder.all(color: Colors.black, width: 1),
        columnWidths: const {
          0: FixedColumnWidth(50),
          1: FixedColumnWidth(250),
          2: FixedColumnWidth(250),
          3: FixedColumnWidth(250),
        },
        children: [
          TableRow(
            decoration: BoxDecoration(color: Colors.grey[300]),
            children: [
              tableHeader('STT'),
              tableHeader('Điều khoản tham chiếu'),
              tableHeader('Chỉ tiêu đánh giá'),
              tableHeader('Lý do'),
            ],
          ),
          ...controller.tieuChiKhongDanhGia
              .map((item) => TableRow(children: [
                    tableCell(item.STT ?? ""),
                    tableCell(item.DieuKhoanThamChieu ?? ""),
                    tableCell(item.TenChiTieu ?? ""),
                    tableCell(item.LyDo ?? ""),
                  ]))
              .toList(),
        ],
      ),
    );
  }

  Widget tableHeader(String text) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Text(
        text,
        style: TextStyle(fontWeight: FontWeight.bold),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget tableCell(String text) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Text(
        text,
        textAlign: TextAlign.left,
      ),
    );
  }

  Widget _buildShimmerDetails() {
    return SizedBox(
      height: 80.h,
      child: ListView.builder(
        itemCount: 5,
        itemBuilder: (context, index) {
          return Padding(
            padding: const EdgeInsets.all(8.0),
            child: Container(
              height: 20,
              color: Colors.grey[300],
            ),
          );
        },
      ),
    );
  }
}
