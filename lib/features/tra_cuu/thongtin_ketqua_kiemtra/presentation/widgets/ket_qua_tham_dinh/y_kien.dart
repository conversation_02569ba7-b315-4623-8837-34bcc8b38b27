// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/model/thong_tin_ket_qua_kiem_tra_model.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/presentation/controller/thong_tin_ket_qua_kiem_tra_controller.dart';

class <PERSON><PERSON>ien extends GetView<ThongTinKetQuaKiemTraController> {
  ThongTinKetQuaKiemTraModel item;

  YKien({
    super.key,
    required this.item,
  });
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading.value) {
        return _buildShimmerDetails();
      }

      List<dynamic> chiTieu = item.ChiDinhChiTieuPhanTich?.toString() != null
          ? jsonDecode(item.ChiDinhChiTieuPhanTich.toString()) as List<dynamic>
          : [];

      return SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                spacing: 10,
                children: [
                  Text(
                    "1. Ý kiến của đoàn thẩm định",
                    style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.primary),
                  ),
                  Expanded(
                      child: Divider(
                    thickness: 1,
                    color: AppColors.primary,
                  )),
                ],
              ),
              const Gap(20),
              Text(
                "\t 1.1 Ý kiến của đoàn thẩm định",
                style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.primary),
              ),
              const Gap(10),
              _contentBox(item.YKienDoanThamDinh ?? controller.defaultValue),
              const Gap(10),
              Text(
                "\t 1.2 Đề xuất kết quả đánh giá (Đạt/Không đạt)",
                style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.primary),
              ),
              const Gap(10),
              _contentBox(item.DeXuatKetQua ?? controller.defaultValue),
              const Gap(10),
              Text(
                "\t 1.3 Nội dung cần khắc phục, thời hạn khắc phục, báo cáo khắc phục",
                style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary),
              ),
              const Gap(10),
              _contentBox(item.NoiDungKhacPhuc ?? controller.defaultValue),
              const Gap(10),
              Row(
                spacing: 10,
                children: [
                  Text(
                    "2. Ý kiến của đại diện cơ sở",
                    style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary),
                  ),
                  Expanded(
                      child: Divider(
                    thickness: 1,
                    color: AppColors.primary,
                  )),
                ],
              ),
              const Gap(10),
              _contentBox(item.YKienCoSoThamDinh ?? controller.defaultValue),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildShimmerDetails() {
    return SizedBox(
      height: 80.h,
      child: ListView.builder(
        itemCount: 5,
        itemBuilder: (context, index) {
          return Padding(
            padding: const EdgeInsets.all(8.0),
            child: Container(
              height: 20,
              color: Colors.grey[300],
            ),
          );
        },
      ),
    );
  }

  Widget _contentBox(String content) {
    return Row(
      children: [
        Expanded(
          child: Container(
            padding: EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.white,
              // borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: 5,
                  spreadRadius: 1,
                  offset: Offset(2, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  content,
                  style: TextStyle(color: Colors.black, fontSize: 16.sp),
                ),
                SizedBox(height: 5),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
