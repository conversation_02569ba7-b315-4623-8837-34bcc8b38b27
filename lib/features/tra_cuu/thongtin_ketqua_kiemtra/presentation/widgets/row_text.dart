import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:flutter/material.dart';

Widget rowText(
    {required String title, required String value, bool? rightAlign}) {
  return RichText(
    textAlign: rightAlign == true ? TextAlign.right : TextAlign.left,
    text: TextSpan(
      style: TextStyle(
          color: Colors.black,
          fontSize: AppDimens.subText,
          fontWeight: FontWeight.bold),
      children: [
        TextSpan(
            text: title,
            style: TextStyle(
                fontWeight: FontWeight.w600, fontSize: AppDimens.subText)),
        TextSpan(
            text: value,
            style: TextStyle(
                fontWeight: FontWeight.w400, fontSize: AppDimens.subText)),
      ],
    ),
  );
}
