// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:attp_2024/core/configs/contents/app_content.dart';
import 'package:attp_2024/core/ui/snackbar/snackbar_until.dart';
import 'package:attp_2024/core/ui/widgets/webview/webview_page.dart';
import 'package:attp_2024/core/utils/color_utils.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/model/thong_tin_ket_qua_kiem_tra_model.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/presentation/controller/thong_tin_ket_qua_kiem_tra_controller.dart';

class KetLuanKiemTra extends GetView<ThongTinKetQuaKiemTraController> {
  ThongTinKetQuaKiemTraModel item;

  KetLuanKiemTra({
    super.key,
    required this.item,
  });
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading.value) {
        return _buildShimmerDetails();
      }

      return SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Kết luận kiểm tra",
                style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary),
              ),
              const Gap(10),
              Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(10),
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        // borderRadius: BorderRadius.circular(10),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black26,
                            blurRadius: 5,
                            spreadRadius: 1,
                            offset: Offset(2, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            item.KetLuanKiemTra ?? controller.defaultValue,
                            style:
                                TextStyle(color: Colors.black, fontSize: 16.sp),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              const Gap(10),
              Text(
                "Kết quả kiểm tra",
                style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary),
              ),
              const Gap(8),
              Container(
                  color: hexToColor(item.MauSac),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 5.0, vertical: 3.0),
                    child: Text(
                      (item.XepLoai ?? '') != ''
                          ? item.XepLoai!
                          : controller.defaultValue,
                      style: TextStyle(
                          color: Colors.white, fontWeight: FontWeight.w700),
                    ),
                  )),
              const SizedBox(height: 10),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildShimmerDetails() {
    return SizedBox(
      height: 80.h,
      child: ListView.builder(
        itemCount: 5,
        itemBuilder: (context, index) {
          return Padding(
            padding: const EdgeInsets.all(8.0),
            child: Container(
              height: 20,
              color: Colors.grey[300],
            ),
          );
        },
      ),
    );
  }
}
