// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:attp_2024/core/configs/contents/app_content.dart';
import 'package:attp_2024/core/ui/snackbar/snackbar_until.dart';
import 'package:attp_2024/core/ui/widgets/webview/webview_page.dart';
import 'package:attp_2024/core/utils/color_utils.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/model/thong_tin_ket_qua_kiem_tra_model.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/presentation/controller/thong_tin_ket_qua_kiem_tra_controller.dart';

class KetQuaThongTinChung extends GetView<ThongTinKetQuaKiemTraController> {
  ThongTinKetQuaKiemTraModel item;
  KetQuaThongTinChung({
    super.key,
    required this.item,
  });
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading.value) {
        return _buildShimmerDetails();
      }
      return SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Thông tin cơ sở sản xuất",
                style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary),
              ),
              const Gap(10),
              const SizedBox(height: 16),
              _buildInfoRow(
                  "Cơ sở sản xuất:",
                  (item.CoSoThamDinh ?? '') != ''
                      ? item.CoSoThamDinh!
                      : controller.defaultValue,
                  icon: Icons.business),
              _buildInfoRow(
                  "Đại diện cơ sở:",
                  (item.HoVaTen_DaiDien ?? '') != ''
                      ? item.HoVaTen_DaiDien!
                      : controller.defaultValue,
                  icon: Icons.person),
              _buildInfoRow(
                  "Địa chỉ:",
                  (item.DiaChi ?? '') != ''
                      ? item.DiaChi!
                      : controller.defaultValue,
                  icon: Icons.location_on),
              _buildInfoRow(
                  "Loại hình:",
                  (item.LoaiHinhCoSo ?? '') != ''
                      ? item.LoaiHinhCoSo!
                      : controller.defaultValue,
                  icon: Icons.calendar_month),
              const Divider(),
              Text(
                "Thông tin biên bản",
                style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary),
              ),
              const Gap(10),
              _buildInfoRow(
                  "Số biên bản:",
                  (item.SoBienBan ?? '') != ''
                      ? item.SoBienBan!
                      : controller.defaultValue,
                  icon: Icons.tag),
              _buildInfoRow(
                  "Ngày lập",
                  (item.NgayLap ?? '') != ''
                      ? item.NgayLap!
                      : controller.defaultValue,
                  icon: Icons.calendar_today),
              _buildInfoRow(
                  "Người lập:",
                  (item.NhanVien_NguoiLap ?? '') != ''
                      ? item.NhanVien_NguoiLap!
                      : controller.defaultValue,
                  icon: Icons.calendar_today),
              _buildInfoRow(
                  "Chức vụ:",
                  (item.ChucVu_NguoiLap ?? '') != ''
                      ? item.ChucVu_NguoiLap!
                      : controller.defaultValue,
                  icon: Icons.tag),
              _buildInfoRow(
                  "Ngày thẩm định:",
                  (item.NgayThamDinh ?? '') != ''
                      ? item.NgayThamDinh!
                      : controller.defaultValue,
                  icon: Icons.calendar_today),
              _buildInfoRow(
                  "Hình thức thẩm định:",
                  (item.HinhThucThamDinh ?? '') != ''
                      ? item.HinhThucThamDinh!
                      : controller.defaultValue,
                  icon: Icons.stacked_bar_chart),
              _buildInfoRow(
                  "Mẫu biên bản:",
                  (item.MauBienBanID ?? '') != ''
                      ? item.MauBienBanID!
                      : controller.defaultValue,
                  icon: Icons.tag),
              _buildInfoRowWidget(
                  "Đính kèm: ",
                  GestureDetector(
                    onTap: () {
                      if (item.DinhKem == null ||
                          item.DinhKem.toString().isEmpty ||
                          item.DinhKem.toString() == '[]') {
                        SnackbarUtil.showWarning("Không có đính kèm !",
                            alignment: 'bottom');
                      } else {
                        Get.to(() => WebViewPage(
                              title: AppContent.appTitleWebview,
                              initialUrl:
                                  '${controller.userAccessModel?.siteURL}' +
                                      item.DinhKem.toString().substring(0,
                                          item.DinhKem.toString().length - 1),
                            ));
                      }
                    },
                    child: Text(
                      'Xem đính kèm',
                      style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          color: AppColors.activeColors),
                    ),
                  ),
                  icon: Icons.attach_file),
              _buildInfoRowWidget(
                "Trạng thái:",
                Container(
                    color: AppColors.ConHieuLucDuoi6Thang,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 5.0, vertical: 3.0),
                      child: Text(
                        item.TrangThai ?? "",
                        style: const TextStyle(
                            color: Colors.white, fontWeight: FontWeight.w700),
                      ),
                    )),
                icon: Icons.stacked_bar_chart,
              ),
              const Divider(),
              Text(
                "Đại diện đoàn kiểm tra, thẩm định",
                style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary),
              ),
              const Gap(10),
              _buildInfoRow(
                  "Ngày thẩm định:",
                  (item.NgayThamDinh ?? '') != ''
                      ? item.NgayThamDinh!
                      : controller.defaultValue,
                  icon: Icons.calendar_today),
              _buildInfoRow(
                  "Đại diện đoàn thẩm định:",
                  (item.DaiDienDoanThamDinh ?? '') != ''
                      ? item.DaiDienDoanThamDinh!
                      : controller.defaultValue,
                  icon: Icons.person),
              const Divider(),
              const Gap(8),
              Text(
                "Kết quả kiểm tra",
                style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary),
              ),
              const Gap(8),
              Container(
                  color: hexToColor(item.MauSac),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 5.0, vertical: 3.0),
                    child: Text(
                      (item.XepLoai ?? '') != ''
                          ? item.XepLoai!
                          : controller.defaultValue,
                      style: const TextStyle(
                          color: Colors.white, fontWeight: FontWeight.w700),
                    ),
                  )),
              const SizedBox(height: 10),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildProcessbarKQDG(
      int percent1, int percent2, int percent3, int percent4) {
    int total = percent1 + percent2 + percent3 + percent4;

    return Row(
      children: [
        Expanded(
          flex: percent1,
          child: Container(
            color: AppColors.ConHieuLucDuoi6Thang,
            alignment: Alignment.center,
            child: TextWidget(
              text: '$percent1',
              color: AppColors.white,
              textAlign: TextAlign.center,
            ),
          ),
        ),
        Expanded(
          flex: percent2,
          child: Container(
            color: AppColors.DaThuHoi,
            alignment: Alignment.center,
            child: TextWidget(
              text: '$percent2',
              color: AppColors.white,
              textAlign: TextAlign.center,
            ),
          ),
        ),
        Expanded(
          flex: percent3,
          child: Container(
            color: AppColors.HetHieuLuc,
            alignment: Alignment.center,
            child: TextWidget(
              text: '$percent3',
              color: AppColors.white,
              textAlign: TextAlign.center,
            ),
          ),
        ),
        Expanded(
          flex: percent4,
          child: Container(
            color: AppColors.ConHieuLucTren6Thang,
            alignment: Alignment.center,
            child: TextWidget(
              text: '$percent4',
              color: AppColors.white,
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value, {IconData? icon}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (icon != null)
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: Icon(
                icon,
                size: 20,
                color: AppColors.primary,
              ),
            ),
          SizedBox(
            width: 140, // Đặt chiều rộng cố định cho label
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(height: 1.5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRowWidget(String label, Widget value, {IconData? icon}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (icon != null)
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: Icon(
                icon,
                size: 20,
                color: AppColors.primary,
              ),
            ),
          SizedBox(
            width: 140, // Đặt chiều rộng cố định cho label
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Flexible(
            // Dùng Flexible thay vì Expanded
            fit: FlexFit.loose, // Để nó chỉ chiếm vừa đủ, không mở rộng toàn bộ
            child: value,
          ),
        ],
      ),
    );
  }

  Widget _buildShimmerDetails() {
    return SizedBox(
      height: 80.h,
      child: ListView.builder(
        itemCount: 5,
        itemBuilder: (context, index) {
          return Padding(
            padding: const EdgeInsets.all(8.0),
            child: Container(
              height: 20,
              color: Colors.grey[300],
            ),
          );
        },
      ),
    );
  }
}
