import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/presentation/controller/thong_tin_ket_qua_kiem_tra_controller.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/presentation/widgets/filter_modal.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class SearchWidget extends GetView<ThongTinKetQuaKiemTraController> {
  const SearchWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final TextEditingController textController =
        TextEditingController(text: controller.searchQuery.value);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 1.h),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 5.h,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(5),
                  ),
                  child: Obx(() {
                    textController.text = controller.searchQuery.value;
                    textController.selection = TextSelection.fromPosition(
                      TextPosition(offset: textController.text.length),
                    );

                    return TextField(
                      controller: textController,
                      style: TextStyle(fontSize: 16.sp),
                      textAlignVertical: TextAlignVertical.center,
                      decoration: InputDecoration(
                        hintText: 'Tìm kiếm...',
                        hintStyle:
                            TextStyle(fontSize: 16.sp, color: Colors.grey[500]),
                        prefixIcon: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 0),
                          child: Icon(Icons.search,
                              size: 18.sp, color: Colors.grey[600]),
                        ),
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 0, vertical: 11),
                        suffixIcon: controller.searchQuery.value.isNotEmpty
                            ? IconButton(
                                icon: Icon(Icons.clear,
                                    size: 18.sp, color: Colors.grey[600]),
                                onPressed: () {
                                  controller.searchQuery.value = "";
                                  textController.clear();
                                  controller.fetchSearchResults("");
                                },
                              )
                            : null,
                      ),
                      onChanged: (value) {
                        controller.searchQuery.value = value;
                        controller.fetchSearchResults(value);
                      },
                    );
                  }),
                ),
              ),
              Gap(1.w),
              Container(
                height: 42,
                width: 40,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(5),
                ),
                child: IconButton(
                  icon: Icon(Icons.tune, size: 18.sp, color: Colors.grey[700]),
                  padding: const EdgeInsets.all(4),
                  constraints:
                      const BoxConstraints(minWidth: 32, minHeight: 32),
                  onPressed: () {
                    FilterModal.show(context);
                  },
                ),
              ),
            ],
          ),
          Gap(1.h),
          Obx(() => Align(
                alignment: Alignment.centerLeft,
                child: Text.rich(
                  TextSpan(
                    children: [
                      TextSpan(
                        text: 'Kết quả: ',
                        style: TextStyle(
                          fontSize: 15.sp,
                          color: Colors.grey[600],
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                      TextSpan(
                        text: controller.filteredResults.isNotEmpty
                            ? '${controller.filteredResults.first.TotalResults}'
                            : '0',
                        style: TextStyle(
                          fontSize: 15.sp,
                          color: Colors.blue,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              )),
        ],
      ),
    );
  }
}
