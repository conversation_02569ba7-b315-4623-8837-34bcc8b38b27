import 'dart:developer';

import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/CustomDatePicker/CustomDatePicker.dart';
import 'package:attp_2024/core/ui/widgets/button/button_widget.dart';
import 'package:attp_2024/core/ui/widgets/custom_combo/combo.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/presentation/controller/thong_tin_ket_qua_kiem_tra_controller.dart';

class FilterModal extends GetView<ThongTinKetQuaKiemTraController> {
  const FilterModal({super.key});

  static void show(BuildContext context) {
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (context) {
        return SizedBox(height: 80.h, child: const FilterModal());
      },
    );
  }

  Widget buildCombobox({
    required String title,
    required RxList<dynamic> items,
    required Function(DropdownModel?) onChange,
    required String Function(dynamic) displayMapper,
    required String Function(dynamic) valueMapper,
    Function()? onDelete,
    DropdownModel? defaultSelectedItem, // Thêm tham số defaultSelectedItem
    bool isEnabled = true,
    bool showDelete = true,
  }) {
    return Obx(() => CustomCombobox(
          title: title,
          weight: 100.w,
          onChange: (selected) => onChange(selected),
          delete: onDelete,
          defaultSelectedItem: defaultSelectedItem, // Truyền giá trị mặc định
          dropDownList: items
              .map((e) => DropdownModel(
                    id: valueMapper(e),
                    display: displayMapper(e),
                  ))
              .toList(),
          isEnabled: isEnabled,
          showDelete: showDelete,
        ));
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Container(
        padding: const EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Divider(
              color: Colors.grey.withOpacity(0.3),
              thickness: 3.5,
              indent: 150,
              endIndent: 150,
            ),
            Row(
              children: [
                const Spacer(),
                const Spacer(),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Tra cứu nâng cao',
                      style: TextStyle(
                        fontSize: AppDimens.largeText,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryFocus,
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconButton(
                      icon: const Icon(
                        Icons.clear,
                        color: Colors.red,
                      ),
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                    ),
                  ],
                ),
              ],
            ),
            const Gap(16),
            Expanded(
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: CustomDatePicker(
                          isRequired: true,
                          title: 'Từ ngày',
                          placeholder: 'dd-MM-yyyy',
                          date: controller.startDate.value,
                          onChange: (DateTime? newDate) {
                            //print("startDate: ${controller.startDate.value}");
                            controller.startDate.value = newDate!;
                          },
                        ),
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: CustomDatePicker(
                          isRequired: true,
                          title: 'Đến ngày',
                          placeholder: 'dd-MM-yyyy',
                          date: controller.endDate.value,
                          onChange: (DateTime? newDate) {
                            //print("endDate: ${controller.endDate.value}");
                            controller.endDate.value = newDate!;
                          },
                        ),
                      ),
                    ],
                  ),
                  const Gap(16),
                  Row(
                    children: [
                      Expanded(
                        child: buildCombobox(
                          title: "Tỉnh/Thành Phố",
                          items: controller.provinces,
                          defaultSelectedItem:
                              controller.provinceFilter.value.isNotEmpty
                                  ? DropdownModel(
                                      id: controller.provinceFilter.value,
                                      display: controller.provinces.firstWhere(
                                        (province) =>
                                            province['value'] ==
                                            controller.provinceFilter.value,
                                        orElse: () => {'display': ''},
                                      )['display'],
                                    )
                                  : null,
                          onChange: (selected) {
                            if (selected != null) {
                              controller.provinceFilter.value = selected.id!;
                              log(selected.id!);
                              controller.fetchAllDistricts(
                                  tinhID: selected.id!);
                            }
                          },
                          onDelete: () {
                            controller.clearProvinceFilters();
                          },
                          displayMapper: (e) => e['display'],
                          valueMapper: (e) => e['value'],
                          isEnabled: controller.userGroup.value == 'Admin'
                              ? true
                              : false,
                          showDelete: controller.userGroup.value == 'Admin'
                              ? true
                              : false,
                        ),
                      ),
                      const Gap(10),
                      Expanded(
                        child: buildCombobox(
                          title: "Quận/Huyện",
                          items: controller.districts,
                          defaultSelectedItem:
                              controller.districtFilter.value.isNotEmpty
                                  ? DropdownModel(
                                      id: controller.districtFilter.value,
                                      display: controller.districts.firstWhere(
                                        (district) =>
                                            district['value'] ==
                                            controller.districtFilter.value,
                                        orElse: () => {'display': ''},
                                      )['display'],
                                    )
                                  : null,
                          onChange: (selected) {
                            if (selected != null) {
                              controller.districtFilter.value = selected.id!;
                              controller.fetchAllCommunes(
                                  huyenID: selected.id!);
                            }
                          },
                          onDelete: () {
                            controller.clearDistrictFilters();
                          },
                          displayMapper: (e) => e['display'],
                          valueMapper: (e) => e['value'],
                          isEnabled: controller.userGroup.value == 'User'
                              ? false
                              : true,
                          showDelete: controller.userGroup.value == 'User'
                              ? false
                              : true,
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: buildCombobox(
                          title: "Xã/Phường",
                          items: controller.communes,
                          onChange: (selected) {
                            if (selected != null) {
                              controller.communeFilter.value = selected.id!;
                              controller.fetchAllVillages(xaID: selected.id!);
                              log(selected.id!);
                            }
                          },
                          defaultSelectedItem:
                              controller.communeFilter.value.isNotEmpty
                                  ? DropdownModel(
                                      id: controller.communeFilter.value,
                                      display: controller.communes.firstWhere(
                                        (communes) =>
                                            communes['value'] ==
                                            controller.communeFilter.value,
                                        orElse: () => {'display': ''},
                                      )['display'],
                                    )
                                  : null,
                          onDelete: () {
                            controller.clearCommuneFilters();
                          },
                          displayMapper: (e) => e['display'],
                          valueMapper: (e) => e['value'],
                        ),
                      ),
                      const Gap(10),
                      Expanded(
                        child: buildCombobox(
                          title: "Thôn/Xóm",
                          items: controller.villages,
                          onChange: (selected) {
                            if (selected != null) {
                              controller.villageFilter.value = selected.id!;
                              log(selected.id!);
                            }
                          },
                          defaultSelectedItem:
                              controller.villageFilter.value.isNotEmpty
                                  ? DropdownModel(
                                      id: controller.villageFilter.value,
                                      display: controller.villages.firstWhere(
                                        (villages) =>
                                            villages['value'] ==
                                            controller.villageFilter.value,
                                        orElse: () => {'display': ''},
                                      )['display'],
                                    )
                                  : null,
                          displayMapper: (e) => e['display'],
                          valueMapper: (e) => e['value'],
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: buildCombobox(
                          title: "Kết quả kiểm tra",
                          items: controller.ketQuaKiemTra,
                          onChange: (selected) {
                            controller.ketQuaKiemTraId.value =
                                selected?.id ?? '';
                          },
                          defaultSelectedItem: controller
                                  .ketQuaKiemTraId.value.isNotEmpty
                              ? DropdownModel(
                                  id: controller.ketQuaKiemTraId.value,
                                  display: controller.ketQuaKiemTra.firstWhere(
                                    (province) =>
                                        province['value'] ==
                                        controller.ketQuaKiemTraId.value,
                                    orElse: () => {'display': ''},
                                  )['display'],
                                )
                              : null,
                          displayMapper: (e) => e['display'],
                          valueMapper: (e) => e['value'],
                        ),
                      ),
                      const Gap(10),
                      Expanded(
                        child: buildCombobox(
                          title: "Trạng thái thẩm định",
                          items: controller.trangThaiXepLoai,
                          onChange: (selected) {
                            controller.trangThaiXepLoaiId.value =
                                selected?.id ?? '';
                          },
                          defaultSelectedItem: controller
                                  .trangThaiXepLoaiId.value.isNotEmpty
                              ? DropdownModel(
                                  id: controller.trangThaiXepLoaiId.value,
                                  display:
                                      controller.trangThaiXepLoai.firstWhere(
                                    (province) =>
                                        province['value'] ==
                                        controller.trangThaiXepLoaiId.value,
                                    orElse: () => {'display': ''},
                                  )['display'],
                                )
                              : null,
                          displayMapper: (e) => e['display'],
                          valueMapper: (e) => e['value'],
                        ),
                      ),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ButtonWidget(
                        text: 'Áp dụng',
                        ontap: () {
                          print(controller.ketQuaKiemTraId.value);
                          controller.fetchSearchResultsWithFilters();
                          Navigator.of(context).pop();
                        },
                        width: MediaQuery.of(context).size.width * 0.90,
                        backgroundColor: Colors.green,
                        textColor: AppColors.white,
                        isBorder: true,
                        borderColor: Colors.grey[100],
                        borderRadius: 6,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12.0, vertical: 10.0),
                        textSize: 16.0,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
