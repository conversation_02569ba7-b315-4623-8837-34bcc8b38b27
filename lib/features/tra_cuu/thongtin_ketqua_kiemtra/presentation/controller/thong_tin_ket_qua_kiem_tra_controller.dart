import 'package:attp_2024/core/data/models/user_access_model.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/model/tieu_chi_danh_gia.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/model/tieu_chi_khong_danh_gia.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:attp_2024/core/configs/contanst/proc_constants.dart';
import 'package:attp_2024/core/data/api/services/proc/proc_service.dart';
import 'package:attp_2024/core/services/user_use_case.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/model/thong_tin_ket_qua_kiem_tra_model.dart';

class ThongTinKetQuaKiemTraController extends GetxController
    with GetSingleTickerProviderStateMixin {
  final ProcService _procService = Get.find<ProcService>();
  var isLoading = false.obs;
  var isLoadingMore = false.obs;
  var currentPage = 1.obs;
  final int pageSize = 10;
  var hasMoreData = true.obs;
  var tieuChiDanhGia = <TieuChiDanhGiaModel>[].obs;
  var tieuChiKhongDanhGia = <TieuChiKhongDanhGiaModel>[].obs;
  var searchResults = <ThongTinKetQuaKiemTraModel>[].obs;
  var filteredResults = <ThongTinKetQuaKiemTraModel>[].obs;
  var searchQuery = "".obs;
  final log = Logger();
  final String defaultValue = 'Trống';
  late TabController tabController;
  final PageController pageController = PageController();

  static const String PROC_NAME =
      "Proc_Mobile_GetAll_TraCuuKetQuaKiemTraThamDinh_Pagination";

  var donViId = '';
  var userId = '';
  var userGroup = ''.obs;
  var defaultTinh = '';
  var defaultHuyen = '';
  var provinces = [].obs;
  var districts = [].obs;
  var communes = [].obs;
  var villages = [].obs;
  var loaiHinhCSs = [].obs;
  var tinhTrangBanHanh = [].obs;
  var provinceFilter = ''.obs;
  var districtFilter = ''.obs;
  var communeFilter = ''.obs;
  var villageFilter = ''.obs;
  var loaihinhCsId = ''.obs;
  var tuNgay = ''.obs;
  var denNgay = ''.obs;
  var tinhTrangBanHanhId = ''.obs;
  var isLoadingProvinces = false.obs;
  var isLoadingDistricts = false.obs;
  var isLoadingCommunes = false.obs;
  var isLoadingVillages = false.obs;
  var isLoadingLoaiHinhCS = false.obs;

  var isLoadingTrangThaiXepLoai = false.obs;
  var trangThaiXepLoai = [].obs;
  var trangThaiXepLoaiId = ''.obs;

  var isLoadingKetQuaKiemTra = false.obs;
  var ketQuaKiemTra = [].obs;
  var ketQuaKiemTraId = ''.obs;

  Rx<DateTime> startDate = DateTime(DateTime.now().year, 1, 1).obs;
  Rx<DateTime> endDate = DateTime.now().obs;

  var isLoadingdefaultTinhHuyenXa = false.obs;
  var defaultTinhHuyenXa = [].obs;
  String defaultID = '00000000-0000-0000-0000-000000000000';

  UserAccessModel? userAccessModel;

  @override
  Future<void> onInit() async {
    super.onInit();
    tabController = TabController(length: 5, vsync: this);
    tabController.addListener(() {
      if (tabController.indexIsChanging) {
        pageController.jumpToPage(tabController.index);
      }
    });
    fetchAllProvinces();
    await loadInfoProfile();
    await fetchDonViByID();
    fetchSearchResults(""); // Tải dữ liệu ban đầu.
    fetchComboTrangThaiXepLoai();
    fetchComboKetQuaKiemTra();
  }

  Future<void> loadInfoProfile() async {
    userAccessModel = await UserUseCase.getUser();
    userGroup.value = userAccessModel?.userGroupCode ?? '';
    userId = userAccessModel?.userID ?? defaultID;
    donViId = userAccessModel?.donViID ?? defaultID;
    print('debug: $donViId');
  }

  void clearProvinceFilters() {
    defaultTinhHuyenXa.clear();
    provinceFilter.value = '';
    districtFilter.value = '';
    communeFilter.value = '';
    villageFilter.value = '';
    districts.clear();
    communes.clear();
    villages.clear();
  }

  void clearDistrictFilters() {
    districtFilter.value = '';
    communeFilter.value = '';
    villageFilter.value = '';
    communes.clear();
    villages.clear();
  }

  void clearCommuneFilters() {
    communeFilter.value = '';
    villageFilter.value = '';
    villages.clear();
  }

  Future<void> fetchAllProvinces() async {
    isLoadingProvinces.value = true;
    final List<Map<String, dynamic>> body = [];
    try {
      List<dynamic> provincesResponse =
          await _procService.callProc(ProcConstants.getAllDiaBanHCTinh, body);
      List<Map<String, dynamic>> mappedTinhResponse =
          provincesResponse.cast<Map<String, dynamic>>();

      provinces.assignAll(
        mappedTinhResponse.map((Map<String, dynamic> tinhs) {
          return {
            "value": tinhs['DiaBanHCID'],
            "display": tinhs['TenDiaBan'],
          };
        }).toList(),
      );
      if (defaultTinhHuyenXa.first['DiaBanHCID_Tinh'] != defaultID) {
        provinceFilter.value = defaultTinhHuyenXa.first['DiaBanHCID_Tinh'];
        fetchAllDistricts(tinhID: defaultTinhHuyenXa.first['DiaBanHCID_Tinh']);
      }
    } finally {
      isLoadingProvinces.value = false;
    }
  }

  Future<void> fetchAllDistricts({required String tinhID}) async {
    isLoadingDistricts.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "TinhID", "type": "guid", "value": tinhID},
    ];
    try {
      List<dynamic> districtsResponse =
          await _procService.callProc(ProcConstants.getAllDiaBanHCHuyen, body);
      List<Map<String, dynamic>> mappedHuyenResponse =
          districtsResponse.cast<Map<String, dynamic>>();

      districts.assignAll(
        mappedHuyenResponse.map((Map<String, dynamic> huyens) {
          return {
            "value": huyens['DiaBanHCID'],
            "display": huyens['TenDiaBan'],
          };
        }).toList(),
      );
    } finally {
      isLoadingDistricts.value = false;
    }
  }

  Future<void> fetchAllCommunes({required String huyenID}) async {
    isLoadingCommunes.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "HuyenID", "type": "guid", "value": huyenID},
    ];
    try {
      List<dynamic> communesResponse =
          await _procService.callProc(ProcConstants.getAllDiaBanHCXa, body);
      List<Map<String, dynamic>> mappedXaResponse =
          communesResponse.cast<Map<String, dynamic>>();

      communes.assignAll(
        mappedXaResponse.map((Map<String, dynamic> xas) {
          return {
            "value": xas['DiaBanHCID'],
            "display": xas['TenDiaBan'],
          };
        }).toList(),
      );
    } finally {
      isLoadingCommunes.value = false;
    }
  }

  Future<void> fetchAllVillages({required String xaID}) async {
    isLoadingVillages.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "xaID", "type": "guid", "value": xaID},
    ];
    try {
      List<dynamic> villagesResponse =
          await _procService.callProc(ProcConstants.getAllDiaBanHCThon, body);
      List<Map<String, dynamic>> mappedThonResponse =
          villagesResponse.cast<Map<String, dynamic>>();

      villages.assignAll(
        mappedThonResponse.map((Map<String, dynamic> thons) {
          return {
            "value": thons['DiaBanHCID'],
            "display": thons['TenDiaBan'],
          };
        }).toList(),
      );
    } finally {
      isLoadingVillages.value = false;
    }
  }

  // Future<void> fetchAllDefaultTinhHuyenXa({required String donViID}) async {
  //   isLoadingdefaultTinhHuyenXa.value = true;
  //   final List<Map<String, dynamic>> body = [
  //     {"name": "DonViID", "type": "guid", "value": donViId}
  //   ];
  //   try {
  //     List<dynamic> res = await _procService.callProc(
  //         'Proc_Mobile_Get_ThietLapHeThong_ByDonViID', body);
  //     List<Map<String, dynamic>> mappedResponse =
  //         res.cast<Map<String, dynamic>>();
  //     defaultTinhHuyenXa.assignAll(
  //       mappedResponse.map((Map<String, dynamic> item) {
  //         return {
  //           "DiaBanHCID_Tinh": item['DiaBanHCID_Tinh'],
  //           "DiaBanHCID_Huyen": item['DiaBanHCID_Huyen'],
  //           "DiaBanHCID_Xa": item['DiaBanHCID_Xa'],
  //           "DiaBanHCID_Thon": item['DiaBanHCID_Thon'],
  //         };
  //       }).toList(),
  //     );
  //     log.i(defaultTinhHuyenXa.toString());
  //     setDefaultTinhHuyenXa();
  //   } finally {
  //     isLoadingdefaultTinhHuyenXa.value = false;
  //   }
  // }

  Future<void> fetchDonViByID() async {
    isLoadingdefaultTinhHuyenXa.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "ID", "type": "guid", "value": donViId}
    ];
    try {
      List<dynamic> res =
          await _procService.callProc('Proc_Mobile_GetDonViByID', body);
      List<Map<String, dynamic>> mappedResponse =
          res.cast<Map<String, dynamic>>();
      defaultTinhHuyenXa.assignAll(
        mappedResponse.map((Map<String, dynamic> item) {
          return {
            "TinhID_": item['TinhID_'],
            "HuyenID_": item['HuyenID_'],
            "XaID_": item['XaID_'],
            "ThonID_": item['ThonID_'],
          };
        }).toList(),
      );
      log.i(defaultTinhHuyenXa.toString());
      setDefaultTinhHuyenXa();
    } finally {
      isLoadingdefaultTinhHuyenXa.value = false;
    }
  }

  void setDefaultTinhHuyenXa() {
    fetchAllProvinces();
    fetchAllDistricts(tinhID: defaultTinhHuyenXa.first['TinhID_']);
    fetchAllCommunes(huyenID: defaultTinhHuyenXa.first['HuyenID_']);
    fetchAllVillages(xaID: defaultTinhHuyenXa.first['XaID_']);
    if (defaultTinhHuyenXa.isNotEmpty &&
        defaultTinhHuyenXa.first['TinhID_'] !=
            '00000000-0000-0000-0000-000000000000') {
      provinceFilter.value = defaultTinhHuyenXa.first['TinhID_'];
    }
    if (defaultTinhHuyenXa.isNotEmpty &&
        defaultTinhHuyenXa.first['HuyenID_'] !=
            '00000000-0000-0000-0000-000000000000') {
      districtFilter.value = defaultTinhHuyenXa.first['HuyenID_'];
    }
    if (defaultTinhHuyenXa.isNotEmpty &&
        defaultTinhHuyenXa.first['XaID_'] !=
            '00000000-0000-0000-0000-000000000000') {
      communeFilter.value = defaultTinhHuyenXa.first['XaID_'];
    }
    if (defaultTinhHuyenXa.isNotEmpty &&
        defaultTinhHuyenXa.first['ThonID_'] !=
            '00000000-0000-0000-0000-000000000000') {
      villageFilter.value = defaultTinhHuyenXa.first['ThonID_'];
    }
  }

  Future<void> fetchSearchResults(String query) async {
    isLoading.value = true;
    currentPage.value = 1;
    hasMoreData.value = true;
    try {
      final response =
          await _procService.callProc(PROC_NAME, _buildRequestParams(query));
      searchResults.value = response.isNotEmpty
          ? response
              .map<ThongTinKetQuaKiemTraModel>((item) =>
                  ThongTinKetQuaKiemTraModel.fromJson(
                      item as Map<String, dynamic>))
              .toList()
          : [];
      filteredResults.value = searchResults;

      if (response.isEmpty || response.length < pageSize) {
        hasMoreData.value = false;
      }
    } catch (e) {
      log.e("Error fetching search results: $e");
      searchResults.clear();
      filteredResults.clear();
      hasMoreData.value = false;
    } finally {
      isLoading.value = false;
    }
  }

  List<Map<String, dynamic>> _buildRequestParams(String query) {
    return [
      {
        "name": "TuNgay",
        "type": "DateTime",
        "value": DateFormat('yyyy-MM-dd').format(startDate.value)
      },
      {
        "name": "DenNgay",
        "type": "DateTime",
        "value": DateFormat('yyyy-MM-dd').format(endDate.value)
      },
      {
        "name": "TinhID",
        "type": "guid",
        "value": provinceFilter.value.isEmpty
            ? "00000000-0000-0000-0000-000000000000"
            : provinceFilter.value
      },
      {
        "name": "HuyenID",
        "type": "guid",
        "value": districtFilter.value.isEmpty
            ? "00000000-0000-0000-0000-000000000000"
            : districtFilter.value
      },
      {
        "name": "XaID",
        "type": "guid",
        "value": communeFilter.value.isEmpty
            ? "00000000-0000-0000-0000-000000000000"
            : communeFilter.value
      },
      {
        "name": "ThonID",
        "type": "guid",
        "value": villageFilter.value.isEmpty
            ? "00000000-0000-0000-0000-000000000000"
            : villageFilter.value
      },
      {"name": "KetQuaKiemTra", "type": "string", "value": null},
      {"name": "XepLoai", "type": "string", "value": null},
      {"name": "TuKhoa", "type": "string", "value": query},
      {"name": "SapXep", "type": "string", "value": "NgayLap"},
      {"name": "TangGiamSapXep", "type": "string", "value": "ASC"},
      {"name": "DonViID", "type": "guid", "value": donViId},
      {"name": "UserID", "type": "guid", "value": userId},
      {"name": "PageNumber", "type": "int", "value": currentPage.value},
      {"name": "PageSize", "type": "int", "value": pageSize}
    ];
  }

  Future<void> fetchSearchResultsWithFilters() async {
    isLoading.value = true;
    currentPage.value = 1;
    hasMoreData.value = true;
    try {
      final List<Map<String, dynamic>> body = [
        {
          "Name": "TuNgay",
          "Type": "DateTime",
          "Value": DateFormat('yyyy-MM-dd').format(startDate.value)
        },
        {
          "Name": "DenNgay",
          "Type": "DateTime",
          "Value": DateFormat('yyyy-MM-dd').format(endDate.value)
        },
        {
          "Name": "TinhID",
          "Type": "guid",
          "Value": provinceFilter.value.isEmpty
              ? "00000000-0000-0000-0000-000000000000"
              : provinceFilter.value
        },
        {
          "Name": "HuyenID",
          "Type": "guid",
          "Value": districtFilter.value.isEmpty
              ? "00000000-0000-0000-0000-000000000000"
              : districtFilter.value
        },
        {
          "Name": "XaID",
          "Type": "guid",
          "Value": communeFilter.value.isEmpty
              ? "00000000-0000-0000-0000-000000000000"
              : communeFilter.value
        },
        {
          "Name": "ThonID",
          "Type": "guid",
          "Value": villageFilter.value.isEmpty
              ? "00000000-0000-0000-0000-000000000000"
              : villageFilter.value
        },
        {
          "Name": "KetQuaKiemTra",
          "Type": "string",
          "Value": ketQuaKiemTraId.value.isEmpty ? null : ketQuaKiemTraId.value
        },
        {
          "Name": "XepLoai",
          "Type": "string",
          "Value":
              trangThaiXepLoaiId.value.isEmpty ? null : trangThaiXepLoaiId.value
        },
        {"Name": "TuKhoa", "Type": "string", "Value": ""},
        {"Name": "SapXep", "Type": "string", "Value": "NgayLap"},
        {"Name": "TangGiamSapXep", "Type": "string", "Value": "ASC"},
        {"Name": "DonViID", "Type": "guid", "Value": donViId},
        {"Name": "UserID", "Type": "guid", "Value": userId},
        {"Name": "PageNumber", "Type": "int", "Value": currentPage.value},
        {"Name": "PageSize", "Type": "int", "Value": pageSize}
      ];

      final response = await _procService.callProc(PROC_NAME, body);
      searchResults.value = response.isNotEmpty
          ? response
              .map<ThongTinKetQuaKiemTraModel>((item) =>
                  ThongTinKetQuaKiemTraModel.fromJson(
                      item as Map<String, dynamic>))
              .toList()
          : [];
      filteredResults.value = searchResults;

      if (response.isEmpty || response.length < pageSize) {
        hasMoreData.value = false;
      }
    } catch (e) {
      log.e("Error fetching search results: $e");
      searchResults.clear();
      filteredResults.clear();
      hasMoreData.value = false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> fetchChiTieuDanhGia(String bienBanThamDinhATTPID) async {
    isLoading.value = true;
    try {
      final List<Map<String, dynamic>> body = [
        {
          "name": "BienBanThamDinhATTPID",
          "type": "guid",
          "value": bienBanThamDinhATTPID
        },
        {"name": "LoaiThamDinh", "type": "string", "value": "1"}
      ];

      final response = await _procService.callProc(
          'Proc_Mobile_GetAllBienBanThamDinhATTPChiTieu', body);
      tieuChiDanhGia.value = response.isNotEmpty
          ? response
              .map<TieuChiDanhGiaModel>((item) =>
                  TieuChiDanhGiaModel.fromJson(item as Map<String, dynamic>))
              .toList()
          : [];
    } catch (e) {
      log.e("Error fetching search results: $e");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> fetchChiTieuKhongDanhGia(String bienBanThamDinhATTPID) async {
    isLoading.value = true;
    try {
      final List<Map<String, dynamic>> body = [
        {
          "name": "BienBanThamDinhATTPID",
          "type": "guid",
          "value": bienBanThamDinhATTPID
        },
      ];

      final response = await _procService.callProc(
          'Proc_Mobile_GetAllBienBanThamDinhATTPChiTieuKhongDanhGia', body);
      tieuChiKhongDanhGia.value = response.isNotEmpty
          ? response
              .map<TieuChiKhongDanhGiaModel>((item) =>
                  TieuChiKhongDanhGiaModel.fromJson(
                      item as Map<String, dynamic>))
              .toList()
          : [];
    } catch (e) {
      log.e("Error fetching search results: $e");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> fetchComboKetQuaKiemTra() async {
    isLoadingKetQuaKiemTra.value = true;
    try {
      List<dynamic> response =
          await _procService.callProc('Proc_Mobile_GetCombo_KetQuaKiemTra', []);
      List<Map<String, dynamic>> mappedResponse =
          response.cast<Map<String, dynamic>>();

      ketQuaKiemTra.assignAll(
        mappedResponse.map((Map<String, dynamic> item) {
          return {
            "value": item['MaTrangThai'],
            "display": item['TenTrangThai'],
          };
        }).toList(),
      );
    } finally {
      isLoadingKetQuaKiemTra.value = false;
    }
  }

  Future<void> fetchComboTrangThaiXepLoai() async {
    isLoadingTrangThaiXepLoai.value = true;
    try {
      List<dynamic> response = await _procService
          .callProc('Proc_Mobile_GetCombo_TrangThaiPhanLoai', []);
      List<Map<String, dynamic>> mappedResponse =
          response.cast<Map<String, dynamic>>();

      trangThaiXepLoai.assignAll(
        mappedResponse.map((Map<String, dynamic> item) {
          return {
            "value": item['MaTrangThai'],
            "display": item['TenTrangThai'],
          };
        }).toList(),
      );
    } finally {
      isLoadingTrangThaiXepLoai.value = false;
    }
  }

  Future<void> loadMore(String query) async {
    if (isLoadingMore.value || !hasMoreData.value) return;

    isLoadingMore.value = true;
    try {
      currentPage.value++;
      final response =
          await _procService.callProc(PROC_NAME, _buildRequestParams(query));

      if (response.isNotEmpty) {
        final newItems = response
            .map<ThongTinKetQuaKiemTraModel>((item) =>
                ThongTinKetQuaKiemTraModel.fromJson(
                    item as Map<String, dynamic>))
            .toList();

        searchResults.addAll(newItems);
        filteredResults.value = searchResults;

        if (response.length < pageSize) {
          hasMoreData.value = false;
        }
      } else {
        hasMoreData.value = false;
      }
    } catch (e) {
      log.e("Error loading more results: $e");
      hasMoreData.value = false;
    } finally {
      isLoadingMore.value = false;
    }
  }
}
