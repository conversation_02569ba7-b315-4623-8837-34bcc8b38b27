import 'package:get/get.dart';
import 'package:attp_2024/features/tra_cuu/thongtin_ketqua_kiemtra/presentation/controller/thong_tin_ket_qua_kiem_tra_controller.dart';

import '../../../../core/data/api/configs/dio_configs.dart';
import '../../../../core/data/api/services/proc/proc_service.dart';

class ThongTinKetQuaKiemTraBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<ThongTinKetQuaKiemTraController>(
      () => ThongTinKetQuaKiemTraController(),
    );
    Get.lazyPut(() => ProcService(Get.find<DioService>()));
  }
}
