// ignore_for_file: public_member_api_docs, sort_constructors_first
const String defaultImage = "https://via.placeholder.com/150";

class TieuChiDanhGiaModel {
  final String? DieuKhoanThamChieu;
  final String? TenChiTieu;
  final String? Muc;
  final String? STTSX;
  final String? STT;
  final String? BienBanThamDinhATTPChiTieuID;
  final String? DienGiai;
  final String? BienBanThamDinhChiTieuID_Cha;

  TieuChiDanhGiaModel({
    this.DieuKhoanThamChieu,
    this.TenChiTieu,
    this.Muc,
    this.STTSX,
    this.STT,
    this.BienBanThamDinhATTPChiTieuID,
    this.DienGiai,
    this.BienBanThamDinhChiTieuID_Cha,
  });

  factory TieuChiDanhGiaModel.fromJson(Map<String, dynamic> json) {
    return TieuChiDanhGiaModel(
      DieuKhoanThamChieu: json['DieuKhoanThamChieu'] as String?,
      TenChiTieu: json['TenChiTieu'] as String?,
      STTSX: json['STTSX'] as String?,
      STT: json['STT'] as String?,
      Muc: json['Muc'] as String?,
      BienBanThamDinhATTPChiTieuID:
          json['BienBanThamDinhATTPChiTieuID'] as String?,
      DienGiai: json['DienGiai'] as String?,
      BienBanThamDinhChiTieuID_Cha:
          json['BienBanThamDinhChiTieuID_Cha'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'DieuKhoanThamChieu': DieuKhoanThamChieu,
      'TenChiTieu': TenChiTieu,
      'Muc': Muc,
      'STTSX': STTSX,
      'STT': STT,
      'BienBanThamDinhATTPChiTieuID': BienBanThamDinhATTPChiTieuID,
      'DienGiai': DienGiai,
      'BienBanThamDinhChiTieuID_Cha': BienBanThamDinhChiTieuID_Cha,
    };
  }
}
