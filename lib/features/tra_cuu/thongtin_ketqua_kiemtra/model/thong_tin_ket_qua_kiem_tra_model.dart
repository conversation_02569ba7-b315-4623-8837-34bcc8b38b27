// ignore_for_file: public_member_api_docs, sort_constructors_first

const String defaultImage = "https://via.placeholder.com/150";

class ThongTinKetQuaKiemTraModel {
  final String? BienBanThamDinhATTPID;
  final String? NgayLap;
  final String? NhanVien_NguoiLap;
  final String? ChucVu_NguoiLap;
  final String? HinhThucThamDinh;
  final String? MauBienBanID;
  final String? CoSoThamDinh;
  final String? DiaChi;
  final String? SoGPKD;
  final String? NgayCapGPKD;
  final String? CoQuanCapGPKD;
  final String? SoDienThoai;
  final String? TenLoaiHinhCoSo;
  final String? HoVaTen_DaiDien;
  final String? ChucVu_DaiDien;
  final String? LoaiHinhCoSo;
  final String? KetQuaDanhGia;
  final String? NgayThamDinh;
  final String? DaiDienDoanThamDinh;
  final String? YKienDoanThamDinh;
  final String? YKienCoSoThamDinh;
  final String? TrangThai;
  final String? ThongTinVeMauLay;
  final String? ChiDinhChiTieuPhanTich;
  final String? ThuocLoai;
  final String? KetLuanKiemTra;
  final int? PhamTramDat;
  final int? PhamTramNhe;
  final int? PhamTramNang;
  final int? PhamTramNghiemTrong;
  final int? PhamTramChuaDanhGia;
  final String? DanhGiaThucHienCamKet;
  final String? DeXuatKetQua;
  final String? NoiDungKhacPhuc;

  final String? XepLoai;
  final String? DinhKem;
  final String? SoBienBan;
  final String? KetQua;
  final String? NoiDungKetQua;
  final String? TrangThaiPD;
  final String? MauSac;
  final int? TotalResults;

  ThongTinKetQuaKiemTraModel({
    this.BienBanThamDinhATTPID,
    this.NgayLap,
    this.NhanVien_NguoiLap,
    this.ChucVu_NguoiLap,
    this.HinhThucThamDinh,
    this.MauBienBanID,
    this.CoSoThamDinh,
    this.DiaChi,
    this.SoGPKD,
    this.NgayCapGPKD,
    this.CoQuanCapGPKD,
    this.SoDienThoai,
    this.TenLoaiHinhCoSo,
    this.HoVaTen_DaiDien,
    this.ChucVu_DaiDien,
    this.LoaiHinhCoSo,
    this.KetQuaDanhGia,
    this.NgayThamDinh,
    this.DaiDienDoanThamDinh,
    this.YKienDoanThamDinh,
    this.YKienCoSoThamDinh,
    this.TrangThai,
    this.ThongTinVeMauLay,
    this.ChiDinhChiTieuPhanTich,
    this.ThuocLoai,
    this.KetLuanKiemTra,
    this.PhamTramDat,
    this.PhamTramNhe,
    this.PhamTramNang,
    this.PhamTramNghiemTrong,
    this.PhamTramChuaDanhGia,
    this.DanhGiaThucHienCamKet,
    this.DeXuatKetQua,
    this.NoiDungKhacPhuc,
    this.XepLoai,
    this.DinhKem,
    this.SoBienBan,
    this.KetQua,
    this.NoiDungKetQua,
    this.TrangThaiPD,
    this.MauSac,
    this.TotalResults,
  });

  factory ThongTinKetQuaKiemTraModel.fromJson(Map<String, dynamic> json) {
    String normalizeColor(String? color) {
      return color?.replaceAll("#", "") ?? "#D63939";
    }

    return ThongTinKetQuaKiemTraModel(
      BienBanThamDinhATTPID: json['BienBanThamDinhATTPID'] as String?,
      NgayLap: json['NgayLap'] as String?,
      NhanVien_NguoiLap: json['NhanVien_NguoiLap'] as String?,
      ChucVu_NguoiLap: json['ChucVu_NguoiLap'] as String?,
      HinhThucThamDinh: json['HinhThucThamDinh'] as String?,
      MauBienBanID: json['MauBienBanID'] as String?,
      CoSoThamDinh: json['CoSoThamDinh'] as String?,
      DiaChi: json['DiaChi'] as String?,
      SoGPKD: json['SoGPKD'] as String?,
      NgayCapGPKD: json['NgayCapGPKD'] as String?,
      CoQuanCapGPKD: json['CoQuanCapGPKD'] as String?,
      SoDienThoai: json['SoDienThoai'] as String?,
      TenLoaiHinhCoSo: json['TenLoaiHinhCoSo'] as String?,
      HoVaTen_DaiDien: json['HoVaTen_DaiDien'] as String?,
      ChucVu_DaiDien: json['ChucVu_DaiDien'] as String?,
      LoaiHinhCoSo: json['LoaiHinhCoSo'] as String?,
      KetQuaDanhGia: json['KetQuaDanhGia'] as String?,
      NgayThamDinh: json['NgayThamDinh'] as String?,
      DaiDienDoanThamDinh: json['DaiDienDoanThamDinh'] as String?,
      YKienDoanThamDinh: json['YKienDoanThamDinh'] as String?,
      YKienCoSoThamDinh: json['YKienCoSoThamDinh'] as String?,
      ThuocLoai: json['ThuocLoai'] as String?,
      KetLuanKiemTra: json['KetLuanKiemTra'] as String?,
      ThongTinVeMauLay: json['ThongTinVeMauLay'] as String?,
      ChiDinhChiTieuPhanTich: json['ChiDinhChiTieuPhanTich'] as String?,
      DanhGiaThucHienCamKet: json['DanhGiaThucHienCamKet'] as String?,
      DeXuatKetQua: json['DeXuatKetQua'] as String?,
      NoiDungKhacPhuc: json['NoiDungKhacPhuc'] as String?,
      TrangThai: json['TrangThai'] as String?,
      PhamTramDat: json['PhamTramDat'] as int?,
      PhamTramNhe: json['PhamTramNhe'] as int?,
      PhamTramNang: json['PhamTramNang'] as int?,
      PhamTramNghiemTrong: json['PhamTramNghiemTrong'] as int?,
      PhamTramChuaDanhGia: json['PhamTramChuaDanhGia'] as int?,
      XepLoai: json['XepLoai'] as String?,
      DinhKem: json['DinhKem'] as String?,
      SoBienBan: json['SoBienBan'] as String?,
      KetQua: json['KetQua'] as String?,
      NoiDungKetQua: json['NoiDungKetQua'] as String?,
      TrangThaiPD: json['TrangThaiPD'] as String?,
      MauSac: normalizeColor(json['MauSac'] as String?),
      TotalResults: json['TotalResults'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'BienBanThamDinhATTPID': BienBanThamDinhATTPID,
      'NgayLap': NgayLap,
      'NhanVien_NguoiLap': NhanVien_NguoiLap,
      'ChucVu_NguoiLap': ChucVu_NguoiLap,
      'HinhThucThamDinh': HinhThucThamDinh,
      'MauBienBanID': MauBienBanID,
      'CoSoThamDinh': CoSoThamDinh,
      'DiaChi': DiaChi,
      'SoGPKD': SoGPKD,
      'NgayCapGPKD': NgayCapGPKD,
      'CoQuanCapGPKD': CoQuanCapGPKD,
      'SoDienThoai': SoDienThoai,
      'TenLoaiHinhCoSo': TenLoaiHinhCoSo,
      'HoVaTen_DaiDien': HoVaTen_DaiDien,
      'ChucVu_DaiDien': ChucVu_DaiDien,
      'LoaiHinhCoSo': LoaiHinhCoSo,
      'KetQuaDanhGia': KetQuaDanhGia,
      'NgayThamDinh': NgayThamDinh,
      'DaiDienDoanThamDinh': DaiDienDoanThamDinh,
      'ThongTinVeMauLay': ThongTinVeMauLay,
      'ChiDinhChiTieuPhanTich': ChiDinhChiTieuPhanTich,
      'DanhGiaThucHienCamKet': DanhGiaThucHienCamKet,
      'YKienDoanThamDinh': YKienDoanThamDinh,
      'YKienCoSoThamDinh': YKienCoSoThamDinh,
      'KetLuanKiemTra': KetLuanKiemTra,
      'TrangThai': TrangThai,
      'PhamTramDat': PhamTramDat,
      'PhamTramNhe': PhamTramNhe,
      'PhamTramNang': PhamTramNang,
      'PhamTramNghiemTrong': PhamTramNghiemTrong,
      'PhamTramChuaDanhGia': PhamTramChuaDanhGia,
      'DeXuatKetQua': DeXuatKetQua,
      'NoiDungKhacPhuc': NoiDungKhacPhuc,
      'ThuocLoai': ThuocLoai,
      'XepLoai': XepLoai,
      'DinhKem': DinhKem,
      'SoBienBan': SoBienBan,
      'KetQua': KetQua,
      'NoiDungKetQua': NoiDungKetQua,
      'TrangThaiPD': TrangThaiPD,
      'MauSac': MauSac,
      'TotalResults': TotalResults,
    };
  }
}
