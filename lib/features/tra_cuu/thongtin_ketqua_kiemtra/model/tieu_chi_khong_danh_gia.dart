// ignore_for_file: public_member_api_docs, sort_constructors_first



const String defaultImage = "https://via.placeholder.com/150";

class TieuChiKhongDanhGiaModel {
  final String? DieuKhoanThamChieu;
  final String? TenChiTieu;
  final String? STTSX;
  final String? STT;
  final String? BienBanThamDinhATTPChiTieuID;
  final String? LyDo;
  final String? BienBanThamDinhChiTieuID_Cha;

  TieuChiKhongDanhGiaModel({
    this.DieuKhoanThamChieu,
    this.TenChiTieu,
    this.STTSX,
    this.STT,
    this.BienBanThamDinhATTPChiTieuID,
    this.LyDo,
    this.BienBanThamDinhChiTieuID_Cha,
  });

  factory TieuChiKhongDanhGiaModel.fromJson(Map<String, dynamic> json) {
    return TieuChiKhongDanhGiaModel(
      DieuKhoanThamChieu: json['DieuKhoanThamChieu'] as String?,
      TenChiTieu: json['TenChiTieu'] as String?,
      STTSX: json['STTSX'] as String?,
      STT: json['STT'] as String?,
      BienBanThamDinhATTPChiTieuID:
          json['BienBanThamDinhATTPChiTieuID'] as String?,
      LyDo: json['LyDo'] as String?,
      BienBanThamDinhChiTieuID_Cha:
          json['BienBanThamDinhChiTieuID_Cha'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'DieuKhoanThamChieu': DieuKhoanThamChieu,
      'TenChiTieu': TenChiTieu,
      'STTSX': STTSX,
      'STT': STT,
      'BienBanThamDinhATTPChiTieuID': BienBanThamDinhATTPChiTieuID,
      'LyDo': LyDo,
      'BienBanThamDinhChiTieuID_Cha': BienBanThamDinhChiTieuID_Cha,
    };
  }
}
