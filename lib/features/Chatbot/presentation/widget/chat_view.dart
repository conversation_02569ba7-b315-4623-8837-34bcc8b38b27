import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/features/Chatbot/presentation/controller/chatbot_controller.dart';
import 'package:attp_2024/features/Chatbot/presentation/widget/markdown_message_widget.dart';
import 'package:attp_2024/features/Chatbot/presentation/widget/typing_indicator.dart';
import 'package:attp_2024/features/Chatbot/presentation/widget/welcome_message_widget.dart';
import 'package:attp_2024/features/Chatbot/presentation/widget/error_message.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:flutter_chat_ui/flutter_chat_ui.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mime/mime.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

class ChatViewWidget extends GetView<ChatBotController> {
  ChatViewWidget({
    Key? key,
  }) : super(key: key);

  final TextEditingController textController = TextEditingController();

  void _handleAttachmentPressed(BuildContext context) {
    showModalBottomSheet<void>(
      context: context,
      builder: (BuildContext context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                controller.handleImageSelection((msg) {
                  controller.addMessage(msg);
                });
              },
              child: const Text('Photo'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                controller.handleFileSelection((msg) {
                  controller.addMessage(msg);
                });
              },
              child: const Text('File'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      print("ChatView - Messages length: ${controller.messages.length}");
      //final messagesList = controller.messages.toList().reversed.toList();
      return Chat(
        // messages: messagesList,
        messageWidthRatio: 0.85,
        messages: controller.messages,
        onAttachmentPressed: () => _handleAttachmentPressed(context),
        onMessageTap: (_, message) => controller.handleMessageTap(message),
        onSendPressed: controller.handleSendPressed,
        showUserAvatars: true,
        showUserNames: true,
        user: controller.user,
        onEndReached: () => controller.loadChatHistory(),
        isLastPage: !controller.hasMoreMessages.value,
        theme: DefaultChatTheme(
          messageInsetsHorizontal: 12,
          messageInsetsVertical: 10,
          messageBorderRadius: 15,
          bubbleMargin: const EdgeInsets.fromLTRB(10.0, 5.0, 0.0, 0.0),
          primaryColor: AppColors.primary,
          typingIndicatorTheme: const TypingIndicatorTheme(
            animatedCirclesColor: Colors.blue,
            bubbleColor: Colors.grey,
            animatedCircleSize: 1.0,
            bubbleBorder: BorderRadius.zero,
            countAvatarColor: Colors.red,
            countTextColor: Colors.yellow,
            multipleUserTextStyle: TextStyle(),
          ),
        
          inputMargin: const EdgeInsets.fromLTRB(24, 20, 24, 20),
        ),
        customMessageBuilder: (message, {required messageWidth}) {
          print('Message metadata: ${message.metadata}');

          if (message.metadata?['type'] == 'welcome_message') {
            return const WelcomeMessageWidget();
          } else if (message.metadata?['type'] == 'typing_indicator') {
            return const TypingIndicatorMsg();
          } else if (message.metadata?['type'] == 'error_message') {
            return ErrorMessageWidget();
          }
          return const SizedBox();
        },
        customBottomWidget: _buildCustomInput(),
      );
    });
  }

  Widget _buildCustomInput() {
    return Container(
      padding: const EdgeInsets.fromLTRB(12, 12, 8, 16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 1,
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(32),
              ),
              padding: const EdgeInsets.all(4.0),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: textController,
                      decoration: InputDecoration(
                        hintText: 'Nhập thông tin bạn cần vào đây',
                        hintStyle: TextStyle(color: Colors.grey[400]),
                        border: InputBorder.none,
                        contentPadding:
                            const EdgeInsets.symmetric(horizontal: 16),
                        counterText: '',
                      ),
                      onChanged: (text) {
                        if (text.length <= 255) {
                          controller.messageText.value = text;
                        }
                      },
                      maxLines: null,
                      maxLength: 255,
                    ),
                  ),
                  Obx(() => IconButton(
                        icon: Icon(
                          Icons.send,
                          color: controller.messageText.trim().isNotEmpty
                              ? AppColors.primary
                              : Colors.grey[400],
                          size: 24,
                        ),
                        onPressed: controller.messageText.trim().isNotEmpty
                            ? () {
                                FocusManager.instance.primaryFocus?.unfocus();
                                controller.handleSendPressed(
                                  types.PartialText(
                                      text:
                                          controller.messageText.value.trim()),
                                );
                                controller.messageText.value = '';
                                textController.clear();
                              }
                            : null,
                      )),
                ],
              ),
            ),
          ),
          Obx(() => IconButton(
                icon: Icon(
                  controller.isListening.value ? Icons.mic : Icons.mic_none,
                  color: controller.isListening.value
                      ? Colors.red
                      : AppColors.primary,
                  size: 28,
                ),
                onPressed: () {
                  controller.handleVoiceMessage((msg) {
                    controller.addMessage(msg);
                  });
                },
              )),
        ],
      ),
    );
  }

  Widget _buildVoiceIndicator() {
    return Obx(() {
      return TweenAnimationBuilder<double>(
        tween: Tween<double>(
          begin: 1.0,
          end: controller.isListening.value ? 1.3 : 1.0,
        ),
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
        builder: (context, scale, child) {
          return Transform.scale(
            scale: scale,
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.red.withOpacity(0.2),
              ),
              child: const Icon(
                Icons.mic,
                color: Colors.red,
                size: 28,
              ),
            ),
          );
        },
        onEnd: () {
          if (controller.isListening.value) {
            _buildVoiceIndicator();
          }
        },
      );
    });
  }
}
