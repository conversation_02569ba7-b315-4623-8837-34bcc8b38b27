import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/features/Chatbot/presentation/controller/chatbot_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class WelcomeMessageWidget extends StatelessWidget {
  const WelcomeMessageWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeHeader(),
          const SizedBox(height: 16),
          _buildInfoMessages(),
          const SizedBox(height: 16),
          _buildOptions(),
        ],
      ),
    );
  }

  Widget _buildWelcomeHeader() {
    return Row(
      children: [
        Image.asset(
          AppImageString.chatbotImg1,
          width: 50,
          height: 50,
        ),
        const SizedBox(width: 12),
        const Expanded(
          child: Text(
            'Xin chào! Tôi là <PERSON>, trợ lý ảo của bạn. Tôi có thể giúp gì cho bạn?',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.darkGreen,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoMessages() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildInfoItem(
          '📋 Bạn có thể hỏi tôi về quy trình cấp giấy chứng nhận, danh sách cơ sở được cấp giấy chứng nhận, thông tin thẩm định định kỳ, và nhiều hơn nữa!',
        ),
        const SizedBox(height: 8),
        _buildInfoItem(
          '🔍 Hãy nhập câu hỏi của bạn, ví dụ: "Thông tin cơ sở sản xuất kinh doanh ABC", "Kết quả cấp giấy chứng nhận cơ sở ABC", ...',
        ),
      ],
    );
  }

  Widget _buildInfoItem(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: AppColors.gray3,
        ),
      ),
    );
  }

  Widget _buildOptions() {
    return Column(
      children: [
        _buildOptionButton(
            'Danh sách cơ sở được cấp giấy chứng nhận an toàn thực phẩm.'),
        const SizedBox(height: 8),
        _buildOptionButton('Danh sách các cơ sở vi phạm an toàn thực phẩm.'),
        const SizedBox(height: 8),
        _buildOptionButton('Thống kê cơ sở cấp giấy chứng nhận qua các năm.'),
      ],
    );
  }

  Widget _buildOptionButton(String text) {
    return InkWell(
      onTap: () {
        Get.find<ChatBotController>().handleOptionSelected(text);
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.darkGreen),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          text,
          style: TextStyle(color: AppColors.primary),
          textAlign: TextAlign.left,
        ),
      ),
    );
  }
}
