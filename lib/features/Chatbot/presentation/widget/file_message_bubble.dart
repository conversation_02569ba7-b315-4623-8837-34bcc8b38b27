import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:path_provider/path_provider.dart';
import 'package:open_filex/open_filex.dart';

class FileMessageBubble extends StatelessWidget {
  final types.FileMessage message;
  final Function(types.FileMessage) onDownload;

  const FileMessageBubble({
    Key? key,
    required this.message,
    required this.onDownload,
  }) : super(key: key);

  Future<bool> checkFileExists() async {
    String filePath;
    if (Platform.isAndroid) {
      final directory = Directory('/storage/emulated/0/Download');
      filePath = '${directory.path}/${message.name}';
    } else {
      final directory = await getApplicationDocumentsDirectory();
      filePath = '${directory.path}/${message.name}';
    }
    return await File(filePath).exists();
  }

  Future<void> openFile() async {
    String filePath;
    if (Platform.isAndroid) {
      final directory = Directory('/storage/emulated/0/Download');
      filePath = '${directory.path}/${message.name}';
    } else {
      final directory = await getApplicationDocumentsDirectory();
      filePath = '${directory.path}/${message.name}';
    }
    await OpenFilex.open(filePath);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool>(
      future: checkFileExists(),
      builder: (context, snapshot) {
        final fileExists = snapshot.data ?? false;

        return Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Icon dựa vào loại file
              Icon(
                message.mimeType?.contains('pdf') == true
                    ? Icons.picture_as_pdf
                    : Icons.table_chart,
                color: message.mimeType?.contains('pdf') == true
                    ? Colors.red
                    : Colors.green,
                size: 24,
              ),
              const SizedBox(width: 8),
              // Tên file
              Flexible(
                child: Text(
                  message.name,
                  style: const TextStyle(
                    fontSize: 14,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 8),
              // Nút tải về hoặc mở file
              if (fileExists)
                IconButton(
                  icon: const Icon(Icons.open_in_new, size: 20),
                  onPressed: openFile,
                  tooltip: 'Mở file',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                  ),
                )
              else
                IconButton(
                  icon: const Icon(Icons.download, size: 20),
                  onPressed: () => onDownload(message),
                  tooltip: 'Tải về',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}