import 'package:flutter/material.dart';

class ErrorMessageWidget extends StatelessWidget {
  // Thông điệp lỗi cố định
  final String errorMessage =
      'Bạn đã đạt giới hạn 20 tin nhắn trong ngày hôm nay. Vui lòng thử lại vào ngày mai';

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      margin: EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: Colors.red,
          width: 2,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(
            Icons.warning_amber_outlined,
            color: Colors.red,
            size: 24,
          ),
          SizedBox(width: 12),
          Expanded(
            child: Text(
              errorMessage,
              style: TextStyle(
                color: Colors.red,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
