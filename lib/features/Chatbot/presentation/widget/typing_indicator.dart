import 'package:flutter/material.dart';

class TypingIndicatorMsg extends StatefulWidget {
  const TypingIndicatorMsg({Key? key}) : super(key: key);

  @override
  State<TypingIndicatorMsg> createState() => _TypingIndicatorMsgState();
}

class _TypingIndicatorMsgState extends State<TypingIndicatorMsg>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              return Row(
                children: List.generate(3, (index) {
                  return Container(
                    margin: const EdgeInsets.symmetric(horizontal: 2),
                    height: 8,
                    width: 8,
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(
                        (1 - (_controller.value - index / 3).abs() * 2).clamp(0.2, 1),
                      ),
                      shape: BoxShape.circle,
                    ),
                  );
                }),
              );
            },
          ),
        ],
      ),
    );
  }
}