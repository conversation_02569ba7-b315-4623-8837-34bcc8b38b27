import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:attp_2024/core/ui/widgets/chart/custom_bar_chart.dart';

class MarkdownMessageWidget extends StatelessWidget {
  final String markdownContent;

  const MarkdownMessageWidget({
    Key? key,
    required this.markdownContent,
  }) : super(key: key);

  bool isMermaidChart() {
    final content = markdownContent.trim().toLowerCase();
    return content.startsWith('mermaid') &&
           (content.contains('bar') || content.contains('pie') || content.contains('graph'));
  }

  @override
  Widget build(BuildContext context) {
    if (isMermaidChart()) {
      if (markdownContent.contains('bar')) {
        return _buildBarChart();
      }
      // <PERSON><PERSON> thể thêm các loại biểu đồ khác
      return const Text('<PERSON>ại biểu đồ chưa được hỗ trợ');
    }

    if (markdownContent.isEmpty) {
      return const Center(child: Text('Không có nội dung'));
    }

    // Xử lý markdown thông thường
    return Container(
      padding: const EdgeInsets.all(16),
      child: Markdown(
        data: markdownContent,
        selectable: true,
        styleSheet: MarkdownStyleSheet(
          p: const TextStyle(fontSize: 14),
          h1: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          h2: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          h3: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          code: const TextStyle(
            backgroundColor: Colors.grey,
            fontFamily: 'monospace',
          ),
          codeblockDecoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
          ),
          blockquote: const TextStyle(
            color: Colors.grey,
            fontStyle: FontStyle.italic,
          ),
          tableHead: const TextStyle(fontWeight: FontWeight.bold),
          tableBody: const TextStyle(fontSize: 13),
        ),
        onTapLink: (text, href, title) async {
          if (href != null) {
            final uri = Uri.parse(href);
            if (await canLaunchUrl(uri)) {
              await launchUrl(uri);
            }
          }
        },
        builders: {

        },
      ),
    );
  }

  Widget _buildPieChart() {
    final lines = markdownContent.split('\n');
    String title = '';
    final Map<String, double> data = {};

    // Parse dữ liệu từ markdown
    for (var line in lines) {
      line = line.trim();
      if (line.startsWith('title')) {
        title = line.substring(5).trim();
      } else if (line.contains(':')) {
        final parts = line.split(':');
        if (parts.length == 2) {
          String key = parts[0].trim().replaceAll('"', '');
          double value = double.tryParse(parts[1].trim()) ?? 0;
          data[key] = value;
        }
      }
    }

    // Tạo sections cho PieChart
    final sections = data.entries.map((entry) {
      return PieChartSectionData(
        value: entry.value,
        title: '${entry.key}\n(${entry.value})',
        color: Colors.primaries[data.keys.toList().indexOf(entry.key) % Colors.primaries.length],
        radius: 100,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();

    return Container(
      padding: const EdgeInsets.all(16),
      height: 400,
      child: Column(
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: PieChart(
              PieChartData(
                sections: sections,
                sectionsSpace: 2,
                centerSpaceRadius: 0,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBarChart() {
    final lines = markdownContent.split('\n');
    String title = '';
    final List<String> labels = [];
    final List<double> data = [];

    // Parse dữ liệu từ nội dung Mermaid
    for (var line in lines) {
      line = line.trim();
      if (line.startsWith('title')) {
        title = line.substring(5).trim();
      } else if (line.contains(':')) {
        final parts = line.split(':');
        if (parts.length == 2) {
          String label = parts[0].trim();
          // Loại bỏ các ký tự đặc biệt và khoảng trắng thừa
          label = label.replaceAll(RegExp(r'["\(\)]'), '').trim();
          labels.add(label);

          // Parse giá trị số
          double value = double.tryParse(parts[1].trim()) ?? 0;
          data.add(value);
        }
      }
    }

    // Tạo groupValues cho GroupedBarChart
    final List<List<double>> groupValues = [data];

    return Container(
      padding: const EdgeInsets.all(16),
      height: 400,
      child: GroupedBarChart(
        title: title,
        labels: labels,
        groupValues: groupValues,
        groupColors: const [Colors.blue], // Một màu vì chỉ có 1 nhóm dữ liệu
        groupNames: const ['Số lượng'], // Tên nhóm dữ liệu
        onTapGroup: (label, groupIndex) {
          // Xử lý sự kiện khi người dùng tap vào cột
          print('Tapped on: $label at index $groupIndex');
        },
      ),
    );
  }
}

// Custom builder cho bảng
