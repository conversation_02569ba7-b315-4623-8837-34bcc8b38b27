import 'package:flutter/material.dart';
import 'package:get/get.dart';

class DownloadProgressWidget extends StatelessWidget {
  final double progress;
  final bool isDownloading;

  const DownloadProgressWidget({
    super.key,
    required this.progress,
    required this.isDownloading,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      opacity: isDownloading ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 200),
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.black54,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 40,
              height: 40,
              child: CircularProgressIndicator(
                value: progress,
                backgroundColor: Colors.grey[300],
                valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
                strokeWidth: 4,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${(progress * 100).toInt()}%',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }
}