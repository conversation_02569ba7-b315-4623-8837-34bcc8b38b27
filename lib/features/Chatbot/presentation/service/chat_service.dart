import 'dart:async';
import 'package:attp_2024/core/services/auth_use_case/auth_use_case.dart';
import 'package:attp_2024/features/Chatbot/model/request/chat_request.dart';
import 'package:attp_2024/features/Chatbot/model/response/chat_history_response.dart';
import 'package:attp_2024/features/Chatbot/model/response/chat_response.dart';
import 'package:attp_2024/features/Chatbot/model/response/mock.dart';
import 'package:dio/dio.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:logger/logger.dart';

class ChatService {
  late Dio _dio;
  final logger = Logger();

  ChatService() {
    _initializeDio();
  }

  void _initializeDio() async {
    final baseurlApi = dotenv.env['APICHAT'] ?? '';
    //const baseurlApi =  'http://localhost:8000';
    //print('baseurlApi: $baseurlApi');
    final token = await AuthUseCase.getTokenMemory();
    //print("baseUrl_API: $baseurlApi");
    //print("token: $token");
    _dio = Dio(BaseOptions(
      baseUrl: baseurlApi,
      connectTimeout: const Duration(seconds: 60),
      receiveTimeout: const Duration(seconds: 120),
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer ${token.replaceAll('"', '')}",
      },
    ));

    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        logger.d(
            'Request: ${options.method} ${options.path}${options.queryParameters}');
        logger.d('Headers: ${options.headers}');
        return handler.next(options);
      },
      onResponse: (response, handler) {
        logger.d('Response: ${response.data}');
        return handler.next(response);
      },
      onError: (DioException error, handler) {
        logger.e('Error: ${error.message}');
        return handler.next(error);
      },
    ));
  }

  void updateToken(String token) {
    if (token.isNotEmpty) {
      _dio.options.headers["Authorization"] = "Bearer $token";
      logger.i('Token updated successfully');
    }
  }

  Future<ChatResponse> sendMessage(ChatRequest request) async {
    try {
      logger
          .i("ChatService - Sending request with message: ${request.message}");

      final response = await _dio.post(
        '/chat',
        queryParameters: {
          'message': request.message,
        },
      ).timeout(
        const Duration(seconds: 120),
        onTimeout: () {
          logger.e("ChatService - Timeout error: Request took too long!");
          throw TimeoutException('Request timed out');
        },
      );

      logger.i("ChatService - Response received: ${response.data}");

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['success'] != true) {
          return ChatResponse(
            message: 'Xin lỗi, tôi không thể xử lý yêu cầu này!',
            success: false,
            type: 'text',
            url: '',
          );
        }

        final chatResponse = ChatResponse.fromJson(responseData);
        return chatResponse;
      } else {
        throw Exception('Failed to send message: ${response.statusCode}');
      }
    } catch (e) {
      logger.e("ChatService - Error: $e");
      return ChatResponse(
        message: 'Xin lỗi, tôi không thể xử lý yêu cầu này!',
        success: false,
        type: 'text',
        url: '',
      );
    }
  }

  Future<ChatHistoryResponse> getChatHistory(String username) async {
    try {
      logger.i("ChatService - Getting chat history for user: $username");

      // Sử dụng mock data thay vì gọi API
      //return ChatHistoryResponse.getMockData();

      final para = username;
      final response = await _dio.get('/chat-history/$para');

      if (response.statusCode == 200) {
        return ChatHistoryResponse.fromJson(response.data);
      } else {
        throw Exception("Failed to get chat history: ${response.statusCode}");
      }
    } catch (e) {
      logger.e("ChatService - Error getting history: $e");
      throw e;
    }
  }

  Future<ChatHistoryResponse> getMockChatHistory() async {
    await Future.delayed(const Duration(seconds: 1));
    return ChatHistoryResponse.fromJson(ChatMockData.getMockChatHistory());
  }

  Future<ChatResponse> getMockChatResponse() async {
    await Future.delayed(const Duration(seconds: 1));
    return ChatResponse.fromJson(ChatMockData.getMockFileResponse());
  }

  Future<ChatResponse> getMockErrorMsgResponse() async {
    await Future.delayed(const Duration(seconds: 1));
    return ChatResponse.fromJson(ChatMockData.getMockOverflowResponse());
  }

  Future<bool> deleteChatSession(String username, String sessionId) async {
    try {
      logger.i(
          "ChatService - Deleting chat session: $sessionId for user: $username");

      final response =
          await _dio.delete('/delete-chat-session/$username/$sessionId');

      if (response.statusCode == 200) {
        final responseData = response.data;
        return responseData['success'] ?? false;
      } else {
        throw Exception(
            "Failed to delete chat session: ${response.statusCode}");
      }
    } catch (e) {
      logger.e("ChatService - Error deleting chat session: $e");
      throw e;
    }
  }

  Future<ChatHistoryResponse> renewSession(String username) async {
    try {
      logger.i("ChatService - Creating new session for user: $username");

      final response = await _dio.post('/renew-session/$username');

      if (response.statusCode == 200) {
        return ChatHistoryResponse.fromJson(response.data);
      } else {
        throw Exception("Failed to create new session: ${response.statusCode}");
      }
    } catch (e) {
      logger.e("ChatService - Error creating new session: $e");
      throw e;
    }
  }

  Future<ChatHistoryResponse> getChatHistoryFromDB(String username) async {
    try {
      logger
          .i("ChatService - Getting chat history from DB for user: $username");

      final response = await _dio.get('/chat-history-db/$username');

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['success'] == true) {
          return ChatHistoryResponse.fromJson(responseData);
        } else {
          throw Exception("API returned success: false");
        }
      } else {
        throw Exception(
            "Failed to get chat history from DB: ${response.statusCode}");
      }
    } catch (e) {
      logger.e("ChatService - Error getting history from DB: $e");
      throw e;
    }
  }
}
