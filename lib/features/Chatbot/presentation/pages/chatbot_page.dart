import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/routes/routes.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:attp_2024/features/Chatbot/presentation/controller/chatbot_controller.dart';
import 'package:attp_2024/features/Chatbot/presentation/pages/chat_history_page.dart';
import 'package:attp_2024/features/Chatbot/presentation/widget/chat_view.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ChatBotPage extends GetView<ChatBotController> {
  const ChatBotPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0.0,
        backgroundColor: AppColors.primary,
        centerTitle: false,
        titleSpacing: 20.0,
        toolbarHeight: 62.0,
        title: const Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: EdgeInsets.only(right: 8.0),
              child: Image(
                image: AssetImage('assets/images/TroLyAo_SoFiGIF.gif'),
                height: 42,
                width: 42,
              ),
            ),
            Text(
              "Trợ lý ảo NTSOFT SOFI",
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: AppDimens.textAppBarSize,
              ),
            ),
          ],
        ),
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios_new,
            color: Colors.white,
            size: 20,
          ),
          onPressed: () => Get.back(),
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.fromLTRB(6.0, 6.0, 6.0, 6.0),
            child: PopupMenuButton<int>(
              icon: const Icon(
                Icons.more_vert,
                color: Colors.white,
              ),
              offset: const Offset(0, 50),
              onSelected: (value) {
                if (value == 1) {
                  controller.loadChatHistory();
                  Get.toNamed(Routes.chatbotHistory);
                } else if (value == 2) {
                  controller.createNewSession();
                }
              },
              padding: EdgeInsets.zero,
              itemBuilder: (context) => [
                PopupMenuItem<int>(
                  value: 1,
                  child: Row(
                    children: [
                      Icon(Icons.history, color: AppColors.primary),
                      const SizedBox(width: 8),
                      const Text("Lịch sử chat"),
                    ],
                  ),
                ),
                PopupMenuItem<int>(
                  value: 2,
                  child: Row(
                    children: [
                      Icon(Icons.add_circle_outline, color: AppColors.primary),
                      const SizedBox(width: 8),
                      const Text("Tạo mới chat"),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      body: ChatViewWidget(),
    );
  }
}
