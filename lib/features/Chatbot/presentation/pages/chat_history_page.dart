import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/snackbar/snackbar_until.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:attp_2024/features/Chatbot/model/response/chat_history_response.dart';
import 'package:attp_2024/features/Chatbot/presentation/controller/chatbot_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class ChatHistoryPage extends GetView<ChatBotController> {
  const ChatHistoryPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppBarWidget(
        title: '<PERSON><PERSON><PERSON> sử',
        centerTitle: true,
        // actions: [
        //   Padding(
        //     padding: const EdgeInsets.only(right: 8.0),
        //     child: IconButton(
        //       onPressed: () => Get.to(() => const ChatHistoryPage()),
        //       //onPressed: null,
        //       icon: const Icon(
        //         Icons.add_circle_outline,
        //         color: Colors.white,
        //         size: 24,
        //       ),
        //     ),
        //   ),
        // ],
      ),

      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: SizedBox(
              height: 60,
              child: TextField(
                controller: controller.searchController,
                onChanged: (value) {
                  controller.filterChatHistory(value);
                },
                decoration: InputDecoration(
                  hintText: 'Tìm kiếm',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: Obx(() => controller.searchQuery.value.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            controller.clearSearch();
                          },
                        )
                      : const SizedBox.shrink()),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.grey[100],
                  contentPadding: const EdgeInsets.symmetric(vertical: 10),
                ),
              ),
            ),
          ),
          Expanded(
            child: Obx(() {
              if (controller.isLoadingHistory.value) {
                return const Center(child: CircularProgressIndicator());
              }
              
              if (controller.filteredChatSessions.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.search_off,
                        size: 64,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        controller.searchQuery.value.isEmpty
                            ? 'Không có cuộc trò chuyện nào'
                            : 'Không tìm thấy kết quả cho "${controller.searchQuery.value}"',
                        style: const TextStyle(
                          color: Colors.grey,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                );
              }
              
              return _buildChatHistoryList();
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildChatHistoryList() {
    final today = DateTime.now();
    final todayStart = DateTime(today.year, today.month, today.day);
    final weekAgoStart = todayStart.subtract(const Duration(days: 7));

    final todayChats = <Message>[];
    final weekChats = <Message>[];
    final olderChats = <Message>[];

    for (var message in controller.filteredChatSessions) {
      final timestamp = DateTime.parse(message.sentAt);
      if (timestamp.isAfter(todayStart)) {
        todayChats.add(message);
      } else if (timestamp.isAfter(weekAgoStart)) {
        weekChats.add(message);
      } else {
        olderChats.add(message);
      }
    }

    return ListView(
      children: [
        if (todayChats.isNotEmpty) ...[
          const Padding(
            padding: EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 8.0),
            child: Text(
              'Hôm nay',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ...todayChats.map((chat) => _buildHistoryItem(chat)),
        ],

        if (weekChats.isNotEmpty) ...[
          const Padding(
            padding: EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 8.0),
            child: Text(
              '7 ngày gần đây',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ...weekChats.map((chat) => _buildHistoryItem(chat)),
        ],
      ],
    );
  }

  Widget _buildHistoryItem(Message chat) {
    final time = DateFormat('HH:mm').format(DateTime.parse(chat.sentAt));

    return Dismissible(
      key: Key(chat.sessionId.toString()),
      direction: DismissDirection.endToStart,
      background: Container(
        color: Colors.red,
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: const Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              'Vuốt để xóa',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(width: 8),
            Icon(Icons.delete, color: Colors.white),
          ],
        ),
      ),
      onDismissed: (direction) {
        controller.deleteChatSession(chat.sessionId);
        SnackbarUtil.showSuccess("Đã xóa cuộc trò chuyện", alignment: 'bottom');
      },
      child: Container(
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Colors.grey.withOpacity(0.2),
              width: 1,
            ),
          ),
        ),
        child: ListTile(
          leading: const Icon(Icons.chat_bubble_outline),
          title: Text(
            chat.message,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          subtitle: Row(
            children: [
              Text(time),
              const SizedBox(width: 8),
              const Icon(
                Icons.swipe_left,
                size: 14,
                color: Colors.grey,
              ),
              const Text(
                ' Vuốt sang trái để xóa',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
          trailing: const Icon(Icons.chevron_right),
          onTap: () => controller.openChatSession(chat.sessionId),
        ),
      ),
    );
  }
}