import 'dart:convert';
import 'dart:io';
import 'package:attp_2024/core/data/models/user_access_model.dart';
import 'package:attp_2024/core/routes/routes.dart';
import 'package:attp_2024/core/services/user_use_case.dart';
import 'package:attp_2024/core/ui/snackbar/snackbar_until.dart';
import 'package:attp_2024/features/Chatbot/model/request/chat_request.dart';
import 'package:attp_2024/features/Chatbot/model/response/chat_history_response.dart';
import 'package:attp_2024/features/Chatbot/presentation/service/chat_service.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mime/mime.dart';
import 'package:uuid/uuid.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:url_launcher/url_launcher.dart';
import 'package:dio/dio.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart' as perm;
import 'package:path/path.dart' as p;

class ChatBotController extends GetxController {
  final String msg_pdf =
      "Chào bạn! Mình là NTSoft Sofi, rất vui được hỗ trợ bạn. Mình đã tạo file pdf theo yêu cầu rồi đây";
  final String msg_chart =
      "Chào bạn! Mình là NTSoft Sofi, rất vui được hỗ trợ bạn. Mình đã tạo biểu đồ theo yêu cầu rồi đây";
  final String msg_excel =
      "Chào bạn! Mình là NTSoft Sofi, rất vui được hỗ trợ bạn. Mình đã tạo excel theo yêu cầu rồi đây";
  final RxList<types.Message> messages = <types.Message>[].obs;
  final user = const types.User(
    id: 'user',
    firstName: 'User',
    imageUrl: 'assets/images/avatar.png',
  );

  final botUser = const types.User(
    id: 'bot',
    firstName: 'NTSOFT SOFI',
    imageUrl: 'assets/images/SOFI.png',
  );

  final ChatService _chatService = ChatService();
  final String _sessionId = const Uuid().v4();
  final stt.SpeechToText _speech = stt.SpeechToText();
  RxBool isListening = false.obs;
  RxString recognizedText = ''.obs;
  RxDouble decibels = 0.0.obs;
  final isTyping = false.obs;
  UserAccessModel? userAccessModel;

  final isLoadingHistory = false.obs;
  final hasMoreMessages = true.obs;
  int currentPage = 1;
  final int perPage = 20;

  final chatSessions = <Message>[].obs;
  final filteredChatSessions = <Message>[].obs;
  final searchQuery = ''.obs;

  final RxDouble downloadProgress = 0.0.obs;
  final RxBool isDownloading = false.obs;
  final RxString currentDownloadFile = ''.obs;

  //final notifications.FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = notifications.FlutterLocalNotificationsPlugin();

  // Thêm TextEditingController
  late TextEditingController searchController;

  @override
  void onInit() async {
    super.onInit();
    searchController = TextEditingController();
    _initSpeech();
    messages.value = [];
    _addWelcomeMessage();
    await loadInfoProfile();
    print("SESSION START");
    createNewSession();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  Future<void> loadInfoProfile() async {
    userAccessModel = await UserUseCase.getUser();
    update(["bodyID"]);
  }

  Future<void> _initSpeech() async {
    bool available = await _speech.initialize(
      onStatus: (status) {
        print('Speech status: $status');
        if (status == 'done' || status == 'notListening') {
          isListening.value = false;
        }
      },
      onError: (errorNotification) => print('Speech error: $errorNotification'),
    );
    print('Speech available: $available');
  }

  void addMessage(types.Message message) {
    print("Adding message: ${message.toJson()}");
    messages.insert(0, message);
    update();
  }

  Future<void> handleFileSelection(
      Function(types.Message) onMessageAdded) async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.any,
    );

    if (result != null && result.files.single.path != null) {
      final message = types.FileMessage(
        author: user,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        id: const Uuid().v4(),
        mimeType: lookupMimeType(result.files.single.path!),
        name: result.files.single.name,
        size: result.files.single.size,
        uri: result.files.single.path!,
      );

      onMessageAdded(message);
    }
  }

  Future<void> handleImageSelection(
      Function(types.Message) onMessageAdded) async {
    final result = await ImagePicker().pickImage(
      imageQuality: 70,
      maxWidth: 1440,
      source: ImageSource.gallery,
    );

    if (result != null) {
      final bytes = await result.readAsBytes();
      final image = await decodeImageFromList(bytes);

      final message = types.ImageMessage(
        author: user,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        height: image.height.toDouble(),
        id: const Uuid().v4(),
        name: result.name,
        size: bytes.length,
        uri: result.path,
        width: image.width.toDouble(),
      );

      onMessageAdded(message);
    }
  }

  Future<void> handleVoiceMessage(
      Function(types.Message) onMessageAdded) async {
    try {
      if (!_speech.isAvailable) {
        print('Speech recognition not available');
        Get.snackbar(
          'Thông báo',
          'Thiết bị của bạn không hỗ trợ nhận dạng giọng nói',
          snackPosition: SnackPosition.BOTTOM,
        );
        return;
      }

      if (!isListening.value) {
        isListening.value = true;
        try {
          await _speech.listen(
            localeId: 'vi_VN',
            onResult: (result) {
              recognizedText.value = result.recognizedWords;
              print("Recognized text: ${result.recognizedWords}");
              if (result.finalResult) {
                isListening.value = false;
                String text = result.recognizedWords;
                if (text.isNotEmpty) {
                  handleSendPressed(types.PartialText(text: text));
                  // Get.snackbar(
                  //   'Thành công',
                  //   'Đã nhận dạng: "$text"',
                  //   snackPosition: SnackPosition.BOTTOM,
                  //   backgroundColor: Colors.green.withOpacity(0.1),
                  // );
                }
              }
            },
            onSoundLevelChange: (level) {
              decibels.value = level * 100;
              print("Sound level: ${decibels.value}");
            },
            cancelOnError: true,
          );
        } catch (e) {
          print("Error during speech recognition: $e");
          isListening.value = false;
          Get.snackbar(
            'Lỗi',
            'Không thể bắt đầu nhận dạng giọng nói: $e',
            snackPosition: SnackPosition.BOTTOM,
          );
        }
      } else {
        isListening.value = false;
        decibels.value = 0;
        await _speech.stop();
        print("Speech recognition stopped");
      }
    } catch (e) {
      print("General error: $e");
      isListening.value = false;
      decibels.value = 0;
      Get.snackbar(
        'Lỗi',
        'Đã xảy ra lỗi: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> handleMessageTap(types.Message message) async {
    if (message is types.FileMessage) {
      final downloadUrl = message.metadata?['downloadUrl'] as String?;
      if (downloadUrl == null) {
        SnackbarUtil.showError('Không tìm thấy link tải file.',
            alignment: 'bottom');
        return;
      }

      try {
        isDownloading.value = true;
        downloadProgress.value = 0.0;
        currentDownloadFile.value = p.basename(downloadUrl);

        final isDownloadable =
            message.metadata?['isDownloadable'] as bool? ?? false;

        if (isDownloadable) {
          // Tạo đường dẫn file
          String filePath;
          if (Platform.isAndroid) {
            final directory = Directory('/storage/emulated/0/Download');
            if (!await directory.exists()) {
              await directory.create(recursive: true);
            }
            filePath = '${directory.path}/${message.name}';
          } else {
            final directory = await getApplicationDocumentsDirectory();
            filePath = '${directory.path}/${message.name}';
          }

          // Tải file bằng Dio
          final response = await Dio().download(
            downloadUrl,
            filePath,
            onReceiveProgress: (received, total) {
              if (total != -1) {
                downloadProgress.value = received / total;
              }
            },
          );

          SnackbarUtil.showSuccess("Tệp đã được tải xuống thành công",
              alignment: 'bottom');
          //await _showNotification('Tải tệp thành công', 'Tệp đã được tải xuống: ${message.name}');
          await OpenFilex.open(filePath);
        } else {
          final url = Uri.parse(downloadUrl);
          if (await canLaunchUrl(url)) {
            await launchUrl(url);
            Get.snackbar(
              'Thông báo',
              'Đang mở file trong trình duyệt',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: Colors.blue.withOpacity(0.1),
            );
          } else {
            throw Exception('Không thể mở file');
          }
        }
      } catch (e) {
        print('Error handling file: $e');
        SnackbarUtil.showError('Không thể xử lý file: $e', alignment: 'bottom');
        //await _showNotification('Lỗi tải file', 'Không thể tải file: $e');
      } finally {
        isDownloading.value = false;
        downloadProgress.value = 0.0;
        currentDownloadFile.value = '';
      }
    }
  }

  Future<void> loadMessages() async {
    try {
      final response = await rootBundle.loadString('assets/messages.json');
      final loadedMessages = (jsonDecode(response) as List)
          .map((e) => types.Message.fromJson(e as Map<String, dynamic>))
          .toList();
      messages.value = loadedMessages;
    } catch (e) {
      print('Error loading messages: $e');
    }
  }

  var messageText = ''.obs;
  Future<void> handleSendPressed(types.PartialText message) async {
    if (message.text.trim().isEmpty) return;

    final textMessage = types.TextMessage(
      author: user,
      createdAt: DateTime.now().millisecondsSinceEpoch,
      id: const Uuid().v4(),
      text: message.text,
    );

    addMessage(textMessage);

    final indicatorMessage = types.CustomMessage(
      author: botUser,
      createdAt: DateTime.now().millisecondsSinceEpoch,
      id: const Uuid().v4(),
      metadata: {
        'type': 'typing_indicator',
      },
    );
    addMessage(indicatorMessage);

    try {
      final request = ChatRequest(
        message: message.text,
        sessionId: _sessionId,
      );

      final response = await _chatService.sendMessage(request);
      // final response = await _chatService.getMockChatResponse();
      final baseurlApi = dotenv.env['APICHAT'] ?? '';

      messages.removeWhere((msg) => msg.id == indicatorMessage.id);

      types.Message botMessage;

      if (!response.success) {
        botMessage = types.TextMessage(
          author: botUser,
          createdAt: DateTime.now().millisecondsSinceEpoch,
          id: const Uuid().v4(),
          text: response.message,
        );
        addMessage(botMessage);
        return;
      }

      switch (response.type) {
        case 'text':
          botMessage = types.TextMessage(
            author: botUser,
            createdAt: DateTime.now().millisecondsSinceEpoch,
            id: const Uuid().v4(),
            text: response.message,
          );
          break;

        case 'overflow':
          botMessage = types.CustomMessage(
            author: botUser,
            createdAt: DateTime.now().millisecondsSinceEpoch,
            id: const Uuid().v4(),
            metadata: {
              'type': 'error_message',
            },
          );
          break;

        case 'img':
          final url =
              response.url.startsWith('/') ? response.url : '/${response.url}';
          final fullUrl = '$baseurlApi$url';
          final textMessage = types.TextMessage(
            author: botUser,
            createdAt: DateTime.now().millisecondsSinceEpoch,
            id: const Uuid().v4(),
            text: msg_chart,
          );
          addMessage(textMessage);
          // Nếu có message, hiển thị message trước
          // if (response.message.isNotEmpty) {
          // final textMessage = types.TextMessage(
          //   author: botUser,
          //   createdAt: DateTime.now().millisecondsSinceEpoch,
          //   id: const Uuid().v4(),
          //   text: response.message,
          // );
          //   addMessage(textMessage);
          // }

          final imgMessage = types.ImageMessage(
            author: botUser,
            createdAt: DateTime.now().millisecondsSinceEpoch,
            id: const Uuid().v4(),
            name: 'image.jpg',
            size: 0,
            uri: fullUrl,
            width: 300,
            height: 00,
            metadata: {
              'downloadUrl': fullUrl,
              'isDownloadable': true,
              // 'marginTop': 10,
              // 'marginBottom': 10,
            },
          );
          addMessage(imgMessage);
          return;
        case 'pdf':
        case 'excel':
          final textMessage = types.TextMessage(
            author: botUser,
            createdAt: DateTime.now().millisecondsSinceEpoch,
            id: const Uuid().v4(),
            text: response.type.toString().toLowerCase() == 'pdf'
                ? msg_pdf
                : msg_excel,
          );
          addMessage(textMessage);
          if (response.message.isNotEmpty) {
            botMessage = types.TextMessage(
              author: botUser,
              createdAt: DateTime.now().millisecondsSinceEpoch,
              id: const Uuid().v4(),
              text: response.message,
            );
            break;
          }

          final url =
              response.url.startsWith('/') ? response.url : '/${response.url}';
          final fullUrl = '$baseurlApi$url';

          botMessage = response.type == 'img'
              ? types.ImageMessage(
                  author: botUser,
                  createdAt: DateTime.now().millisecondsSinceEpoch,
                  id: const Uuid().v4(),
                  name: 'image.jpg',
                  size: 0,
                  uri: fullUrl,
                  width: 300,
                  height: 200,
                  metadata: {
                    'downloadUrl': fullUrl,
                    'isDownloadable': true,
                    'marginTop': 30,
                    'marginBottom': 10,
                  },
                )
              : types.FileMessage(
                  author: botUser,
                  createdAt: DateTime.now().millisecondsSinceEpoch,
                  id: const Uuid().v4(),
                  name: response.type == 'pdf' ? 'file.pdf' : 'file.xlsx',
                  size: response.fileSize ?? 0,
                  uri: fullUrl,
                  mimeType: response.type == 'pdf'
                      ? 'application/pdf'
                      : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                  metadata: {
                    'type': response.type,
                    'fileType': response.type.toUpperCase(),
                    'isDownloadable': true,
                    'downloadUrl': fullUrl,
                    'hideSize': true,
                  },
                );
          break;
        default:
          botMessage = types.TextMessage(
            author: botUser,
            createdAt: DateTime.now().millisecondsSinceEpoch,
            id: const Uuid().v4(),
            text: response.message,
          );
      }

      addMessage(botMessage);
    } catch (e) {
      print('Error sending message: $e');
      // Xóa tin nhắn indicator trong trường hợp lỗi
      messages.removeWhere((msg) => msg.id == indicatorMessage.id);
      // Thêm tin nhắn lỗi từ bot
      final errorMessage = types.TextMessage(
        author: botUser,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        id: const Uuid().v4(),
        text: 'Xin lỗi, tôi không thể xử lý yêu cầu này!',
      );
      addMessage(errorMessage);
    }

    messageText.value = '';
  }

  void _addWelcomeMessage() {
    final welcomeMessage = types.CustomMessage(
      author: botUser,
      createdAt: DateTime.now().millisecondsSinceEpoch,
      id: const Uuid().v4(),
      metadata: const {
        'type': 'welcome_message',
      },
    );
    addMessage(welcomeMessage);

    // final errorMessage = types.CustomMessage(
    //   author: botUser,
    //   createdAt: DateTime.now().millisecondsSinceEpoch,
    //   id: const Uuid().v4(),
    //   metadata: {
    //     'type': 'error_message',
    //   },
    // );
    // addMessage(errorMessage);
    // Thêm tin nhắn hình ảnh
    // final imageMessage = types.ImageMessage(
    //   author: botUser,
    //   createdAt: DateTime.now().millisecondsSinceEpoch,
    //   id: const Uuid().v4(),
    //   name: 'image.jpg',
    //   size: 0,
    //   uri: 'https://picsum.photos/id/237/200/300',
    //   width: 200,
    //   height: 300,
    //   metadata: {
    //     'downloadUrl': 'https://picsum.photos/id/237/200/300',
    //     'isDownloadable': true,
    //     'marginTop': 30,
    //     'marginBottom': 10,
    //   },
    // );
    // addMessage(imageMessage);

    // final excelMessage = types.FileMessage(
    //   author: botUser,
    //   createdAt: DateTime.now().millisecondsSinceEpoch,
    //   id: const Uuid().v4(),
    //   name: 'capbudg.xls',
    //   size: 9999999,
    //   uri: 'https://exinfm.com/excel%20files/capbudg.xls',
    //   mimeType: 'application/vnd.ms-excel',
    //   metadata: const {
    //     'type': 'excel',
    //     'fileType': 'Excel',
    //     'isDownloadable': true,
    //     'downloadUrl': 'https://exinfm.com/excel%20files/capbudg.xls',
    //     'hideSize': false,
    //   },z
    // );
    // addMessage(excelMessage);

    // Thêm tin nhắn PDF với tùy chọn tải xuống
    // final pdfMessage = types.FileMessage(
    //   author: botUser,
    //   createdAt: DateTime.now().millisecondsSinceEpoch,
    //   id: const Uuid().v4(),
    //   name: 'sample.pdf',
    //   size: 0,
    //   uri: 'https://pdfobject.com/pdf/sample.pdf',
    //   mimeType: 'application/pdf',
    //   metadata: {
    //     'type': 'pdf',
    //     'fileType': 'PDF',
    //     'isDownloadable': true,
    //     'downloadUrl': 'https://pdfobject.com/pdf/sample.pdf',
    //     'hideSize': true,
    //   },
    // );
    // addMessage(pdfMessage);
  }

  void handleOptionSelected(String option) async {
    final userMessage = types.TextMessage(
      author: user,
      createdAt: DateTime.now().millisecondsSinceEpoch,
      id: const Uuid().v4(),
      text: option,
    );

    addMessage(userMessage);

    // Thêm tin nhắn indicator từ bot
    final indicatorMessage = types.CustomMessage(
      author: botUser,
      createdAt: DateTime.now().millisecondsSinceEpoch,
      id: const Uuid().v4(),
      metadata: {
        'type': 'typing_indicator',
      },
    );
    addMessage(indicatorMessage);

    try {
      final request = ChatRequest(
        message: option,
        sessionId: _sessionId,
      );

      final response = await _chatService.sendMessage(request);
      final baseurlApi = dotenv.env['APICHAT'] ?? '';

      messages.removeWhere((msg) => msg.id == indicatorMessage.id);

      types.Message botMessage;

      if (response.success) {
        switch (response.type) {
          case 'text':
            botMessage = types.TextMessage(
              author: botUser,
              createdAt: DateTime.now().millisecondsSinceEpoch,
              id: const Uuid().v4(),
              text: response.message,
            );
            break;

          case 'img':
            final url = response.url.startsWith('/')
                ? response.url
                : '/${response.url}';
            final fullUrl = '$baseurlApi$url';
            botMessage = types.ImageMessage(
              author: botUser,
              createdAt: DateTime.now().millisecondsSinceEpoch,
              id: const Uuid().v4(),
              name: 'image',
              size: 0,
              uri: fullUrl,
              width: double.infinity,
              height: double.infinity,
              metadata: {
                'downloadUrl': fullUrl,
                'isDownloadable': true,
                'marginTop': 30,
                'marginBottom': 10,
              },
            );
            break;

          case 'pdf':
          case 'excel':
            final url = response.url.startsWith('/')
                ? response.url
                : '/${response.url}';
            final fullUrl = '$baseurlApi$url';
            final fileType = response.type == 'pdf' ? 'PDF' : 'Excel';
            final extension = response.type == 'pdf' ? '.pdf' : '.xlsx';

            botMessage = types.FileMessage(
              author: botUser,
              createdAt: DateTime.now().millisecondsSinceEpoch,
              id: const Uuid().v4(),
              name: 'file$extension',
              size: 0,
              uri: fullUrl,
              mimeType: response.type == 'pdf'
                  ? 'application/pdf'
                  : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
              metadata: {
                'type': response.type,
                'fileType': fileType,
                'isDownloadable': true,
                'downloadUrl': fullUrl,
                'hideSize': true,
              },
            );
            break;

          default:
            botMessage = types.TextMessage(
              author: botUser,
              createdAt: DateTime.now().millisecondsSinceEpoch,
              id: const Uuid().v4(),
              text: response.message,
            );
        }
        addMessage(botMessage);
      }
    } catch (e) {
      print('Error sending message: $e');
      // Xóa tin nhắn indicator trong trường hợp lỗi
      messages.removeWhere((msg) => msg.id == indicatorMessage.id);
    }
  }

  Future<void> loadChatHistory() async {
    try {
      isLoadingHistory.value = true;
      final username = userAccessModel?.tenDangNhap ?? '';

      //final response = await _chatService.getMockChatHistory();
      final response = await _chatService.getChatHistoryFromDB(username);

      final Map<String, Message> firstMessagesMap = {};

      for (var session in response.sessions) {
        for (var message in session.messages) {
          if ((message.role == 'Human' || message.role == 'AI') &&
              message.message.trim().isNotEmpty) {
            if (!firstMessagesMap.containsKey(session.sessionId)) {
              firstMessagesMap[session.sessionId] = message;
            } else {
              final existingTimestamp =
                  DateTime.parse(firstMessagesMap[session.sessionId]!.sentAt);
              final newTimestamp = DateTime.parse(message.sentAt);
              if (newTimestamp.isBefore(existingTimestamp)) {
                firstMessagesMap[session.sessionId] = message;
              }
            }
          }
        }
      }

      final uniqueMessages = firstMessagesMap.values.toList()
        ..sort((a, b) =>
            DateTime.parse(b.sentAt).compareTo(DateTime.parse(a.sentAt)));

      chatSessions.value = uniqueMessages;
      filteredChatSessions.value = uniqueMessages;
    } catch (e) {
      print('Error loading chat history: $e');
    } finally {
      isLoadingHistory.value = false;
    }
  }

  void clearSearch() {
    searchController.clear();
    searchQuery.value = '';
    filterChatHistory('');
  }

  void filterChatHistory(String query) {
    searchQuery.value = query;
    if (query.isEmpty) {
      filteredChatSessions.value = chatSessions;
    } else {
      filteredChatSessions.value = chatSessions
          .where((chat) =>
              chat.message.toLowerCase().contains(query.toLowerCase()))
          .toList();
    }
  }

  void openChatSession(String sessionId) async {
    try {
      final username = userAccessModel?.tenDangNhap ?? '';

      //final response = await _chatService.getMockChatHistory();
      final response = await _chatService.getChatHistoryFromDB(username);
      //final response = await _chatService.getMockChatHistory();
      final baseurlApi = dotenv.env['APICHAT'] ?? '';

      final session = response.sessions.firstWhere(
        (s) => s.sessionId == sessionId,
        orElse: () => throw Exception('Không tìm thấy phiên chat'),
      );

      final List<types.Message> messagesForSession = [];

      for (var msg in session.messages) {
        final author = msg.role == 'Human' ? user : botUser;

        types.Message message;
        final lowerCaseMessage = msg.message.toLowerCase();

        if (lowerCaseMessage.endsWith('.png')) {
          final url =
              msg.message.startsWith('/') ? msg.message : '/${msg.message}';
          final fullUrl = '$baseurlApi$url';

          message = types.ImageMessage(
            author: author,
            createdAt: DateTime.parse(msg.sentAt).millisecondsSinceEpoch,
            id: msg.messageId,
            name: 'image.png',
            size: 0,
            uri: fullUrl,
            width: 300,
            height: 200,
            metadata: {
              'downloadUrl': fullUrl,
              'isDownloadable': true,
              'marginTop': 30,
              'marginBottom': 10,
            },
          );
        } else if (lowerCaseMessage.endsWith('.pdf') ||
            lowerCaseMessage.endsWith('.xlsx')) {
          final url =
              msg.message.startsWith('/') ? msg.message : '/${msg.message}';
          final fullUrl = '$baseurlApi$url';
          final isPdf = lowerCaseMessage.endsWith('.pdf');
          final fileType = isPdf ? 'PDF' : 'Excel';
          final extension = isPdf ? '.pdf' : '.xlsx';

          message = types.FileMessage(
            author: author,
            createdAt: DateTime.parse(msg.sentAt).millisecondsSinceEpoch,
            id: msg.messageId,
            name: 'file$extension',
            size: 0,
            uri: fullUrl,
            mimeType: isPdf
                ? 'application/pdf'
                : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            metadata: {
              'type': isPdf ? 'pdf' : 'excel',
              'fileType': fileType,
              'isDownloadable': true,
              'downloadUrl': fullUrl,
              'hideSize': true,
            },
          );
        } else {
          message = types.TextMessage(
            author: author,
            createdAt: DateTime.parse(msg.sentAt).millisecondsSinceEpoch,
            id: msg.messageId,
            text: msg.message,
          );
        }

        messagesForSession.add(message);
      }

      messages.clear();
      messages.addAll(messagesForSession.reversed);

      Get.back();
      Get.toNamed(Routes.chatbot);
    } catch (e) {
      print('Error opening chat session: $e');
      SnackbarUtil.showError('Không thể mở lại phiên chat: $e',
          alignment: 'bottom');
    }
  }

  Future<void> deleteChatSession(String sessionId) async {
    try {
      final username = userAccessModel?.tenDangNhap ?? '';
      final success = await _chatService.deleteChatSession(username, sessionId);

      if (success) {
        chatSessions.removeWhere((chat) => chat.sessionId == sessionId);
        filteredChatSessions.removeWhere((chat) => chat.sessionId == sessionId);
        SnackbarUtil.showSuccess("Đã xóa cuộc trò chuyện", alignment: 'bottom');
      } else {
        SnackbarUtil.showError("Không thể xóa cuộc trò chuyện",
            alignment: 'bottom');
      }
    } catch (e) {
      print('Error deleting chat session: $e');
      SnackbarUtil.showError("Lỗi khi xóa cuộc trò chuyện: $e",
          alignment: 'bottom');
    }
  }

  Future<void> createNewSession() async {
    try {
      final username = userAccessModel?.tenDangNhap ?? '';
      final response = await _chatService.renewSession(username);

      messages.clear();
      _addWelcomeMessage();

      await loadChatHistory();

      // SnackbarUtil.showSuccess("Đã tạo phiên chat mới", alignment: 'bottom');
    } catch (e) {
      print('Error creating new session: $e');
      // SnackbarUtil.showError("Không thể tạo phiên chat mới: $e", alignment: 'bottom');
    }
  }

  Future<void> loadChatHistoryFromDB() async {
    try {
      isLoadingHistory.value = true;
      final username = userAccessModel?.tenDangNhap ?? '';
      final response = await _chatService.getChatHistoryFromDB(username);

      if (response.success) {
        final Map<String, Message> firstMessagesMap = {};
        for (var session in response.sessions) {
          if (session.messages.isNotEmpty) {
            final firstMessage = session.messages.reduce((a, b) =>
                DateTime.parse(a.sentAt).isBefore(DateTime.parse(b.sentAt))
                    ? a
                    : b);
            firstMessagesMap[session.sessionId] = firstMessage;
          }
        }

        final uniqueMessages = firstMessagesMap.values.toList()
          ..sort((a, b) =>
              DateTime.parse(b.sentAt).compareTo(DateTime.parse(a.sentAt)));

        chatSessions.value = uniqueMessages;
        filteredChatSessions.value = uniqueMessages;
      } else {
        throw Exception('Failed to load chat history');
      }
    } catch (e) {
      print('Error loading chat history from DB: $e');
      SnackbarUtil.showError("Không thể tải lịch sử chat: $e",
          alignment: 'bottom');
    } finally {
      isLoadingHistory.value = false;
    }
  }

  void resetChat() {
    messages.clear();
    _addWelcomeMessage();
    // Tạo session ID mới
  }

// Thêm hàm khởi tạo notifications
  // Future<void> _initNotifications() async {
  //   // Cấu hình cho Android
  //   const androidSettings = notifications.AndroidInitializationSettings('@mipmap/ic_launcher');
  //
  //   // Cấu hình cho iOS
  //   const iosSettings = notifications.DarwinInitializationSettings(
  //     requestAlertPermission: true,
  //     requestBadgePermission: true,
  //     requestSoundPermission: true,
  //   );
  //
  //   // Khởi tạo settings
  //   const initSettings = notifications.InitializationSettings(
  //     android: androidSettings,
  //     iOS: iosSettings,
  //   );
  //
  //   // Khởi tạo plugin
  //   await flutterLocalNotificationsPlugin.initialize(initSettings);
  //
  //   // Tạo notification channel cho Android
  //   const androidChannel = notifications.AndroidNotificationChannel(
  //     'download_channel',
  //     'Downloads',
  //     description: 'Thông báo khi tải file xong',
  //     importance: notifications.Importance.high,
  //   );
  //
  //   await flutterLocalNotificationsPlugin
  //       .resolvePlatformSpecificImplementation<notifications.AndroidFlutterLocalNotificationsPlugin>()
  //       ?.createNotificationChannel(androidChannel);
  // }
  //
  // // Sửa lại phần hiển thị thông báo trong handleMessageTap
  // Future<void> _showNotification(String title, String body) async {
  //   try {
  //     const androidDetails = notifications.AndroidNotificationDetails(
  //       'download_channel',
  //       'Downloads',
  //       channelDescription: 'Thông báo khi tải file xong',
  //       importance: notifications.Importance.high,
  //       priority: notifications.Priority.high,
  //       showWhen: true,
  //     );
  //
  //     const iosDetails = notifications.DarwinNotificationDetails(
  //       presentAlert: true,
  //       presentBadge: true,
  //       presentSound: true,
  //     );
  //
  //     const details = notifications.NotificationDetails(
  //       android: androidDetails,
  //       iOS: iosDetails,
  //     );
  //
  //     await flutterLocalNotificationsPlugin.show(
  //       0,
  //       title,
  //       body,
  //       details,
  //     );
  //   } catch (e) {
  //     print('Error showing notification: $e');
  //   }
  // }
}
