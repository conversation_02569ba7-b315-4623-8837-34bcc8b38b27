import 'dart:convert';

class ChatHistoryResponse {
  final bool success;
  final String type;
  final String url;
  final String username;
  final List<ChatSession> sessions;
  final String source;
  final int totalSessions;
  final int totalMessages;
  final String lastUpdated;

  ChatHistoryResponse({
    required this.success,
    required this.type,
    required this.url,
    required this.username,
    required this.sessions,
    required this.source,
    required this.totalSessions,
    required this.totalMessages,
    required this.lastUpdated,
  });

  factory ChatHistoryResponse.fromJson(Map<String, dynamic> json) {
    return ChatHistoryResponse(
      success: json['success'] ?? false,
      type: json['type'] ?? '',
      url: json['url'] ?? '',
      username: json['username'] ?? '',
      sessions: (json['sessions'] as List<dynamic>?)
          ?.map((e) => ChatSession.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      source: json['source'] ?? '',
      totalSessions: json['total_sessions'] ?? 0,
      totalMessages: json['total_messages'] ?? 0,
      lastUpdated: json['last_updated'] ?? '',
    );
  }
}

class ChatSession {
  final String sessionId;
  final String lastActive;
  final List<Message> messages;

  ChatSession({
    required this.sessionId,
    required this.lastActive,
    required this.messages,
  });

  factory ChatSession.fromJson(Map<String, dynamic> json) {
    return ChatSession(
      sessionId: json['session_id'] ?? '',
      lastActive: json['last_active'] ?? '',
      messages: (json['messages'] as List<dynamic>?)
          ?.map((e) => Message.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
    );
  }
}

class Message {
  final String role;
  final String type;
  final String message;
  final String sentAt;
  final String messageId;
  final String sessionId;

  Message({
    required this.role,
    required this.type,
    required this.message,
    required this.sentAt,
    required this.messageId,
    required this.sessionId,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      role: json['role'] ?? '',
      type: json['type'] ?? '',
      message: json['message'] ?? '',
      sentAt: json['sent_at'] ?? '',
      messageId: json['message_id'] ?? '',
      sessionId: json['session_id'] ?? '',
    );
  }
}
