class ChatMockData {
  static Map<String, dynamic> getMockChatHistory() {
    return {
      "success": true,
      "type": "text",
      "url": "",
      "username": "Admin",
      "sessions": [
        {
          "session_id": "7F519836-C20A-4B0C-8328-3070C0A9D277",
          "last_active": "2025-03-05 14:13:07",
          "messages": [
            {
              "role": "Human",
              "type": "text",
              "message": "An toàn thực phẩm là gì?",
              "sent_at": "2025-03-05 14:13:08",
              "message_id": "5A0B1C08-9361-4B70-AD1B-14D8B7529254",
              "session_id": "7F519836-C20A-4B0C-8328-3070C0A9D277"
            },
            {
              "role": "AI",
              "type": "text",
              "message":
                  "An toàn thực phẩm là một kh<PERSON>i niệm đảm bảo rằng thực phẩm không gây hại cho sức khỏe con người...",
              "sent_at": "2025-03-05 14:14:08",
              "message_id": "1B0C7524-D5D3-4D98-AECE-E2CC703A141F",
              "session_id": "7F519836-C20A-4B0C-8328-3070C0A9D277"
            },
            {
              "role": "Human",
              "type": "text",
              "message": "Quy trình?",
              "sent_at": "2025-03-05 14:15:08",
              "message_id": "5A0B1C08-9361-4B70-AD1B-14D8B7529111",
              "session_id": "7F519836-C20A-4B0C-8328-3070C0A9D277"
            },
            {
              "role": "AI",
              "type": "text",
              "message":
                  "Quy triình An toàn thực phẩm là một khái niệm đảm bảo rằng thực phẩm không gây hại cho sức khỏe con người...",
              "sent_at": "2025-03-05 14:16:09",
              "message_id": "1B0C7524-D5D3-4D98-AECE-E2CC703A1333",
              "session_id": "7F519836-C20A-4B0C-8328-3070C0A9D277"
            }
          ]
        },
        {
          "session_id": "D664AA1B-429A-47C6-BCD3-6A0C6EEB2785",
          "last_active": "2025-03-05 14:01:25",
          "messages": [
            {
              "role": "Human",
              "type": "text",
              "message": "Cho tôi xem file hướng dẫn",
              "sent_at": "2025-03-05 13:55:14",
              "message_id": "4C73CA43-78BE-4AF9-B005-7FF8706415AD",
              "session_id": "D664AA1B-429A-47C6-BCD3-6A0C6EEB2785"
            },
            {
              "role": "AI",
              "type": "pdf",
              "message": "",
              "sent_at": "2025-03-05 13:55:14",
              "message_id": "6DD1A8A1-D6F0-4E43-AB27-7DE3689A6206",
              "session_id": "D664AA1B-429A-47C6-BCD3-6A0C6EEB2785",
              "url": "https://pdfobject.com/pdf/sample.pdf"
            },
            {
              "role": "Human",
              "type": "text",
              "message": "Cho tôi xem báo cáo thống kê",
              "sent_at": "2025-03-05 14:01:26",
              "message_id": "66816C31-BA64-4536-8511-C77FFCF5D87E",
              "session_id": "D664AA1B-429A-47C6-BCD3-6A0C6EEB2785"
            },
            {
              "role": "AI",
              "type": "excel",
              "message": "",
              "sent_at": "2025-03-05 14:01:26",
              "message_id": "D2A944F7-49AB-478E-9495-81F94654DC88",
              "session_id": "D664AA1B-429A-47C6-BCD3-6A0C6EEB2785",
              "url": "https://pdfobject.com/pdf/sample.pdf"
            }
          ]
        },
        {
          "session_id": "E664AA1B-429A-47C6-BCD3-6A0C6EEB278t",
          "last_active": "2025-03-05 15:01:25",
          "messages": [
            {
              "role": "Human",
              "type": "text",
              "message": "Ảnh mẫu",
              "sent_at": "2025-03-05 15:55:14",
              "message_id": "5C73CA43-78BE-4AF9-B005-7FF8706415AD",
              "session_id": "E664AA1B-429A-47C6-BCD3-6A0C6EEB278t"
            },
            {
              "role": "AI",
              "type": "img",
              "message": "Đây là hình ảnh mẫu",
              "sent_at": "2025-03-05 15:55:14",
              "message_id": "7DD1A8A1-D6F0-4E43-AB27-7DE3689A6206",
              "session_id": "E664AA1B-429A-47C6-BCD3-6A0C6EEB278t",
              "url": "https://picsum.photos/200/300"
            }
          ]
        },
        {
          "session_id": "TTT4AA1B-429A-47C6-BCD3-6A0C6EEB2786",
          "last_active": "2025-03-06 7:01:25",
          "messages": [
            {
              "role": "Human",
              "type": "text",
              "message": "Hôm nay",
              "sent_at": "2025-03-06 07:01:25",
              "message_id": "TT73CA43-78BE-4AF9-B005-7FF8706415AD",
              "session_id": "TTT4AA1B-429A-47C6-BCD3-6A0C6EEB2786"
            },
            {
              "role": "AI",
              "type": "text",
              "message": "Đây là hôm nay",
              "sent_at": "2025-03-06 07:02:25",
              "message_id": "TTD1A8A1-D6F0-4E43-AB27-7DE3689A6206",
              "session_id": "TTT4AA1B-429A-47C6-BCD3-6A0C6EEB2786",
              "url": ""
            }
          ]
        }
      ],
      "source": "database",
      "total_sessions": 4,
      "total_messages": 12,
      "last_updated": "2025-03-06T12:10:00Z"
    };
  }

  static Map<String, dynamic> getMockChatResponse() {
    return {
      "success": true,
      "type": "text",
      "message": "Đây là câu trả lời mẫu",
      "url": "",
      "username": "Admin"
    };
  }

  static Map<String, dynamic> getMockFileResponse() {
    return {
      "success": true,
      "type": "pdf",
      "message": "",
      "file_size": 4567899,
      "url": "https://pdfobject.com/pdf/sample.pdf"
    };
  }

  static Map<String, dynamic> getMockImageResponse() {
    return {
      "success": true,
      "type": "img",
      "message": "Đây là hình ảnh mẫu",
      "url": "https://picsum.photos/200/300",
      "username": "Admin"
    };
  }

  static Map<String, dynamic> getMockOverflowResponse() {
    return {
      "success": true,
      "type": "overflow",
      "message": "Vượt mức pickle ball",
      "url": ""
    };
  }
}
