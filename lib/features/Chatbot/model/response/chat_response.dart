class ChatResponse {
  final String message;
  final String type;
  final String url;
  final bool success;
  final num? fileSize;

  ChatResponse({
    required this.message,
    required this.success,
    required this.type,
    required this.url,
    this.fileSize,
  });

  factory ChatResponse.fromJson(Map<String, dynamic> json) {
    return ChatResponse(
      message: json['message'] ?? '',
      success: json['success'] ?? false,
      type: json['type'] ?? 'text',
      url: json['url'] ?? '',
      fileSize: (json['type'] == 'pdf' || json['type'] == 'excel')
          ? (json['file_size'] as num?)
          : null,
    );
  }

  factory ChatResponse.mockResponse(String question) {
    return ChatResponse(
      message: '<PERSON><PERSON><PERSON> là câu trả lời cho câu hỏi: "$question"',
      success: true,
      type: 'text',
      url: '',
    );
  }
}
