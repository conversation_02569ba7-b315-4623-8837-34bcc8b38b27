import 'dart:developer';

import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/features/tinTuc/presentation/controller/tinTuc_controller.dart';

class NewsTabs extends GetView<TinTucController> {
  @override
  Widget build(BuildContext context) {
    // Lấy instance của TinTucController
    // final TinTucController controller = Get.put(TinTucController());

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: EdgeInsets.only(left: 3.w),
      child: Obx(() {
        // Thêm tab "Tất cả" vào đầu danh sách
        final allTabs = [
          {
            'id':
                '00000000-0000-0000-0000-000000000000', // ID tĩnh cho "Tất cả"
            'name': 'Tất cả', // Tên hiển thị
          },
          ...controller.allNewsCategoryItems.map((item) => {
                'id': item.LoaiBaiVietID,
                'name': item.TenLoaiBaiViet,
              }),
        ];

        return Row(
          children: allTabs.map((tab) {
            // Kiểm tra trạng thái selected dựa trên ID
            final isSelected = controller.selectedTabIndex.value == tab['id'];

            return GestureDetector(
              onTap: () async {
                controller.selectedTabIndex.value = tab['id'] as String;
                controller.handleChangeTab();
              },
              child: Container(
                margin: const EdgeInsets.only(right: 8),
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                decoration: BoxDecoration(
                  // color: isSelected ? Colors.white : Colors.grey.shade200,
                  color: Colors.white,
                  border: Border(
                    bottom: BorderSide(
                      color: isSelected
                          ? AppColors.primary
                          : Colors
                              .transparent, // Viền dưới màu xanh nếu isSelected
                      width: 3.0, // Độ dày của viền dưới
                    ),
                  ),
                ),
                child: Text(
                  tab['name'] as String,
                  style: TextStyle(
                      color: isSelected ? AppColors.primary : Colors.black54,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.w500,
                      fontSize: AppDimens.defaultText),
                ),
              ),
            );
          }).toList(),
        );
      }),
    );
  }
}
