import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/customCachedImage/customCachedImage.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/core/utils/convert_text.dart';
import 'package:attp_2024/features/tinTuc/model/NewsModel.dart';
import 'package:attp_2024/features/tinTuc/presentation/page/tinTuc_detail.dart';

Widget buildVerticalNewsItem(
    NewsItem news, BuildContext context, String urlImage) {
  return Column(
    children: [
      Padding(
        padding: EdgeInsets.symmetric(horizontal: 4.w),
        child: GestureDetector(
          onTap: () {
            Get.to(() => TinTucDetailPage(news: news));
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 5.0),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white, // Màu nền của item
                borderRadius: BorderRadius.circular(8), // Bo góc nhẹ
                boxShadow: [
                  BoxShadow(
                    // ignore: deprecated_member_use
                    color: Colors.black.withOpacity(0.2), // Màu shadow
                    blurRadius: 8, // Độ mờ của shadow
                    offset: const Offset(0, 4), // Độ dịch chuyển của shadow
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Nội dung tin tức
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextWidget(
                            text: news.TieuDe,
                            size: AppDimens.subText,
                            fontWeight: FontWeight.w600,
                            maxLines: 2,
                          ),
                          const SizedBox(height: 3),
                          TextWidget(
                            text: convertHtmlToString(news.NoiDungTomTat,
                                "Nội dung tóm tắt không thể hiển thị ..."),
                            size: AppDimens.subText,
                            fontWeight: FontWeight.w500,
                            color: AppColors.gray2,
                            wordLimit: 15,
                            maxLines: 2,
                          ),
                          const SizedBox(height: 3),
                          TextWidget(
                            text: '${news.TenLoaiBaiViet} • ${news.NgayTao}',
                            size: AppDimens.subText,
                            fontWeight: FontWeight.w600,
                            color: AppColors.gray2,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Ảnh đại diện
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: CustomCachedImage(
                        defaultImage: AppImageString.imageNotFount,
                        imageUrl: urlImage + news.HinhAnh,
                        width: 35.w,
                        height: 12.h,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
      // const Divider(
      //   // Đường gạch ngang
      //   height: 0.5,
      //   thickness: 0.5,
      //   color: Colors.grey, // Màu của đường gạch ngang
      // ),
    ],
  );
}
