import 'package:attp_2024/core/configs/contanst/proc_constants.dart';
import 'package:attp_2024/core/data/api/services/proc/proc_service.dart';
import 'package:attp_2024/features/tinTuc/model/NewsModel.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

class NewsService {
  final ProcService _procService = Get.find<ProcService>();

  Future<List<NewsCategoryItem>> fetchCategoryNews() async {
    final List<Map<String, dynamic>> body = [
      {"name": "IsForCombo", "type": "Boolean", "value": false}
    ];
    try {
      final response =
          await _procService.callProc(ProcConstants.getAllLoaiBaiViet, body);
      if (response.isNotEmpty) {
        return response
            .map((json) => NewsCategoryItem(
                  LoaiBaiVietID: json['LoaiBaiVietID'] ?? '',
                  MaLoaiBaiViet: json['MaLoaiBaiViet'] ?? '',
                  TenLoaiBaiViet: json['TenLoaiBaiViet'] ?? '',
                  LoaiBaiVietID_Cha: json['LoaiBaiVietID_Cha'] ?? '',
                  TenLoaiBaiViet_Cha: json['TenLoaiBaiViet_Cha'] ?? '',
                  TuKhoa: json['TuKhoa'] ?? '',
                  TrangThai: json['TrangThai'] ?? false,
                  HienThiHDSD: json['HienThiHDSD'] ?? '',
                ))
            .toList();
      }
    } catch (e) {
      print("Error fetching category news: $e");
    }
    return [];
  }

  Future<List<NewsItem>> fetchNews({int? limit}) async {
    final List<Map<String, dynamic>> body = [];

    // Nếu có limit, thêm vào body request
    if (limit != null) {
      body.add({"name": "Limit", "type": "Int", "value": limit});
    }

    try {
      final response =
          await _procService.callProc(ProcConstants.getAllBaiViet, body);
      if (response.isNotEmpty) {
        return response
            .map((json) => NewsItem(
                  BaiVietID: json['BaiVietID'] ?? '',
                  TieuDe: json['TieuDe'] ?? '',
                  HinhAnh: json['HinhAnh'] ?? '',
                  TuKhoa: json['TuKhoa'] ?? '',
                  NoiDung: json['NoiDung'] ?? '',
                  LuotXem: json['LuotXem'] ?? 0,
                  LaNoiBat: json['LaNoiBat'] ?? false,
                  DinhDanh: json['DinhDanh'] ?? '',
                  UserID_NguoiTao: json['UserID_NguoiTao'] ?? '',
                  NgayTao: json['NgayTao'] ?? '',
                  NguoiTao: json['NguoiTao'] ?? '',
                  DonViID: json['DonViID'] ?? '',
                  NoiDungTomTat: json['NoiDungTomTat'] ?? '',
                  HienThiHDSD: json['HienThiHDSD'] ?? false,
                  TenLoaiBaiViet: json['TenLoaiBaiViet'] ?? '',
                  LoaiBaiVietID: json['LoaiBaiVietID'] ?? '',
                  TrangThai: json['TrangThai'] ?? false,
                  RemainingCount: json['RemainingCount'] ?? 0,
                ))
            .toList();
      }
    } catch (e) {
      print("Error fetching news: $e");
    }

    return [];
  }
}
