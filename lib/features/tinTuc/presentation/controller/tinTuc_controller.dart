// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:developer';

import 'package:attp_2024/core/data/models/user_access_model.dart';
import 'package:attp_2024/features/main/presentation/controller/main_controller.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/configs/contanst/proc_constants.dart';
import 'package:attp_2024/core/data/api/services/proc/proc_service.dart';
import 'package:attp_2024/core/services/user_use_case.dart';
import 'package:attp_2024/features/tinTuc/model/NewsModel.dart';

class TinTucController extends GetxController {
  var isLoading = false.obs;
  var selectedTabIndex = '00000000-0000-0000-0000-000000000000'.obs;
  final ProcService _procService = Get.find<ProcService>();
  var allNewsCategoryItems = <NewsCategoryItem>[].obs;
  var allNewsItems = <NewsItem>[].obs;
  var hotFilteredNews = <NewsItem>[].obs;
  var currentNews = Rx<NewsItem?>(null);
  final username = ''.obs;
  final pageIndex = 0.obs;
  final soLuongMoiLanLoad = 5.obs;
  final hetDuLieuRoi = false.obs;
  final soLuongBaiVietNoiBatMoiLanLoad = 10.obs;
  final baiVietConLai = 0.obs;
  var url = "".obs;

  MainController? mainController;
  int selectedIndex = 0;

  UserAccessModel? userAccessModel;

  @override
  Future<void> onInit() async {
    super.onInit();
    mainController = Get.find<MainController>();
    selectedIndex = mainController!.selectedPageIndex.value;
    username.value = await getUserName();
    await loadInfoProfile();
    await fetchCategoryNews(); // fetch Tab
    await fetchNewsByID(
        selectedTabIndex.value, pageIndex.value); //fetch bài viết
    await fetchHotNewsByID(selectedTabIndex.value); //fetch bài viết noibat
  }

  Future<void> loadInfoProfile() async {
    userAccessModel = await UserUseCase.getUser();
    url.value = userAccessModel!.siteURL.toString();
    log(url.value, name: "tintucurl");
  }

  Future<String> getUserName() async {
    final user = await UserUseCase.getUser();
    return user!.userName;
  }

  void setNews(NewsItem news) {
    currentNews.value = news;
  }

  Future<void> fetchNewsByID(String id, int page) async {
    isLoading.value = true;
    pageIndex.value = pageIndex.value + soLuongMoiLanLoad.value;
    final List<Map<String, dynamic>> body = [
      {"name": "ID", "type": "Guid", "value": id},
      {"name": "Page", "type": "Int", "value": page},
      {"name": "Quantity", "type": "Int", "value": soLuongMoiLanLoad.value}
    ];
    try {
      final response = await _procService.callProc(
          ProcConstants.getAllBaiVietTheoLoaiBaiVietID, body);
      if (response.isNotEmpty) {
        final newsItem = response
            .map((json) => NewsItem(
                  BaiVietID: json['BaiVietID'] ?? '',
                  // MaBaiViet: json['MaBaiViet'] ?? '',
                  TieuDe: json['TieuDe'] ?? '',
                  HinhAnh: json['HinhAnh'] ?? '',
                  TuKhoa: json['TuKhoa'] ?? '',
                  NoiDung: json['NoiDung'] ?? '',
                  LuotXem: json['LuotXem'] ?? 0,
                  LaNoiBat: json['LaNoiBat'] ?? false,
                  DinhDanh: json['DinhDanh'] ?? '',
                  UserID_NguoiTao: json['UserID_NguoiTao'] ?? '',
                  NgayTao: json['NgayTao'] ?? '',
                  NguoiTao: json['NguoiTao'] ?? '',
                  DonViID: json['DonViID'] ?? '',
                  NoiDungTomTat: json['NoiDungTomTat'] ?? '',
                  HienThiHDSD: json['HienThiHDSD'] ?? false,
                  TenLoaiBaiViet: json['TenLoaiBaiViet'] ?? '',
                  LoaiBaiVietID: json['LoaiBaiVietID'] ?? '',
                  TrangThai: json['TrangThai'] ?? false,
                  RemainingCount: json['RemainingCount'] ?? 0,
                ))
            .toList();
        log(newsItem.length.toString(), name: 'Số lượng bài :');
        allNewsItems.addAll(newsItem);
        baiVietConLai.value =
            newsItem.first.RemainingCount.toInt() - allNewsItems.length;
        if (baiVietConLai.value == 0) {
          hetDuLieuRoi.value = true;
        }
        // allNewsItems
        //     .forEach((item) => log(item.toString(), name: item.TenLoaiBaiViet));
      } else {
        hetDuLieuRoi.value = true;
      }
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> fetchHotNewsByID(String id) async {
    isLoading.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "ID", "type": "Guid", "value": id},
      {
        "name": "Quantity",
        "type": "Int",
        "value": soLuongBaiVietNoiBatMoiLanLoad.value
      }
    ];
    try {
      final response = await _procService.callProc(
          ProcConstants.getAllBaiVietNoiBatTheoLoaiBaiVietID, body);
      if (response.isNotEmpty) {
        final newsItem = response
            .map((json) => NewsItem(
                  BaiVietID: json['BaiVietID'] ?? '',
                  // MaBaiViet: json['MaBaiViet'] ?? '',
                  TieuDe: json['TieuDe'] ?? '',
                  HinhAnh: json['HinhAnh'] ?? '',
                  TuKhoa: json['TuKhoa'] ?? '',
                  NoiDung: json['NoiDung'] ?? '',
                  LuotXem: json['LuotXem'] ?? 0,
                  LaNoiBat: json['LaNoiBat'] ?? false,
                  DinhDanh: json['DinhDanh'] ?? '',
                  UserID_NguoiTao: json['UserID_NguoiTao'] ?? '',
                  NgayTao: json['NgayTao'] ?? '',
                  NguoiTao: json['NguoiTao'] ?? '',
                  DonViID: json['DonViID'] ?? '',
                  NoiDungTomTat: json['NoiDungTomTat'] ?? '',
                  HienThiHDSD: json['HienThiHDSD'] ?? false,
                  TenLoaiBaiViet: json['TenLoaiBaiViet'] ?? '',
                  LoaiBaiVietID: json['LoaiBaiVietID'] ?? '',
                  TrangThai: json['TrangThai'] ?? false,
                  RemainingCount: json['RemainingCount'] ?? 0,
                ))
            .toList();
        log(newsItem.length.toString(), name: 'Số lượng bài :');
        hotFilteredNews.assignAll(newsItem);
        // allNewsItems
        //     .forEach((item) => log(item.toString(), name: item.TenLoaiBaiViet));
      }
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> fetchCategoryNews() async {
    isLoading.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "IsForCombo", "type": "Boolean", "value": false}
    ];
    try {
      final response =
          await _procService.callProc(ProcConstants.getAllLoaiBaiViet, body);
      if (response.isNotEmpty) {
        final newsCategoryItems = response
            .map((json) => NewsCategoryItem(
                  LoaiBaiVietID: json['LoaiBaiVietID'] ?? '',
                  MaLoaiBaiViet: json['MaLoaiBaiViet'] ?? '',
                  TenLoaiBaiViet: json['TenLoaiBaiViet'] ?? '',
                  LoaiBaiVietID_Cha: json['LoaiBaiVietID_Cha'] ?? '',
                  TenLoaiBaiViet_Cha: json['TenLoaiBaiViet_Cha'] ?? '',
                  TuKhoa: json['TuKhoa'] ?? '',
                  TrangThai: json['TrangThai'] ?? false,
                  HienThiHDSD: json['HienThiHDSD'] ?? '',
                ))
            .toList();
        allNewsCategoryItems.assignAll(newsCategoryItems);
        // allNewsCategoryItems.forEach((item) => print(item));
      }
    } finally {
      isLoading.value = false;
    }
  }

  // Future<void> fetchNews() async {
  //   isLoading.value = true;
  //   final List<Map<String, dynamic>> body = [];
  //   try {
  //     final response =
  //         await _procService.callProc(ProcConstants.getAllBaiViet, body);
  //     if (response.isNotEmpty) {
  //       final newsItem = response
  //           .map((json) => NewsItem(
  //                 BaiVietID: json['BaiVietID'] ?? '',
  //                 // MaBaiViet: json['MaBaiViet'] ?? '',
  //                 TieuDe: json['TieuDe'] ?? '',
  //                 HinhAnh: json['HinhAnh'] ?? '',
  //                 TuKhoa: json['TuKhoa'] ?? '',
  //                 NoiDung: json['NoiDung'] ?? '',
  //                 LuotXem: json['LuotXem'] ?? 0,
  //                 LaNoiBat: json['LaNoiBat'] ?? false,
  //                 DinhDanh: json['DinhDanh'] ?? '',
  //                 UserID_NguoiTao: json['UserID_NguoiTao'] ?? '',
  //                 NgayTao: json['NgayTao'] ?? '',
  //                 NguoiTao: json['NguoiTao'] ?? '',
  //                 DonViID: json['DonViID'] ?? '',
  //                 NoiDungTomTat: json['NoiDungTomTat'] ?? '',
  //                 HienThiHDSD: json['HienThiHDSD'] ?? false,
  //                 TenLoaiBaiViet: json['TenLoaiBaiViet'] ?? '',
  //                 LoaiBaiVietID: json['LoaiBaiVietID'] ?? '',
  //                 TrangThai: json['TrangThai'] ?? false,
  //                 RemainingCount: json['RemainingCount'] ?? 0,
  //               ))
  //           .toList();
  //       allNewsItems.assignAll(newsItem);
  //       // allNewsItems.forEach((item) => print(item));
  //       // print(allNewsItems);
  //     }
  //   } finally {
  //     isLoading.value = false;
  //   }
  // }

  void handleChangeTab() async {
    allNewsItems.clear();
    hotFilteredNews.clear();
    pageIndex.value = 0;
    baiVietConLai.value = 0;
    hetDuLieuRoi.value = false;
    await fetchNewsByID(selectedTabIndex.value, 0);
    await fetchHotNewsByID(selectedTabIndex.value);
  }

  void selectTab(String index) {
    selectedTabIndex.value = index;
  }
}
