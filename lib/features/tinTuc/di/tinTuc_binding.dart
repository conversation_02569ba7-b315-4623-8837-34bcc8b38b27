import 'package:get/get.dart';
import 'package:attp_2024/features/tinTuc/presentation/controller/tinTuc_controller.dart';

import '../../../core/data/api/configs/dio_configs.dart';
import '../../../core/data/api/services/proc/proc_service.dart';

class TinTucBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TinTucController());
    Get.lazyPut(() => ProcService(Get.find<DioService>()));
  }
}
