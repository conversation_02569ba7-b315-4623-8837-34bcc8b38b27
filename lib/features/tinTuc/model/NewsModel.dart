// ignore_for_file: public_member_api_docs, sort_constructors_first
class NewsItem {
  String BaiVietID;
  // String MaBaiViet;
  String TieuDe;
  String HinhAnh;
  String TuKhoa;
  String NoiDung;
  double LuotXem;
  bool LaNoiBat;
  String DinhDanh;
  String UserID_NguoiTao;
  String NgayTao;
  String NguoiTao;
  String DonViID;
  String NoiDungTomTat;
  bool HienThiHDSD;
  String TenLoaiBaiViet;
  String LoaiBaiVietID;
  bool TrangThai;
  int RemainingCount;

  NewsItem({
    required this.BaiVietID,
    // required this.MaBaiViet,
    required this.TieuDe,
    required this.HinhAnh,
    required this.TuKhoa,
    required this.NoiDung,
    required this.LuotXem,
    required this.LaNoiBat,
    required this.DinhDanh,
    required this.UserID_NguoiTao,
    required this.NgayTao,
    required this.NguoiTao,
    required this.DonViID,
    required this.NoiDungTomTat,
    required this.HienThiHDSD,
    required this.TenLoaiBaiViet,
    required this.LoaiBaiVietID,
    required this.TrangThai,
    required this.RemainingCount,
  });

  @override
  String toString() {
    return '''
    NewsItem(
      BaiVietID: $BaiVietID, 
      TieuDe: $TieuDe, 
      LaNoiBat: $LaNoiBat, 
      TrangThai: $TrangThai, 
      LoaiBaiVietID: $LoaiBaiVietID, 
      TenLoaiBaiViet: $TenLoaiBaiViet, 
    )
    ''';
  }
}

class NewsCategoryItem {
  final String LoaiBaiVietID;
  final String MaLoaiBaiViet;
  final String TenLoaiBaiViet;
  final String LoaiBaiVietID_Cha;
  final String TenLoaiBaiViet_Cha;
  final String TuKhoa;
  final bool TrangThai;
  final bool HienThiHDSD;

  NewsCategoryItem({
    required this.LoaiBaiVietID,
    required this.MaLoaiBaiViet,
    required this.TenLoaiBaiViet,
    required this.LoaiBaiVietID_Cha,
    required this.TenLoaiBaiViet_Cha,
    required this.TuKhoa,
    required this.TrangThai,
    required this.HienThiHDSD,
  });

  @override
  String toString() {
    return '''
    NewsCategoryItem(
      LoaiBaiVietID: $LoaiBaiVietID, 
      MaLoaiBaiViet: $MaLoaiBaiViet, 
      TenLoaiBaiViet: $TenLoaiBaiViet, 
      LoaiBaiVietID_Cha: $LoaiBaiVietID_Cha, 
      TenLoaiBaiViet_Cha: $TenLoaiBaiViet_Cha, 
      TuKhoa: $TuKhoa, 
      DangSD: $TrangThai, 
      HienThiHDSD: $HienThiHDSD
    )
    ''';
  }
}
