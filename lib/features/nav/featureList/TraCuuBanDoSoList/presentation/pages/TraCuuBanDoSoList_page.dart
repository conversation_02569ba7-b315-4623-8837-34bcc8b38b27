import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/features/nav/featureList/TraCuuBanDoSoList/presentation/controllers/TraCuuBanDoSoList_controller.dart';
import 'package:attp_2024/features/nav/home/<USER>/item_category_function_model.dart';

import '../../../../../../core/ui/widgets/appbar/app_bar_widget.dart';
import '../../../../../../core/ui/widgets/customCachedImage/customCachedImage.dart';

class TracuubandosolistPage extends GetView<TracuubandosolistController> {
  const TracuubandosolistPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColorPrimary,
      appBar: const AppBarWidget(
        centerTitle: true,
        title: 'Tra cứu bản đồ số',
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: AppDimens.textSize16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 12),
              SizedBox(
                height: MediaQuery.of(context).size.height *
                    0.8,
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: GetBuilder<TracuubandosolistController>(
                    id: "bodyID",
                    builder: (controller) {
                      if (controller.userAccessModel == null) {
                        return const Center(child: CircularProgressIndicator());
                      }
                      return _BuildCategoryFunction(
                        listItemCategory: controller.listCategoryModel,
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Container _buildAppBar() {
    return Container(
      padding: const EdgeInsets.fromLTRB(5.0, 50.0, 5.0, 10.0),
      child: GetBuilder<TracuubandosolistController>(
        id: "bodyID",
        builder: (controller) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(32.0),
                    child: CustomCachedImage(
                      width: 45,
                      height: 45,
                      imageUrl:
                          '${controller.userAccessModel?.siteURL}\\${controller.userAccessModel?.anhDaiDien.replaceFirst('~', '') ?? ''}',
                      defaultImage: 'https://via.placeholder.com/150',
                    ),
                  ),
                  const SizedBox(width: 10),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextWidget(
                        text: controller.displayName.value.isNotEmpty
                            ? controller.displayName.value
                            : "Đang cập nhật",
                        size: 16,
                        fontWeight: FontWeight.w700,
                        color: Colors.black,
                      ),
                      TextWidget(
                        text: controller.userAccessModel?.TenDonVi ??
                            "Đang cập nhật",
                        size: 12,
                      ),
                    ],
                  ),
                  const Spacer(),
                  const Icon(
                    CupertinoIcons.bell,
                    color: AppColors.lightThemePrimaryText,
                    size: AppDimens.textSize24,
                  ),
                ],
              ),
              const SizedBox(height: 5),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  IconButton(
                    icon: const Icon(
                      Icons.arrow_back_ios,
                      color: AppColors.lightThemePrimaryText,
                    ),
                    onPressed: () {
                      Get.back();
                    },
                  ),
                  const TextWidget(
                    text: "Tra cứu trên bản đồ số",
                    size: AppDimens.textSize16,
                    fontWeight: FontWeight.w600,
                  ),
                ],
              ),
            ],
          );
        },
      ),
    );
  }
}

class _BuildCategoryFunction extends StatelessWidget {
  final List<ItemCategoryFunctionModel> listItemCategory;

  const _BuildCategoryFunction({required this.listItemCategory});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // _buildHeaderCategoryFunction(),
        Expanded(
          child: GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            mainAxisSpacing: 10,
            crossAxisSpacing: 10,
            children: listItemCategory.map(
                  (items) {
                return _buildItemCategoryFunction(
                  title: items.title,
                  iconsUrl: items.iconUrl,
                  toPagePath: items.toPagePath,
                );
              },
            ).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildItemCategoryFunction({
    required String title,
    required String iconsUrl,
    required String toPagePath,
  }) {
    return InkWell(
      onTap: () {
        if (toPagePath != "") Get.toNamed(toPagePath);
      },
      child: Container(
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: AppColors.black.withOpacity(.15),
              spreadRadius: 1,
              blurRadius: 2,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              iconsUrl,
              fit: BoxFit.contain,
              height: 70,
              width: 70,
            ),
            const SizedBox(height: 10),
            TextWidget(
              textAlign: TextAlign.center,
              text: title,
              size: AppDimens.textSize12,
              fontWeight: FontWeight.w600,
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }

  Row _buildHeaderCategoryFunction() {
    return const Row(
      children: [
        SizedBox(
          width: 5,
        ),
        TextWidget(
          text: "Tra cứu thông tin",
          size: AppDimens.textSize18,
          fontWeight: FontWeight.w700,
        ),
        SizedBox(
          height: 20,
        ),
      ],
    );
  }
}
