import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/features/nav/home/<USER>/item_category_function_model.dart';
import '../../../../../../core/ui/widgets/appbar/app_bar_widget.dart';
import '../controllers/TraCuuList_controller.dart';

class TracuulistPage extends GetView<TracuulistController> {
  const TracuulistPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColorPrimary,
      appBar: AppBarWidget(
        hiddenLeading: controller.selectedIndex == 0,
        centerTitle: true,
        title: 'Tra cứu',
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: AppDimens.textSize16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 12),
            Expanded(
              child: GetBuilder<TracuulistController>(
                id: "bodyID",
                builder: (controller) {
                  if (controller.userAccessModel == null) {
                    return const Center(child: CircularProgressIndicator());
                  }
                  return GridView.builder(
                    shrinkWrap: true,
                    primary: false,
                    physics: const BouncingScrollPhysics(),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      mainAxisSpacing: 10,
                      crossAxisSpacing: 10,
                      childAspectRatio: 0.85,
                    ),
                    itemCount: controller.listCategoryModel.length,
                    itemBuilder: (context, index) {
                      final item = controller.listCategoryModel[index];
                      return _buildItemCategoryFunction(item);
                    },
                  );
                },
              ),
            ),
            Gap(7.h),
          ],
        ),
      ),
    );
  }

  Widget _buildItemCategoryFunction(ItemCategoryFunctionModel item) {
    return InkWell(
      onTap: () {
        if (item.toPagePath.isNotEmpty) {
          Get.toNamed(item.toPagePath);
        }
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: AppColors.black.withOpacity(.15),
              spreadRadius: 1,
              blurRadius: 2,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              item.iconUrl,
              fit: BoxFit.contain,
              height: 70,
              width: 70,
            ),
            const SizedBox(height: 10),
            TextWidget(
              textAlign: TextAlign.center,
              text: item.title,
              size: AppDimens.defaultText,
              fontWeight: FontWeight.w600,
              maxLines: 2, // Giới hạn hiển thị 2 dòng
              // overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
