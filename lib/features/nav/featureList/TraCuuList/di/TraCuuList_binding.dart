import 'package:get/get.dart';
import 'package:attp_2024/core/data/api/configs/dio_configs.dart';
import 'package:attp_2024/core/data/api/services/auth/auth_service.dart';
import 'package:attp_2024/core/data/api/services/proc/proc_service.dart';

import '../presentation/controllers/TraCuuList_controller.dart';

class TracuulistBinding extends Bindings {
  @override
  void dependencies() async {
    Get.putAsync(() async => DioService().init());
    Get.lazyPut(() => AuthService(Get.find<DioService>()));
    Get.lazyPut(() => TracuulistController());
    Get.lazyPut(() => ProcService(Get.find<DioService>()));

  }
}
