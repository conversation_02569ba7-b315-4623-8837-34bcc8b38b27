import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:attp_2024/core/ui/widgets/button/button_widget.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/features/nav/statistics/presentation/controllers/statistics_controller.dart';

class StatisticsPage extends GetView<StatisticsController> {
  const StatisticsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppBarWidget(
        title: "Ví dụ về chuyển trang với GetX",
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const TextWidget(text: "<PERSON><PERSON><PERSON> c<PERSON>ch chuyển trang"),
            const TextWidget(
                text: "Dưới đây là các dạng chuyển trang hay dùng"),
            const SizedBox(
              height: 20,
            ),

            const SizedBox(
                width: 300,
                child: TextWidget(
                    text:
                        "toName: chuyển trang và thêm trang trước đó vào stack")),

            //================================================================//
            SizedBox(
              width: 300,
              child: ButtonWidget(
                text: "Get.toNamed('/page1')",
                ontap: () {
                  // Get.toNamed(Routes.page1); //
                },
              ),
            ),
            const SizedBox(
              height: 20,
            ),
            const SizedBox(
                width: 300,
                child: TextWidget(
                    text:
                        "offAndToNamed: Xóa trang hiện tại là chuyển đến trang mới")),

            //==================================================================//
            SizedBox(
              width: 300,
              child: ButtonWidget(
                text: "Get.offAndToNamed('/page2')",
                ontap: () {
                  // Get.offAndToNamed(Routes.page2); //
                },
              ),
            ),
            const SizedBox(
              height: 20,
            ),
            const SizedBox(
                width: 300,
                child: TextWidget(
                    text:
                        "offAndToNamed:Xóa trang hiện tại và tất cả các trang trước đó và chuyển đến trang mới")),

            //===================================================================================//
            SizedBox(
              width: 300,
              child: ButtonWidget(
                text: "Get.offAllNamed('/page3')",
                ontap: () {
                  // Get.offAllNamed(Routes.page3); //
                },
              ),
            ),
            const SizedBox(
              height: 20,
            ),
          ],
        ),
      ),
    );
  }
}
