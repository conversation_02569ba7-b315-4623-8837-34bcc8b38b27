import 'package:attp_2024/core/data/dto/response/profile_user_model.dart';
import 'package:geocoding/geocoding.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:attp_2024/core/data/api/services/proc/proc_service.dart';
import 'package:attp_2024/core/data/models/user_access_model.dart';
import 'package:attp_2024/core/routes/routes.dart';
import 'package:attp_2024/core/services/auth_use_case/auth_use_case.dart';
import 'package:attp_2024/core/services/user_use_case.dart';
import 'package:geolocator/geolocator.dart';

class ProfileController extends GetxController {
  final ProcService _procService = Get.find<ProcService>();
  UserAccessModel? userAccessModel;
  final userID = ''.obs;
  RxString avatarPathOld = ''.obs;
  RxString name = ''.obs;
  @override
  onInit() async {
    super.onInit();
    userID.value = await getUserID();
    await loadInfoProfile();
    await fetchUserProfile(userID: userID.value);
    // getCurrentLocation();
  }

  Future<String> getUserID() async {
    final userId = await UserUseCase.getUser();
    return userId!.userID;
  }

  Future<void> fetchUserProfile({required String userID}) async {
    final List<Map<String, dynamic>> body = [
      {"name": "UserID", "type": "Guid", "value": userID},
    ];
    try {
      final response =
          await _procService.callProc("Proc_Mobile_GetUserInfo_byDB", body);
      if (response.isNotEmpty) {
        final userProfile = response
            .map((json) => ProfileUserModel(
                  diaChi: json['DiaChi'] ?? '',
                  tenDangNhap: json['TenDangNhap'] ?? '',
                  hoTen: json['HoTen'] ?? '',
                  gioiTinh: json['GioiTinh'] ?? '',
                  diDong: json['DiDong'] ?? '',
                  email: json['Email'] ?? '',
                  ngaySinh: json['NgaySinh'] ?? '',
                  hinhDaiDien: json['HinhDaiDien'] ?? '',
                  donViCode: json['DonViCode'] ?? '',
                  tenDonVi: json['TenDonVi'] ?? '',
                  donViID: json['DonViID'] ?? '',
                  tinhID: json['TinhID'] ?? '',
                  huyenID: json['HuyenID'] ?? '',
                  xaID: json['XaID'] ?? '',
                  thonID: json['ThonID'] ?? '',
                  tenTinh: json['TenTinh'] ?? '',
                  tenHuyen: json['TenHuyen'] ?? '',
                  tenXa: json['TenXa'] ?? '',
                  tenThon: json['TenThon'] ?? '',
                  userGroupID: json['UserGroupID'] ?? '',
                  userGroupCode: json['UserGroupCode'] ?? '',
                ))
            .first;
        name.value = userProfile.hoTen;
        avatarPathOld.value = userProfile.hinhDaiDien;
      }
    } catch (e) {
    } finally {}
  }

  final ProcService proc = Get.find<ProcService>();
  var logger = Logger();

  Future<void> donvi() async {}

  String getGreetingMessage() {
    final hour = DateTime.now().hour;
    if (hour >= 5 && hour < 12) {
      return "Xin chào buổi sáng 🌞";
    } else if (hour >= 12 && hour < 18) {
      return "Xin chào buổi chiều 🌤️";
    } else if (hour >= 18 && hour < 22) {
      return "Xin chào buổi tối 🌆";
    } else {
      return "Chúc bạn ngủ ngon 🌙";
    }
  }

  Future<void> loadInfoProfile() async {
    userAccessModel = await UserUseCase.getUser();
    logger.t(userAccessModel?.token.toString());
    update(["bodyID"]);
  }

  Future<void> logOut() async {
    try {
      await AuthUseCase.logOut();
      Get.offAllNamed(Routes.splash);
    } catch (e) {
      // ignore: avoid_print
      print(e);
    }
  }

  var currentAddress = ''.obs;
  Future<String> getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw Exception('Dịch vụ vị trí đang bị tắt.');
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('Quyền vị trí bị từ chối.');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('Quyền vị trí bị từ chối vĩnh viễn.');
      }

      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      // Extract address
      Placemark place = placemarks[0];
      String fullAddress =
          '${place.street}, ${place.locality}, ${place.administrativeArea}, ${place.country}';

      // Split the address and get the last three parts excepts the last one.
      List<String> addressParts =
          fullAddress.split(',').map((e) => e.trim()).toList();
      if (addressParts.length >= 3) {
        String simplifiedAddress =
            '${addressParts[addressParts.length - 3]}, ${addressParts[addressParts.length - 2]}';

        return simplifiedAddress;
      } else {
        return 'Không thể xác định địa chỉ đầy đủ.';
      }
    } catch (e) {
      return 'Không thể lấy vị trí: ${e.toString()}';
    }
  }
}
