import 'package:get/get.dart';
import 'package:attp_2024/core/data/prefs/prefs.dart';
import 'package:attp_2024/features/nav/profile/presentation/controllers/profile_controller.dart';

import '../../../../core/data/api/configs/dio_configs.dart';
import '../../../../core/data/api/services/proc/proc_service.dart';

class ProfileBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => Prefs(), fenix: true);
    Get.lazyPut(() => ProfileController());
    Get.lazyPut(() => ProcService(Get.find<DioService>()));
  }
}
