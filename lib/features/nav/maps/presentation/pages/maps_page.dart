import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:attp_2024/core/ui/widgets/avatar/avatar_widget.dart';
import 'package:attp_2024/features/nav/home/<USER>/controllers/home_controller.dart';

class MapsPage extends GetView<HomeController> {
  const MapsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      appBar: AppBarWidget(
        title: "Widget dùng chung",
        centerTitle: true,
      ),
      body: Center(
        child: Column(
          children: [
            SizedBox(
              height: 30,
            ),
            AvatarWidget(
              radius: 50,
              url: "kjadksjdasdjhakhkj",
            ),
            <PERSON><PERSON><PERSON><PERSON>(
              height: 30,
            ),
            SizedBox(
              width: 300,
              child: Column(
                children: [
                  // ButtonWidget(ontap: () {}, text: "My Butom"),
                  //
                  // const SizedBox(
                  //   height: 10,
                  // ),
                  //
                  // const SizedBox(
                  //   height: 10,
                  // ),
                  // const TextWidget(text: "Thông báo"),
                  // ButtonWidget(
                  //     backgroundColor: AppColors.success,
                  //     ontap: () {
                  //       SnackbarUtil.showSuccess("Thanh cong");
                  //     },
                  //     text: "click me !"),
                  // const SizedBox(
                  //   height: 10,
                  // ),
                  // ButtonWidget(
                  //     backgroundColor: AppColors.error,
                  //     ontap: () {
                  //       SnackbarUtil.showError("Thất bại");
                  //     },
                  //     text: "click me!"),
                  // const SizedBox(
                  //   height: 10,
                  // ),
                  // ButtonWidget(
                  //     backgroundColor: AppColors.warning,
                  //     ontap: () {
                  //       SnackbarUtil.showWarning("Cảnh báo");
                  //     },
                  //     text: "click me!"),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
