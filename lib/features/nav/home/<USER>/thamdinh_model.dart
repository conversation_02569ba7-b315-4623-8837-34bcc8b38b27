class ThamDinhModel {
  final String soBienBan;
  final String tenCoSo;
  final String xepLoai;
  final String hinhThucThamDinhID;
  final String ngayThamDinh;
  final String mauHinhThucThamDinhID;
  final String mauXepLoai;

  ThamDinhModel({
    required this.soBienBan,
    required this.tenCoSo,
    required this.xepLoai,
    required this.hinhThucThamDinhID,
    required this.ngayThamDinh,
    required this.mauHinhThucThamDinhID,
    required this.mauXepLoai,
  });

  factory ThamDinhModel.fromJson(Map<String, dynamic> json) {
    return ThamDinhModel(
      soBienBan: json['SoBienBan'] ?? '',
      tenCoSo: json['TenCoSo'] ?? '',
      xepLoai: json['XepLoai'] ?? '',
      hinhThucThamDinhID: json['HinhThucThamDinhID'] ?? '',
      ngayThamDinh: json['NgayThamDinh'] ?? '',
      mauHinhThucThamDinhID: json['MauHinhThucThamDinhID'] ?? '',
      mauXepLoai: json['MauXepLoai'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'SoBienBan': soBienBan,
      'TenCoSo': tenCoSo,
      'XepLoai': xepLoai,
      'HinhThucThamDinhID': hinhThucThamDinhID,
      'NgayThamDinh': ngayThamDinh,
      'MauHinhThucThamDinhID': mauHinhThucThamDinhID,
      'MauXepLoai': mauXepLoai,
    };
  }
}
