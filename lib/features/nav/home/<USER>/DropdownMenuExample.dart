import 'package:flutter/material.dart';

class DropdownMenuExample extends StatefulWidget {
  const DropdownMenuExample({super.key});

  @override
  _DropdownMenuExampleState createState() => _DropdownMenuExampleState();
}

class _DropdownMenuExampleState extends State<DropdownMenuExample> {
  final List<String> menuItems = [
    "Tháng 1",
    "Tháng 2",
    "Tháng 3",
    "Tháng 4",
    "Tháng 5",
    "Tháng 6",
    "Tháng 7",
    "Tháng 8",
    "Tháng 9",
    "Tháng 10",
    "Tháng 11",
    "Tháng 12",
    "<PERSON>ăm",
    "Quý I",
    "Quý II",
    "Quý III",
    "Quý IV",
    "6 tháng đầu năm",
    "6 tháng cuối năm",
  ];

  String selectedMenu = "Tháng 1"; // Default selected item

  void fetchDataForSelectedMenu(String value) {
    // Logic to fetch data for the selected menu
    print("Fetching data for: $value");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("Dropdown Menu Example")),
      body: Center(
        child: DropdownButton<String>(
          value: selectedMenu,
          items: menuItems.map((item) {
            return DropdownMenuItem<String>(
              value: item,
              child: Text(item),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                selectedMenu = value;
              });
              fetchDataForSelectedMenu(value);
            }
          },
          underline: const SizedBox(), // Removes the underline
        ),
      ),
    );
  }
}
