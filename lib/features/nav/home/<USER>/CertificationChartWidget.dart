import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/features/nav/home/<USER>/RibbonWidget.dart';
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:gap/gap.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class CertificationChartWidget extends StatelessWidget {
  final double total;
  final List<double> values;
  final List<String> labels;
  final List<Color> colors;
  final String title;
  final List<String> menuItems;
  final String selectedMenu;
  final ValueChanged<String> onMenuChanged;
  final double sizeTitle = 15;
  final double sizeValues = 15;

  const CertificationChartWidget({
    super.key,
    required this.total,
    required this.values,
    required this.labels,
    required this.colors,
    required this.title,
    required this.menuItems,
    required this.selectedMenu,
    required this.onMenuChanged,
  });

  @override
  Widget build(BuildContext context) {
    final totalValue = values.fold(0.0, (sum, val) => sum + val);
    final List<PieChartSectionData> sections = _generateSections();

    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      elevation: 3,
      child: Padding(
        padding: EdgeInsets.only(left: 2.w, right: 3.w, bottom: 1.h),
        child: Stack(
          clipBehavior: Clip.antiAliasWithSaveLayer,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Gap(5.h),
                _buildChartContent(sections, totalValue),
              ],
            ),
            Positioned(
              top: -.8.h,
              left: 0,
              right: 0,
              child: _buildHeader(),
            ),
          ],
        ),
      ),
    );
  }

  /// Widget Header gồm Title + Dropdown Menu
  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            RibbonWidget(color: AppColors.primary),
            Gap(1.w),
            Text(
              title,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: sizeTitle.sp,
                color: AppColors.primary,
              ),
            ),
          ],
        ),
        DropdownButton<String>(
          menuMaxHeight: 30.h,
          value:
              menuItems.contains(selectedMenu) ? selectedMenu : menuItems.first,
          items: menuItems
              .map((item) => DropdownMenuItem<String>(
                  value: item,
                  child: Text(
                    item,
                    style: TextStyle(fontSize: 15.sp),
                  )))
              .toList(),
          onChanged: (value) => value != null ? onMenuChanged(value) : null,
          underline: const SizedBox(),
        ),
        // DropdownButton<String>(
        //   value: selectedMenu,
        //   menuMaxHeight: 50.h,
        //   items: menuItems
        //       .map((item) =>
        //           DropdownMenuItem<String>(value: item, child: Text(item)))
        //       .toList(),
        //   onChanged: (value) => value != null ? onMenuChanged(value) : null,
        //   underline: const SizedBox(),
        // ),
      ],
    );
  }

  /// Widget chứa Pie Chart + Danh sách thống kê
  Widget _buildChartContent(
      List<PieChartSectionData> sections, double totalValue) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildPieChart(sections),
        Gap(1.h),
        _buildStatistics(totalValue),
      ],
    );
  }

  /// Widget Pie Chart
  Widget _buildPieChart(List<PieChartSectionData> sections) {
    return Flexible(
      flex: 1,
      child: AspectRatio(
        aspectRatio: 1,
        child: PieChart(
          PieChartData(
            sections: sections,
            borderData: FlBorderData(show: false),
            centerSpaceRadius: 1,
          ),
        ),
      ),
    );
  }

  /// Widget danh sách thống kê + Tổng số lượng
  Widget _buildStatistics(double totalValue) {
    return Flexible(
      flex: 2,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTitle("Trong đó:"),
          Gap(1.h),
          ..._buildLegendItems(),
          Divider(color: AppColors.accentColor),
          _buildTotalRow(totalValue),
        ],
      ),
    );
  }

  /// Widget Tiêu đề danh sách thống kê
  Widget _buildTitle(String text) {
    return Text(
      text,
      style: TextStyle(
        fontWeight: FontWeight.w500,
        fontSize: sizeValues.sp,
        color: Colors.black87,
      ),
    );
  }

  /// Widget hiển thị tổng số lượng
  Widget _buildTotalRow(double totalValue) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _buildTitle("Tổng"),
        Text(
          totalValue.toStringAsFixed(0),
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: sizeValues.sp,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  /// Tạo danh sách Pie Chart Sections
  List<PieChartSectionData> _generateSections() {
    return List.generate(values.length, (index) {
      return PieChartSectionData(
        value: values[index],
        color: colors[index],
        radius: 50,
        title: '',
      );
    });
  }

  /// Xây dựng danh sách chú thích (Legend Items)
  List<Widget> _buildLegendItems() {
    return List.generate(labels.length, (index) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          children: [
            Container(
              width: 10,
              height: 10,
              decoration: BoxDecoration(
                color: colors[index],
                shape: BoxShape.circle,
              ),
            ),
            Gap(3.w),
            Text(
              labels[index],
              style:
                  TextStyle(fontSize: sizeValues.sp - 1, color: Colors.black87),
            ),
            const Spacer(),
            Text(
              values[index].toStringAsFixed(0),
              style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: sizeValues.sp - 1,
                  color: Colors.black87),
            ),
          ],
        ),
      );
    });
  }
}
