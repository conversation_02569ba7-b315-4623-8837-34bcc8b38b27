import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class BuildListGcn extends StatelessWidget {
  final String dncGCNValue;
  final String paATTPValue;
  final String csTDValue;
  final String viphamATTPValue;
  final String card1Name;
  final String card2Name;
  final String card3Name;
  final String card4Name;

  const BuildListGcn({
    required this.dncGCNValue,
    required this.paATTPValue,
    required this.csTDValue,
    required this.viphamATTPValue,
    required this.card1Name,
    required this.card2Name,
    required this.card3Name,
    required this.card4Name,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(5.0),
      child: Container(
        padding: const EdgeInsets.all(5),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(5),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            _buildGridRow([
              _buildGridItem(
                icon: Icons.description,
                title: card1Name,
                value: dncGCNValue,
                subtitle: "hồ sơ",
              ),
              _buildGridItem(
                icon: Icons.phone_android,
                title: card2Name,
                value: paATTPValue,
                subtitle: "trường hợp",
              ),
            ]),
            Divider(thickness: 1, color: AppColors.primary),
            _buildGridRow([
              _buildGridItem(
                icon: Icons.approval,
                title: card3Name,
                value: csTDValue,
                subtitle: "cơ sở",
              ),
              _buildGridItem(
                icon: Icons.warning_amber_rounded,
                title: card4Name,
                value: viphamATTPValue,
                subtitle: "trường hợp",
              ),
            ]),
          ],
        ),
      ),
    );
  }

  Widget _buildGridRow(List<Widget> items) {
    List<Widget> rowChildren = [];
    for (int i = 0; i < items.length; i++) {
      rowChildren.add(Expanded(child: items[i]));
      if (i < items.length - 1) {
        rowChildren
            .add(const VerticalDivider(thickness: 1, color: Colors.black54));
      }
    }
    return Row(
      children: rowChildren,
    );
  }

  Widget _buildGridItem({
    required IconData icon,
    required String title,
    required String value,
    required String subtitle,
  }) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Row(
          children: [
            Icon(icon, size: 24.sp, color: AppColors.primary),
            Gap(3.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 15.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                    ),
                  ),
                  Text.rich(
                    TextSpan(
                      text: value,
                      style: TextStyle(
                        fontSize: 17.sp,
                        fontWeight: FontWeight.w900,
                        color: Colors.black54,
                      ),
                      children: [
                        TextSpan(
                          text: " $subtitle",
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.normal,
                            color: Colors.black,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
