// ignore_for_file: unused_catch_stack

import 'package:attp_2024/core/configs/contanst/proc_constants.dart';
import 'package:attp_2024/core/data/api/services/proc/proc_service.dart';
import 'package:attp_2024/core/services/user_use_case.dart';
import 'package:attp_2024/features/nav/home/<USER>/thamdinh_model.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:intl/intl.dart';

class HomeService {
  final ProcService _procService = Get.find<ProcService>();
  final List<String> filterTypes = ['DAY', 'WEEK', 'MONTH', 'QUARTER', 'YEAR'];
  // Lấy thông tin người dùng một cách bất đồng bộ khi cần
  Future<String> _getDonViID() async {
    final userAccessModel = await UserUseCase.getUser();
    return userAccessModel?.donViID ?? ''; // G<PERSON> giá trị mặc định nếu null
  }

  Future<String> _getuserGroupCode() async {
    final userAccessModel = await UserUseCase.getUser();
    return userAccessModel?.userGroupCode ??
        ''; // Gán giá trị mặc định nếu null
  }

// Hàm lấy ngày đầu năm và cuối năm hiện tại
  Map<String, String> _getYearStartAndEndDates() {
    final now = DateTime.now();
    final firstDayOfYear = DateTime(now.year, 1, 1); // Ngày đầu năm
    final lastDayOfYear = DateTime(now.year, 12, 31); // Ngày cuối năm
    final dateFormat = DateFormat('yyyy-MM-dd'); // Định dạng ngày (yyyy-MM-dd)
    return {
      "TuNgay": dateFormat.format(firstDayOfYear),
      "DenNgay": dateFormat.format(lastDayOfYear),
    };
  }

  Map<String, String> _getDateRangeByFilter(String filterType) {
    final now = DateTime.now();
    DateTime fromDate, toDate;

    switch (filterType) {
      case "Hôm nay": // DAY
        fromDate = DateTime(now.year, now.month, now.day);
        toDate = fromDate;
        break;

      case "Tuần này": // WEEK
        fromDate = now.subtract(Duration(days: now.weekday - 1)); // Đầu tuần
        toDate = fromDate.add(const Duration(days: 6)); // Cuối tuần
        break;

      case "Tháng này": // MONTH
        fromDate = DateTime(now.year, now.month, 1);
        toDate =
            DateTime(now.year, now.month, _daysInMonth(now.year, now.month));
        break;

      case "Năm": // YEAR
        fromDate = DateTime(now.year, 1, 1);
        toDate = DateTime(now.year, 12, 31);
        break;

      case "Quý I": // QUARTER_1
        fromDate = DateTime(now.year, 1, 1);
        toDate = DateTime(now.year, 3, 31);
        break;

      case "Quý II": // QUARTER_2
        fromDate = DateTime(now.year, 4, 1);
        toDate = DateTime(now.year, 6, 30);
        break;

      case "Quý III": // QUARTER_3
        fromDate = DateTime(now.year, 7, 1);
        toDate = DateTime(now.year, 9, 30);
        break;

      case "Quý IV": // QUARTER_4
        fromDate = DateTime(now.year, 10, 1);
        toDate = DateTime(now.year, 12, 31);
        break;

      case "6 tháng đầu năm": // FIRST_HALF
        fromDate = DateTime(now.year, 1, 1);
        toDate = DateTime(now.year, 6, 30);
        break;

      case "6 tháng cuối năm": // SECOND_HALF
        fromDate = DateTime(now.year, 7, 1);
        toDate = DateTime(now.year, 12, 31);
        break;

      // ✅ Từng tháng cụ thể theo `filterOptions2`
      case "Tháng 1":
        fromDate = DateTime(now.year, 1, 1);
        toDate = DateTime(now.year, 1, 31);
        break;

      case "Tháng 2":
        fromDate = DateTime(now.year, 2, 1);
        toDate = DateTime(now.year, 2, _daysInMonth(now.year, 2));
        break;

      case "Tháng 3":
        fromDate = DateTime(now.year, 3, 1);
        toDate = DateTime(now.year, 3, 31);
        break;

      case "Tháng 4":
        fromDate = DateTime(now.year, 4, 1);
        toDate = DateTime(now.year, 4, 30);
        break;

      case "Tháng 5":
        fromDate = DateTime(now.year, 5, 1);
        toDate = DateTime(now.year, 5, 31);
        break;

      case "Tháng 6":
        fromDate = DateTime(now.year, 6, 1);
        toDate = DateTime(now.year, 6, 30);
        break;

      case "Tháng 7":
        fromDate = DateTime(now.year, 7, 1);
        toDate = DateTime(now.year, 7, 31);
        break;

      case "Tháng 8":
        fromDate = DateTime(now.year, 8, 1);
        toDate = DateTime(now.year, 8, 31);
        break;

      case "Tháng 9":
        fromDate = DateTime(now.year, 9, 1);
        toDate = DateTime(now.year, 9, 30);
        break;

      case "Tháng 10":
        fromDate = DateTime(now.year, 10, 1);
        toDate = DateTime(now.year, 10, 31);
        break;

      case "Tháng 11":
        fromDate = DateTime(now.year, 11, 1);
        toDate = DateTime(now.year, 11, 30);
        break;

      case "Tháng 12":
        fromDate = DateTime(now.year, 12, 1);
        toDate = DateTime(now.year, 12, 31);
        break;

      default:
        fromDate = DateTime(now.year, 1, 1);
        toDate = DateTime(now.year, 12, 31);
        break;
    }

    final dateFormat = DateFormat('yyyy-MM-dd');
    return {
      "TuNgay": dateFormat.format(fromDate),
      "DenNgay": dateFormat.format(toDate),
    };
  }

// ✅ Hàm tính số ngày trong tháng
  int _daysInMonth(int year, int month) {
    return DateTime(year, month + 1, 0).day;
  }
  // Map<String, String> _getDateRangeByFilter(String filterType) {
  //   final now = DateTime.now();
  //   DateTime fromDate, toDate;

  //   switch (filterType) {
  //     case "DAY": // Hôm nay
  //       fromDate = DateTime(now.year, now.month, now.day);
  //       toDate = fromDate;
  //       break;

  //     case "WEEK": // Tuần này
  //       fromDate = now.subtract(Duration(days: now.weekday - 1)); // Đầu tuần
  //       toDate = fromDate.add(const Duration(days: 6)); // Cuối tuần
  //       break;

  //     case "MONTH": // Tháng này
  //       fromDate = DateTime(now.year, now.month, 1);
  //       toDate =
  //           DateTime(now.year, now.month, _daysInMonth(now.year, now.month));
  //       break;

  //     case "QUARTER": // Quý này
  //       int currentQuarter = ((now.month - 1) ~/ 3) + 1;
  //       fromDate = DateTime(now.year, (currentQuarter - 1) * 3 + 1, 1);
  //       toDate = DateTime(now.year, currentQuarter * 3,
  //           _daysInMonth(now.year, currentQuarter * 3));
  //       break;

  //     case "YEAR": // Năm này
  //       fromDate = DateTime(now.year, 1, 1);
  //       toDate = DateTime(now.year, 12, 31);
  //       break;

  //     case "QUARTER_1": // Quý 1
  //       fromDate = DateTime(now.year, 1, 1);
  //       toDate = DateTime(now.year, 3, 31);
  //       break;

  //     case "QUARTER_2": // Quý 2
  //       fromDate = DateTime(now.year, 4, 1);
  //       toDate = DateTime(now.year, 6, 30);
  //       break;

  //     case "QUARTER_3": // Quý 3
  //       fromDate = DateTime(now.year, 7, 1);
  //       toDate = DateTime(now.year, 9, 30);
  //       break;

  //     case "QUARTER_4": // Quý 4
  //       fromDate = DateTime(now.year, 10, 1);
  //       toDate = DateTime(now.year, 12, 31);
  //       break;

  //     case "FIRST_HALF": // 6 tháng đầu năm
  //       fromDate = DateTime(now.year, 1, 1);
  //       toDate = DateTime(now.year, 6, 30);
  //       break;

  //     case "SECOND_HALF": // 6 tháng cuối năm
  //       fromDate = DateTime(now.year, 7, 1);
  //       toDate = DateTime(now.year, 12, 31);
  //       break;

  //     default:
  //       fromDate = DateTime(now.year, 1, 1);
  //       toDate = DateTime(now.year, 12, 31);
  //       break;
  //   }

  //   final dateFormat = DateFormat('yyyy-MM-dd');
  //   return {
  //     "TuNgay": dateFormat.format(fromDate),
  //     "DenNgay": dateFormat.format(toDate),
  //   };
  // }

  // int _daysInMonth(int year, int month) {
  //   return DateTime(year, month + 1, 0).day;
  // }
  // Map<String, String> _getDateRangeByFilter(String filterType) {
  //   final now = DateTime.now();
  //   DateTime fromDate, toDate;

  //   switch (filterType) {
  //     case "DAY": // Hôm nay
  //       fromDate = now;
  //       toDate = now;
  //       break;

  //     case "WEEK": // Tuần này
  //       fromDate = now.subtract(Duration(days: now.weekday - 1)); // Đầu tuần
  //       toDate = fromDate.add(const Duration(days: 6)); // Cuối tuần
  //       break;

  //     case "MONTH": // Tháng này
  //       fromDate = DateTime(now.year, now.month, 1);
  //       toDate = DateTime(now.year, now.month + 1, 0);
  //       break;

  //     case "QUARTER": // Quý này
  //       int currentQuarter = ((now.month - 1) ~/ 3) + 1;
  //       fromDate = DateTime(now.year, (currentQuarter - 1) * 3 + 1, 1);
  //       toDate = DateTime(now.year, currentQuarter * 3 + 1, 0);
  //       break;

  //     case "YEAR": // Năm này
  //       fromDate = DateTime(now.year, 1, 1);
  //       toDate = DateTime(now.year, 12, 31);
  //       break;

  //     case "QUARTER_1": // Quý 1
  //       fromDate = DateTime(now.year, 1, 1);
  //       toDate = DateTime(now.year, 3, 31);
  //       break;

  //     case "QUARTER_2": // Quý 2
  //       fromDate = DateTime(now.year, 4, 1);
  //       toDate = DateTime(now.year, 6, 30);
  //       break;

  //     case "QUARTER_3": // Quý 3
  //       fromDate = DateTime(now.year, 7, 1);
  //       toDate = DateTime(now.year, 9, 30);
  //       break;

  //     case "QUARTER_4": // Quý 4
  //       fromDate = DateTime(now.year, 10, 1);
  //       toDate = DateTime(now.year, 12, 31);
  //       break;

  //     case "FIRST_HALF": // 6 tháng đầu năm
  //       fromDate = DateTime(now.year, 1, 1);
  //       toDate = DateTime(now.year, 6, 30);
  //       break;

  //     case "SECOND_HALF": // 6 tháng cuối năm
  //       fromDate = DateTime(now.year, 7, 1);
  //       toDate = DateTime(now.year, 12, 31);
  //       break;

  //     default:
  //       fromDate = DateTime(now.year, 1, 1);
  //       toDate = DateTime(now.year, 12, 31);
  //       break;
  //   }

  //   final dateFormat = DateFormat('yyyy-MM-dd');
  //   return {
  //     "TuNgay": dateFormat.format(fromDate),
  //     "DenNgay": dateFormat.format(toDate),
  //   };
  // }

  Future<List<T>> fetchData<T>(
    String procedureName,
    List<Map<String, dynamic>> params,
    T Function(Map<String, dynamic>) fromJson,
  ) async {
    final response = await _procService.callProc(procedureName, params);
    return response.isNotEmpty
        ? response
            .map<T>((item) => fromJson(item as Map<String, dynamic>))
            .toList()
        : [];
  }

  Future<List<dynamic>> fetchDataCardTab2() async {
    final donViID = await _getDonViID();
    final userGroupCode = await _getuserGroupCode();
    String defaultID = '00000000-0000-0000-0000-000000000000';
    final dateRange =
        _getYearStartAndEndDates(); // Lấy ngày đầu và cuối năm hiện tại
    return fetchData(
      ProcConstants.thongKeSoLuongCardKyGCK,
      [
        {
          "Type": "guid",
          "Name": "DonViID",
          "Value": userGroupCode == "Admin" ? defaultID : donViID
        },
        {"Type": "datetime", "Name": "TuNgay", "Value": dateRange["TuNgay"]},
        {"Type": "datetime", "Name": "DenNgay", "Value": dateRange["DenNgay"]},
        {
          "Type": "datetime",
          "Name": "TuNgay_ThangTruoc",
          "Value": dateRange["TuNgay"]
        },
        {
          "Type": "datetime",
          "Name": "TuNgay_ThangTruoc",
          "Value": dateRange["DenNgay"]
        },
      ],
      (json) => json,
    );
  }

  Future<List<dynamic>> fetchDataCard() async {
    final donViID = await _getDonViID();
    final userGroupCode = await _getuserGroupCode();
    String defaultID = '00000000-0000-0000-0000-000000000000';
    final dateRange = _getYearStartAndEndDates();
    return fetchData(
      ProcConstants.thongKeSoLuongCard,
      [
        {
          "Type": "guid",
          "Name": "DonViID",
          "Value": userGroupCode == "Admin" ? defaultID : donViID
        },
        {"Type": "datetime", "Name": "TuNgay", "Value": dateRange["TuNgay"]},
        {"Type": "datetime", "Name": "DenNgay", "Value": dateRange["DenNgay"]},
        {
          "Type": "datetime",
          "Name": "TuNgay_ThangTruoc",
          "Value": dateRange["TuNgay"]
        },
        {
          "Type": "datetime",
          "Name": "TuNgay_ThangTruoc",
          "Value": dateRange["DenNgay"]
        },
      ],
      (json) => json,
    );
  }

  Future<List<dynamic>> fetchDataCharGCN(String filterType) async {
    try {
      final donViID = await _getDonViID();
      final dateRange = _getDateRangeByFilter(
          filterType); // 📌 Lấy khoảng thời gian theo filter
      final userGroupCode = await _getuserGroupCode();
      String defaultID = '00000000-0000-0000-0000-000000000000';
      return await fetchData(
        ProcConstants.charGCN,
        [
          {
            "Type": "guid",
            "Name": "DonViID",
            "Value": userGroupCode == "Admin" ? defaultID : donViID
          },
          {"Type": "datetime", "Name": "TuNgay", "Value": dateRange["TuNgay"]},
          {
            "Type": "datetime",
            "Name": "DenNgay",
            "Value": dateRange["DenNgay"]
          },
        ],
        (json) => (json),
      );
    } catch (e, stackTrace) {
      return [];
    }
  }

  Future<List<dynamic>> fetchDataCharGCK(String filterType) async {
    try {
      final donViID = await _getDonViID();
      final dateRange = _getDateRangeByFilter(
          filterType); // 📌 Lấy khoảng thời gian theo filter
      final userGroupCode = await _getuserGroupCode();
      String defaultID = '00000000-0000-0000-0000-000000000000';
      final body = [
        {
          "Type": "guid",
          "Name": "DonViID",
          "Value": userGroupCode == "Admin" ? defaultID : donViID
        },
        {"Type": "datetime", "Name": "TuNgay", "Value": dateRange["TuNgay"]},
        {"Type": "datetime", "Name": "DenNgay", "Value": dateRange["DenNgay"]}
      ];
      return await fetchData(
        ProcConstants.charKyGCK,
        [
          {
            "Type": "guid",
            "Name": "DonViID",
            "Value": userGroupCode == "Admin" ? defaultID : donViID
          },
          {"Type": "datetime", "Name": "TuNgay", "Value": dateRange["TuNgay"]},
          {"Type": "datetime", "Name": "DenNgay", "Value": dateRange["DenNgay"]}
        ],
        (json) => (json),
      );
    } catch (e, stackTrace) {
      return [];
    }
  }
  // Future<List<dynamic>> fetchDataCharGCN() async {
  //   try {
  //     final donViID = await _getDonViID();
  //     final dateRange = _getYearStartAndEndDates();
  //     return await fetchData(
  //       ProcConstants.charGCN,
  //       [
  //         {"Type": "guid", "Name": "DonViID", "Value": donViID},
  //         {"Type": "datetime", "Name": "TuNgay", "Value": dateRange["TuNgay"]},
  //         {
  //           "Type": "datetime",
  //           "Name": "DenNgay",
  //           "Value": dateRange["DenNgay"]
  //         },
  //       ],
  //       (json) => (json),
  //     );
  //   } catch (e, stackTrace) {
  //     return [];
  //   }
  // }

  // Future<List<dynamic>> fetchDataCharGCK() async {
  //   try {
  //     final donViID = await _getDonViID();
  //     final dateRange = _getYearStartAndEndDates();
  //     return await fetchData(
  //       ProcConstants.charKyGCK,
  //       [
  //         {"Type": "guid", "Name": "DonViID", "Value": donViID},
  //         {"Type": "datetime", "Name": "TuNgay", "Value": dateRange["TuNgay"]},
  //         {
  //           "Type": "datetime",
  //           "Name": "DenNgay",
  //           "Value": dateRange["DenNgay"]
  //         },
  //         {"Type": "string", "Name": "UserGroup", "Value": "TongHopTinh"}
  //       ],
  //       (json) => (json),
  //     );
  //   } catch (e, stackTrace) {
  //     // Log lỗi nếu cần
  //     return [];
  //   }
  // }

  Future<List<ThamDinhModel>> fetchDataDanhSachThamDinhATTP(
      String loai, String FilterType) async {
    try {
      final donViID = await _getDonViID();
      final userGroupCode = await _getuserGroupCode();
      String defaultID = '00000000-0000-0000-0000-000000000000';
      return await fetchData(
        ProcConstants.danhSachThamDinhATTP,
        [
          {"Type": "guid", "Name": "DonViID", "Value": userGroupCode == "Admin" ? defaultID : donViID},
          {
            "Type": "string",
            "Name": "FilterType",
            "Value": FilterType.isEmpty ? "WEEK" : FilterType
          },
          {"Type": "string", "Name": "Loai", "Value": loai},
        ],
        (json) => ThamDinhModel.fromJson(json),
      );
    } catch (e, stackTrace) {
      return [];
    }
  }
}
