import 'package:attp_2024/core/ui/widgets/chart/custom_pie_chart.dart';
import 'package:attp_2024/core/ui/widgets/customCachedImage/customCachedImage.dart';
import 'package:attp_2024/core/utils/color_utils.dart';
import 'package:attp_2024/features/main/presentation/controller/main_controller.dart';
import 'package:attp_2024/features/nav/home/<USER>/item_category_function_model.dart';
import 'package:attp_2024/features/nav/home/<USER>/CertificationChartWidget.dart';
import 'package:attp_2024/features/nav/home/<USER>/RibbonWidget.dart';
import 'package:attp_2024/features/tinTuc/presentation/page/tinTuc_detail.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/features/nav/home/<USER>/controllers/home_controller.dart';
import 'package:attp_2024/features/tinTuc/di/tinTuc_binding.dart';

class HomePage extends GetView<HomeController> {
  const HomePage({super.key});
  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Container(
        color: AppColors.primary,
        child: SafeArea(
          child: Scaffold(
            backgroundColor: AppColors.backgroundColorPrimary,
            body: Column(
              children: [
                _buildAppBar(),
                Expanded(
                  child: TabBarView(
                    children: [
                      SingleChildScrollView(
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Column(
                            children: [
                              Obx(
                                () => _BuildListGcn(
                                  card_1Name: "Đề nghị cấp GCN",
                                  card_2Name: "Phản ánh ATTP",
                                  card_3Name: "Thẩm định",
                                  card_4Name: "Vi phạm ATTP",
                                  dncGCNValue: controller.dncGCN.value,
                                  paATTPValue: controller.paATTP.value,
                                  csTDValue: controller.csTD.value,
                                  viphamATTPValue: controller.viphamATTP.value,
                                ),
                              ),
                              Obx(
                                () => CertificationChartWidget(
                                  title: "Cấp giấy chứng nhận",
                                  total: controller.certificationValues
                                      .map((e) => e.toDouble())
                                      .reduce(
                                          (value, element) => value + element),
                                  values: controller.certificationValues
                                      .map((e) => e.toDouble())
                                      .toList(),
                                  labels: const [
                                    "Cấp mới",
                                    "Cấp lại",
                                    "Thu hồi",
                                    "Hết hạn"
                                  ],
                                  colors: const [
                                    Colors.blue,
                                    Colors.green,
                                    Colors.purple,
                                    Colors.red
                                  ],
                                  menuItems: controller.filterOptions2
                                      .map((e) => e['label']!)
                                      .toList(),
                                  selectedMenu:
                                      controller.currentFilterChar_tab1.value,
                                  onMenuChanged: (value) {
                                    controller.updateFilterChar_Tab1(
                                        value); // Update filter in controller
                                  },
                                ),
                              ),
                              _BuildListCard(),
                              _BuildCategoryFunction(
                                  listItemCategory:
                                      controller.listCategoryModel),
                              Gap(1.h),
                              const _BuildListNews(),
                              Gap(10.h),
                            ],
                          ),
                        ),
                      ),
                      SingleChildScrollView(
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Column(
                            children: [
                              Obx(
                                () => _BuildListGcn(
                                  card_1Name: "Ký cam kết ATTP",
                                  card_2Name: "Phản ánh ATTP",
                                  card_3Name: "Kiểm tra ATTP",
                                  card_4Name: "Vi phạm ATTP",
                                  dncGCNValue: controller.dncGCN2.value,
                                  paATTPValue: controller.paATTP2.value,
                                  csTDValue: controller.csTD2.value,
                                  viphamATTPValue: controller.viphamATTP2.value,
                                ),
                              ),
                              Obx(
                                () => CertificationChartWidget(
                                  title: "Ký giấy cam kết",
                                  total: controller.certificationValues1
                                      .map((e) => e.toDouble())
                                      .reduce(
                                          (value, element) => value + element),
                                  values: controller.certificationValues1
                                      .map((e) => e.toDouble())
                                      .toList(),
                                  labels: const ["Cấp mới", "Hết hạn"],
                                  colors: const [
                                    Colors.blue,
                                    Colors.green,
                                  ],
                                  menuItems: controller.filterOptions2
                                      .map((e) => e['label']!)
                                      .toList(),
                                  selectedMenu:
                                      controller.currentFilterChar_tab2.value,
                                  onMenuChanged: (value) {
                                    controller.updateFilterChar_Tab2(
                                        value); // Update filter in controller
                                  },
                                ),
                              ),
                              _BuildListCardTab2(),
                              _BuildCategoryFunction(
                                  listItemCategory:
                                      controller.listCategoryModel),
                              Gap(1.h),
                              const _BuildListNews(),
                              Gap(10.h),
                              Gap(10.h),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  _buildListTag() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        color: Colors.white,
        boxShadow: const [],
      ),
      child: SizedBox(
        width: 100.w,
        child: Wrap(
          runSpacing: 8,
          alignment: WrapAlignment.spaceBetween,
          children: [
            _buildTagItem(
                title: "Hồ sơ đề nghị cấp",
                // icon: CupertinoIcons.person_circle_fill,
                icon: AppImageString.iconHome_1,
                qualityPerson: 0,
                color: AppColors.accentColor),
            _buildTagItem(
                title: "Cơ sở được thẩm định",
                // icon: CupertinoIcons.person_circle_fill,
                icon: AppImageString.iconHome_2,
                qualityPerson: 0,
                color: Colors.blue.shade800),
            _buildTagItem(
              title: "Trường hợp phản ánh",
              // icon: CupertinoIcons.person_circle_fill,
              icon: AppImageString.iconHome_3,
              qualityPerson: 0,
              color: Colors.purple.shade300,
            ),
            _buildTagItem(
              title: "Vi phạm bị phát hiện",
              // icon: CupertinoIcons.person_circle_fill,
              icon: AppImageString.iconHome_4,
              qualityPerson: 0,
              color: AppColors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Container _buildTagItem({
    required String title,
    required String icon,
    required int qualityPerson,
    Color color = AppColors.blue,
    bool fullRow = false,
  }) {
    return Container(
      width: !fullRow ? 50.w - 20 : Get.width,
      padding: const EdgeInsets.symmetric(vertical: 22, horizontal: 18),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        gradient: LinearGradient(
          // ignore: deprecated_member_use
          colors: [color.withOpacity(1), color.withOpacity(0.5)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            // ignore: deprecated_member_use
            color: color.withOpacity(0.5),
            blurRadius: 8,
            offset: const Offset(2, 4),
          )
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Image.asset(
                icon,
                width: 40,
                height: 40,
              ),
              const Spacer(),
              TextWidget(
                text: qualityPerson.toString(),
                size: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.white,
              ),
            ],
          ),
          const Gap(10),
          Center(
            child: TextWidget(
              text: title,
              textAlign: TextAlign.center,
              color: AppColors.white,
              fontWeight: FontWeight.w500,
              size: 14.sp,
            ),
          ),
        ],
      ),
    );
  }

  Wrap _buildListCategory() {
    return Wrap(
      spacing: 5,
      runSpacing: 5,
      alignment: WrapAlignment.start,
      children: [
        _buildItemCategory(
          title: "Giấy chứng nhận ATTP",
          icon: CupertinoIcons.person_circle_fill,
          color: AppColors.primary,
          isSelected: true,
        ),
        _buildItemCategory(
          title: "GCK đảm bảo ATTP",
          icon: CupertinoIcons.building_2_fill,
          color: AppColors.gray2,
          isSelected: false,
        ),
      ],
    );
  }

  Widget _buildItemCategory({
    required String title,
    required IconData icon,
    required Color color,
    required bool isSelected,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 5),
      decoration: BoxDecoration(
        color: isSelected ? color : Colors.white, // Dynamic background
        borderRadius:
            BorderRadius.circular(20), // Rounded corners for pill effect
        border: Border.all(
          color: isSelected
              ? Colors.transparent
              : AppColors.gray2, // Border for non-selected
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: isSelected ? Colors.white : AppColors.gray2,
          ),
          const SizedBox(width: 8),
          TextWidget(
            text: title,
            size: AppDimens.textSize14,
            fontWeight: FontWeight.w600,
            color: isSelected ? Colors.white : AppColors.gray2, // Text color
          ),
        ],
      ),
    );
  }

  TextField _buildSearchField() {
    return TextField(
      decoration: InputDecoration(
        contentPadding: const EdgeInsets.symmetric(vertical: 0),
        hintText: "Phường 1, Thành phố Bạc Liêu",
        hintStyle: const TextStyle(
            fontSize: AppDimens.textSize14,
            fontWeight: FontWeight.w400,
            color: AppColors.grey),
        suffixIcon: const Icon(
          Icons.arrow_forward_ios_rounded,
          color: AppColors.lightThemePrimaryText,
          size: AppDimens.textSize18,
        ),
        prefixIcon: const Icon(
          CupertinoIcons.search,
          color: AppColors.lightThemePrimaryText,
          size: AppDimens.textSize20,
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: const BorderSide(width: 1, color: AppColors.gray1),
          borderRadius: BorderRadius.circular(10),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(width: 1, color: AppColors.primary),
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Column(
      children: [
        Container(
          color: AppColors.primary,
          padding: EdgeInsets.symmetric(horizontal: 3.w),
          child: Column(
            children: [
              Obx(() {
                String path = controller.avatarPathOld.value;
                String newPath = path.replaceFirst('~', '');

                return Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      spacing: 3.w,
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(50.0),
                          child: CustomCachedImage(
                            width: 11.w,
                            height: 5.h,
                            imageUrl:
                                '${controller.userAccessModel.value.siteURL}/$newPath',
                            defaultImage: AppImageString.defaultProfile,
                          ),
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TextWidget(
                              text: controller.name.value,
                              size: 16.sp,
                              color: AppColors.white,
                              fontWeight: FontWeight.w700,
                            ),
                            TextWidget(
                              text: controller.userAccessModel.value.TenDonVi,
                              color: AppColors.white,
                              size: AppDimens.defaultText,
                              wordLimit: 8,
                            ),
                          ],
                        ),
                      ],
                    ),
                    Icon(
                      CupertinoIcons.bell,
                      color: AppColors.white,
                      size: 21.sp,
                    ),
                  ],
                );
              }),
              // Row(
              //   children: [
              //     Obx(() {
              //       String path = controller.userAccessModel.value.anhDaiDien;
              //       String newPath = path.replaceFirst('~', '');
              //       return ClipRRect(
              //         borderRadius: BorderRadius.circular(50.0),
              //         child: CustomCachedImage(
              //           width: 11.w,
              //           height: 5.h,
              //           imageUrl:
              //               '${controller.userAccessModel.value.siteURL}/$newPath',
              //           defaultImage: AppImageString.defaultProfile,
              //         ),
              //       );
              //     }),
              //     Gap(3.w),
              //     Obx(() => Column(
              //           crossAxisAlignment: CrossAxisAlignment.start,
              //           children: [
              //             TextWidget(
              //               text: controller.userAccessModel.value.hoTen,
              //               size: 18.sp,
              //               color: AppColors.white,
              //               fontWeight: FontWeight.w700,
              //             ),
              //             TextWidget(
              //               text: controller.userAccessModel.value.TenDonVi,
              //               color: AppColors.white,
              //               size: AppDimens.defaultText,
              //             ),
              //           ],
              //         )),
              //     const Spacer(),
              //     Icon(
              //       CupertinoIcons.bell,
              //       color: AppColors.white,
              //       size: AppDimens.textSize22.sp,
              //     ),
              //   ],
              // ),
              TabBar(
                isScrollable: true,
                tabAlignment: TabAlignment.start,
                labelColor: AppColors.white,
                unselectedLabelColor: AppColors.white,
                indicator: const UnderlineTabIndicator(
                  borderSide: BorderSide(width: 2.0, color: Colors.white),
                ),
                tabs: [
                  Tab(
                    child: Text(
                      "Cấp giấy chứng nhận",
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: AppDimens.defaultText,
                      ),
                    ),
                  ),
                  Tab(
                    child: Text(
                      "Ký giấy cam kết",
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: AppDimens.defaultText,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget HomePageTabs() {
    return DefaultTabController(
      length: 2, // Số lượng tabs
      child: Column(
        children: [
          // TabBar
          TabBar(
            labelColor: AppColors.primary, // Màu tab đang chọn
            unselectedLabelColor: AppColors.gray2, // Màu tab chưa chọn
            indicatorColor: AppColors.primary, // Màu gạch chân
            tabs: const [
              Tab(
                child: Text(
                  "Hồ sơ đề nghị cấp GCN",
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              Tab(
                child: Text(
                  "Hồ sơ khác",
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
          // Nội dung của các tab
          const Expanded(
            child: TabBarView(
              children: [
                Center(
                  child: Text("Nội dung Tab 1 - Hồ sơ đề nghị cấp GCN"),
                ),
                Center(
                  child: Text("Nội dung Tab 2 - Hồ sơ khác"),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _BuildCapGcn() {
    return Padding(
      padding: const EdgeInsets.all(5.0),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(5),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: const CustomPieChart(
          values: [30, 40, 20, 10],
          labels: ['Category A', 'Category B', 'Category C', 'Category D'],
          title: 'Pie Chart Example',
          sumary: 100,
          // Total value
          colors: [Colors.blue, Colors.green, Colors.orange, Colors.red],
          // Custom colors
          aspectRatio: 1.5,
          centerSpaceRadius: 40,
          sectionRadius: 30,
          sectionSpace: 2,
          legendSpacing: 12.0,
          fontSize: 12.0,
          legendBoxSize: 16.0,
          legendBorderRadius: 4.0,
          horizontalSpace: 20,
          verticalSpace: 20,
        ),
      ),
    );
  }

  // ignore: non_constant_identifier_names
  Widget _BuildListGcn({
    required String dncGCNValue,
    required String paATTPValue,
    required String csTDValue,
    required String viphamATTPValue,
    required String card_1Name,
    required String card_2Name,
    required String card_3Name,
    required String card_4Name,
  }) {
    return Padding(
      padding: const EdgeInsets.all(5.0),
      child: Container(
        padding: const EdgeInsets.all(5),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(5),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            _buildGridRow([
              _buildGridItem(
                icon: Icons.description,
                title: card_1Name,
                value: dncGCNValue,
                subtitle: "hồ sơ",
              ),
              _buildGridItem(
                icon: Icons.phone_android,
                title: card_2Name,
                value: paATTPValue,
                subtitle: "trường hợp",
              ),
            ]),
            Divider(thickness: 1, color: AppColors.primary),
            _buildGridRow([
              _buildGridItem(
                icon: Icons.approval,
                title: card_3Name,
                value: csTDValue,
                subtitle: "cơ sở",
              ),
              _buildGridItem(
                icon: Icons.warning_amber_rounded,
                title: card_4Name,
                value: viphamATTPValue,
                subtitle: "trường hợp",
              ),
            ]),
          ],
        ),
      ),
    );
  }

  Widget _buildGridRow(List<Widget> items) {
    List<Widget> rowChildren = [];
    for (int i = 0; i < items.length; i++) {
      rowChildren.add(Expanded(child: items[i]));
      if (i < items.length - 1) {
        rowChildren
            .add(VerticalDivider(thickness: 1, color: AppColors.primary));
      }
    }
    return Row(
      children: rowChildren,
    );
  }

  Widget _buildGridItem({
    required IconData icon,
    required String title,
    required String value,
    required String subtitle,
  }) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Row(
          children: [
            Icon(icon, size: 25.sp, color: AppColors.primary),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: AppDimens.defaultText,
                      fontWeight: FontWeight.w400,
                      color: AppColors.black,
                    ),
                  ),
                  Text.rich(
                    TextSpan(
                      text: value,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w900,
                        color: AppColors.primary,
                      ),
                      children: [
                        TextSpan(
                          text: " $subtitle",
                          style: TextStyle(
                            fontSize: AppDimens.defaultText,
                            fontWeight: FontWeight.normal,
                            color: AppColors.black,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _BuildListCard() {
    return Padding(
      padding: const EdgeInsets.all(1.0),
      child: Stack(
        clipBehavior: Clip.antiAliasWithSaveLayer,
        children: [
          Card(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            elevation: 3,
            child: Padding(
              padding: EdgeInsets.only(left: 3.w, right: 3.w, bottom: 0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Gap(5.w),
                      Text(
                        "Kế hoạch kiểm tra, thẩm định",
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: AppDimens.defaultText,
                          color: AppColors.primary,
                        ),
                      ),
                      const Spacer(),
                      Obx(() {
                        return DropdownButton<String>(
                          value: controller.currentFilteCh_tb1.value,
                          items: controller.filterOptions.map((option) {
                            return DropdownMenuItem<String>(
                              value: option['label'],
                              child: Text(
                                option['label']!,
                                style:
                                    TextStyle(fontSize: AppDimens.defaultText),
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              controller.updateFilterTab1(value);
                            }
                          },
                          underline: const SizedBox(),
                        );
                      }),
                    ],
                  ),
                  // List Items
                  Obx(() {
                    if (controller.danhSachThamDinh.isEmpty) {
                      return Center(
                        child: Lottie.asset(
                          AppImageString.iconNotFoundLotte,
                          height: 15.h,
                          fit: BoxFit.cover,
                        ),
                      );
                    }

                    return ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: controller.danhSachThamDinh.length,
                      separatorBuilder: (context, index) => const Gap(8),
                      itemBuilder: (context, index) {
                        final item = controller.danhSachThamDinh[index];
                        return _buildTaskItem(
                            date: item.ngayThamDinh,
                            code: item.soBienBan,
                            company: item.tenCoSo,
                            status: item.xepLoai,
                            color: item.mauXepLoai);
                      },
                    );
                  }),
                ],
              ),
            ),
          ),
          // Ribbon dán sát mép trái của Card
          Positioned(
            top: .5.h, // Điều chỉnh cho ribbon bám sát mép trên của card
            left: 3.w, // Điều chỉnh để bám sát mép trái
            child: RibbonWidget(
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _BuildListCardTab2() {
    return Padding(
      padding: const EdgeInsets.all(1.0),
      child: Stack(
        clipBehavior: Clip
            .antiAliasWithSaveLayer, // Để Ribbon có thể hiển thị ra ngoài Stack
        children: [
          Card(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            elevation: 3,
            child: Padding(
              padding: const EdgeInsets.only(
                  left: 12, right: 12, bottom: 10, top: 0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Gap(5.w), // Để tạo khoảng trống cho Ribbon
                      Text(
                        "Kế hoạch kiểm tra, thẩm định",
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: AppDimens.defaultText,
                            color: AppColors.primary),
                      ),
                      Gap(2.w),
                      Expanded(
                        child: Obx(() {
                          final filterOptions =
                              controller.filterOptions.toSet().toList();
                          final currentValue = filterOptions.any((option) =>
                                  option['label'] ==
                                  controller.currentFilteCh_tb2.value)
                              ? controller.currentFilteCh_tb2.value
                              : filterOptions.first['label'];

                          return DropdownButton<String>(
                            value: currentValue,
                            items: filterOptions.map((option) {
                              return DropdownMenuItem<String>(
                                value: option['label'],
                                child: Text(
                                  option['label']!,
                                  style: TextStyle(
                                      fontSize: AppDimens.defaultText),
                                ),
                              );
                            }).toList(),
                            onChanged: (value) {
                              if (value != null) {
                                controller.updateFilterTab2(value);
                              }
                            },
                            underline: const SizedBox(),
                            isExpanded: true,
                          );
                        }),
                      ),
                    ],
                  ),
                  const Gap(10),
                  // List Items
                  Obx(() {
                    if (controller.danhSachThamDinhTab2.isEmpty) {
                      return Center(
                        child: Lottie.asset(
                          AppImageString.iconNotFoundLotte,
                          height: 15.h,
                          fit: BoxFit.cover,
                        ),
                      );
                    }

                    return ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: controller.danhSachThamDinhTab2.length,
                      separatorBuilder: (context, index) => const Gap(8),
                      itemBuilder: (context, index) {
                        final item = controller.danhSachThamDinhTab2[index];
                        return _buildTaskItem(
                            date: item.ngayThamDinh,
                            code: item.soBienBan,
                            company: item.tenCoSo,
                            status: item.xepLoai,
                            color: item.mauXepLoai);
                      },
                    );
                  }),
                ],
              ),
            ),
          ),
          // Ribbon dán sát mép trái của Card
          Positioned(
            top: .5.h, // Đưa Ribbon lên sát mép trên của Card
            left: 3.w, // Đưa Ribbon sát mép trái
            child: RibbonWidget(
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTaskItem(
      {required String date,
      required String code,
      required String company,
      required String status,
      required String color}) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 5.w,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 4,
              children: [
                Text(
                  date,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 15.sp,
                    color: Colors.black87,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: hexToColor(color),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    status,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: AppDimens.textSize12,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            // Company Info
            Expanded(
              child: Column(
                spacing: 4,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    code,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14.sp,
                      color: Colors.black87,
                    ),
                  ),
                  Text(
                    company,
                    style: const TextStyle(
                      fontSize: 13,
                      color: Colors.black54,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

_buildHeaderListNews() {
  return Row(
    children: [
      TextWidget(
        text: "Tin tức nổi bật",
        fontWeight: FontWeight.w700,
        size: AppDimens.defaultText,
      ),
      const Spacer(),
      InkWell(
        onTap: () {
          Get.find<MainController>().onChangeItemBottomBar(1);
        },
        child: Row(
          children: [
            Text(
              "Xem tất cả",
              style: TextStyle(
                fontSize: AppDimens.defaultText,
                color: AppColors.primary,
                decorationColor: AppColors.primary,
                decoration: TextDecoration.combine([
                  TextDecoration.underline,
                ]),
              ),
            ),
            Icon(
              Icons.chevron_right,
              size: AppDimens.defaultText,
              color: AppColors.primary,
            ),
          ],
        ),
      ),
    ],
  );
}

Widget _buildItemCategoryFunction(
    {required String title,
    required String iconsUrl,
    required String toPagePath}) {
  return InkWell(
    onTap: () {
      if (toPagePath != "") Get.toNamed(toPagePath);
    },
    child: Column(
      children: [
        Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                      color: AppColors.black.withOpacity(.15),
                      spreadRadius: 1,
                      blurRadius: 2,
                      offset: const Offset(0, 2))
                ]),
            width: 15.w,
            height: 7.h,
            child: Image.asset(iconsUrl)),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 5),
          child: TextWidget(
            textAlign: TextAlign.center,
            text: title,
            size: AppDimens.subText,
            maxLines: 2,
          ),
        )
      ],
    ),
  );
}

_buildHeaderCategoryFunction() {
  return Row(
    children: [
      RibbonWidget(
        color: AppColors.primary,
      ),
      Gap(
        1.w,
      ),
      TextWidget(
        text: "Tiện ích",
        size: AppDimens.defaultText,
        fontWeight: FontWeight.w600,
        color: AppColors.primary,
      ),
    ],
  );
}

class _BuildCategoryFunction extends StatelessWidget {
  final List<ItemCategoryFunctionModel> listItemCategory;

  const _BuildCategoryFunction({required this.listItemCategory});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(5.0),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 2.w),
        decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                  // ignore: deprecated_member_use
                  color: AppColors.black.withOpacity(.15),
                  spreadRadius: 1,
                  blurRadius: 2,
                  offset: const Offset(0, 2))
            ]),
        child: Column(
          children: [
            _buildHeaderCategoryFunction(),
            Container(
              padding: EdgeInsets.symmetric(
                vertical: 2.h,
              ),
              width: 100.w,
              child: SizedBox(
                height: 26.h,
                child: GridView.count(
                    physics: const NeverScrollableScrollPhysics(),
                    crossAxisCount: 3,
                    mainAxisSpacing: 0,
                    children: listItemCategory.map(
                      (items) {
                        return _buildItemCategoryFunction(
                            title: items.title,
                            iconsUrl: items.iconUrl,
                            toPagePath: items.toPagePath);
                      },
                    ).toList()),
              ),
            )
          ],
        ),
      ),
    );
  }
}

class _BuildListNews extends StatelessWidget {
  const _BuildListNews();

  @override
  Widget build(BuildContext context) {
    final HomeController controller = Get.find<HomeController>();
    return Padding(
      padding: const EdgeInsets.all(5.0),
      child: Column(
        children: [
          _buildHeaderListNews(),
          Gap(1.h),
          SizedBox(
            height: 18.h,
            child: Obx(() {
              if (controller.newsList.isEmpty) {
                return Center(
                  child: Text(
                    "Không có tin tức nào",
                    style: TextStyle(
                        fontSize: AppDimens.defaultText, color: Colors.grey),
                  ),
                );
              }
              return ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: controller.newsList.length,
                itemBuilder: (context, index) {
                  final news = controller.newsList[index];
                  return GestureDetector(
                    onTap: () {
                      Get.to(
                        binding: TinTucBinding(),
                        TinTucDetailPage(
                          news: news,
                        ),
                      );
                    },
                    child: Container(
                      width: 60.w,
                      margin: const EdgeInsets.only(right: 15),
                      child: Stack(
                        alignment: Alignment.bottomLeft,
                        children: [
                          // Ảnh nền
                          Obx(() {
                            return ClipRRect(
                              borderRadius: BorderRadius.circular(10),
                              child: CustomCachedImage(
                                  width: double.infinity,
                                  imageUrl:
                                      "${controller.userAccessModel.value.siteURL}${news.HinhAnh}",
                                  defaultImage: AppImageString.imageNotFount,
                                  fit: BoxFit.fill),
                            );
                          }),
                          // Lớp phủ mờ nền phía dưới
                          Positioned.fill(
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                gradient: LinearGradient(
                                  begin: Alignment.bottomCenter,
                                  end: Alignment.topCenter,
                                  colors: [
                                    Colors.black.withOpacity(0.6),
                                    Colors.transparent,
                                  ],
                                ),
                              ),
                            ),
                          ),
                          Positioned(
                            bottom: 10,
                            left: 10,
                            right: 10,
                            child: Text(
                              news.TieuDe ?? "Không có tiêu đề",
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                fontSize: AppDimens.subText,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                shadows: const [
                                  Shadow(
                                    blurRadius: 4,
                                    color: Colors.black,
                                    offset: Offset(2, 2),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );
            }),
          ),
        ],
      ),
    );
  }
}
