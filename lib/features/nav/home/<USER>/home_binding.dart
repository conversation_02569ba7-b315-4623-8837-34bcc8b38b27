import 'package:get/get.dart';
import 'package:attp_2024/core/data/api/configs/dio_configs.dart';
import 'package:attp_2024/core/data/api/services/auth/auth_service.dart';
import 'package:attp_2024/core/data/api/services/proc/proc_service.dart';
import 'package:attp_2024/features/nav/home/<USER>/controllers/home_controller.dart';
import 'package:attp_2024/features/tinTuc/presentation/controller/tinTuc_controller.dart';

class HomeBinding extends Bindings {
  @override
  void dependencies() async {
    Get.putAsync(() async => DioService().init());
    Get.lazyPut(() => AuthService(Get.find<DioService>()));
    Get.lazyPut(() => TinTucController());
    Get.lazyPut(() => HomeController());
    Get.lazyPut(() => ProcService(Get.find<DioService>()));
  }
}
