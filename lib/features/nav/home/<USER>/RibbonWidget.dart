import 'package:flutter/material.dart';

class RibbonWidget extends StatelessWidget {
  final Color color;
  final double width;
  final double height;

  const RibbonWidget({
    Key? key,
    this.color = Colors.green,
    this.width = 20,
    this.height = 38,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ClipPath(
      clipper: RibbonClipper(),
      child: Container(
        width: width,
        height: height,
        color: color,
      ),
    );
  }
}

class RibbonClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final Path path = Path();
    path.lineTo(0, size.height);
    path.lineTo(size.width / 2, size.height - 10);
    path.lineTo(size.width, size.height);
    path.lineTo(size.width, 0);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) {
    return false;
  }
}
