import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/utils/color_utils.dart';
import 'package:attp_2024/features/nav/home/<USER>/RibbonWidget.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class ListCardWidget extends StatelessWidget {
  final List<dynamic> danhSachThamDinh;
  final List<Map<String, String>> filterOptions;
  final String currentFilter;
  final void Function(String) onFilterChanged;

  const ListCardWidget({
    required this.danhSachThamDinh,
    required this.filterOptions,
    required this.currentFilter,
    required this.onFilterChanged,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(1.0),
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        elevation: 3,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      RibbonWidget(color: AppColors.primary),
                      const Gap(8),
                      Text(
                        "Kế hoạch kiểm tra, thẩm định",
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: AppColors.primary,
                        ),
                      ),
                    ],
                  ),
                  DropdownButton<String>(
                    value: currentFilter,
                    items: filterOptions.map((option) {
                      return DropdownMenuItem<String>(
                        value: option['label'],
                        child: Text(option['label']!),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) onFilterChanged(value);
                    },
                    underline: const SizedBox(),
                  ),
                ],
              ),
              const Gap(10),
              // List Items
              danhSachThamDinh.isEmpty
                  ? const Center(
                      child: Text(
                        "Không có dữ liệu thẩm định",
                        style: TextStyle(color: Colors.grey, fontSize: 16),
                      ),
                    )
                  : ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: danhSachThamDinh.length,
                      separatorBuilder: (context, index) => const Gap(8),
                      itemBuilder: (context, index) {
                        final item = danhSachThamDinh[index];
                        return _buildTaskItem(
                          date: item['ngayThamDinh'],
                          code: item['soBienBan'],
                          company: item['tenCoSo'],
                          status: item['xepLoai'],
                          color: item['mauXepLoai'],
                        );
                      },
                    ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTaskItem({
    required String date,
    required String code,
    required String company,
    required String status,
    required String color,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  date,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
                const Gap(4),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: hexToColor(color),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    status,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: AppDimens.textSize12,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const Gap(12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    code,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: Colors.black87,
                    ),
                  ),
                  const Gap(4),
                  Text(
                    company,
                    style: const TextStyle(
                      fontSize: 13,
                      color: Colors.black54,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
