{"buildFiles": ["D:\\flutter\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\NTS\\git-lab\\ATTP_Flutter\\android\\app\\.cxx\\Debug\\4t4x213z\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\NTS\\git-lab\\ATTP_Flutter\\android\\app\\.cxx\\Debug\\4t4x213z\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}